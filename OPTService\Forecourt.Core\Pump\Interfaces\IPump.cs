﻿using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Enums;
using Forecourt.Core.Pump.Models;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Hydra.Messages.Opt.Models;
using System;
using System.Collections.Generic;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;

namespace Forecourt.Core.Pump.Interfaces
{
    /// <summary>
    /// Any and all capabilities of a Pump
    /// </summary>
    public interface IPump
    {
        IOpt Opt { get; }
        byte Number { get; }
        string Tid { get; }

        PumpState LastPumpState { get; set; }
        HydraPosPumpState State { get; }

        // If the pump is closed, pump state is Closed,
        // if the OPT is in POD mode and the pump is in Kiosk mode, pump state is KioskOnly
        // otherwise pump state is Open.
        PumpStateType PumpState { get; }
        SecAuthState SecAuthState { get; }
        DateTime SecAuthExpiryTime { get; }
        bool HasSecAuthRequestTimedOut { get; }
        DateTime ReserveExpiryTime { get; }
        bool HasReserveTimedOut { get; }
        bool IsSecAuthRequested { get; }
        bool IsSecAuthApproved { get; }
        bool IsDelivering { get; }
        bool IsDelivered { get; }
        bool HasPayment { get; }
        bool InUse { get; }
        bool InUseByOpt { get; }
        bool IsMobile { get; }
        bool PendingIsMobile { get; }
        bool KioskUse { get; }
        bool DefaultKioskUse { get; }
        bool Mixed { get; }
        bool OutsideOnly { get; }
        bool DefaultOutsideOnly { get; }
        bool HasOptPayment { get; }
        bool IsNozzleUp { get; }
        bool PumpIsClosed { get; }
        bool OptUse { get; }
        string CardNumber { get; }
        string CardProductName { get; }
        uint ClearedAmount { get; }
        bool ThirdPartyWait { get; }
        bool ClosePending { get; }
        bool ModePending { get; }
        byte DeliveredHose { get; }
        ushort DeliveredPpu { get; }
        bool CanAddPayment { get; }
        bool CanCancelPayment { get; }
        bool CanClearPayment { get; }
        bool CanInsertCard { get; }
        bool CanSetKioskUse { get; }
        uint AuthorisedAmount { get; }
        bool IsPaid { get; }
        bool HasKioskPayment { get; }
        bool MaxFillOverrideForFuelCards { get; }
        bool MaxFillOverrideForPaymentCards { get; }
        IList<byte> AllGrades { get; }


        /// <summary>
        /// Add an approved payment to a pump.
        /// The pump will be flagged as having a payment, and the payment timeout will be started.
        /// If the pump is flagged as having a card inserted, this is cleared.
        /// If the Third Party Wait flag passed in is set, the pump is marked as needing Third Party POS authorisation.
        /// </summary>
        /// <param name="amount">Maximum amount approved.</param>
        /// <param name="thirdPartyWait">True if Third Party POS authorisation is required.</param>
        /// <param name="allowedGrades">List of allowed grades (by card restriction), or null if there are no grade restrictions.</param>
        /// <param name="disallowedGrades">List of disallowed grades (fuel cards only), or null if there are no fuel cards only grades.</param>
        /// <param name="reference">Current logging reference</param>
        void AddPayment(uint amount, bool thirdPartyWait = false, IList<byte> allowedGrades = null, IList<byte> disallowedGrades = null, string reference = null);

        /// <summary>
        /// Cancel an approved payment, or clear a card inserted notification.
        /// If the pump is flagged as having a payment, this is cleared.
        /// If the pump is flagged as having a card inserted, this is cleared.
        /// Any ANPR check is cleared.
        /// As the pump will no longer be "in use", any pending closure is actioned.
        /// </summary>
        bool CancelPayment(string reference = null);

        /// <summary>
        /// Complete a transaction process, pump will no longer be "in use".
        /// As the pump will no longer be "in use", any pending closure is actioned.
        /// <param name="cardNumber">(Obfuscated) Card number for payment.</param>
        /// <param name="cardProductName">Product name of card.</param>
        /// <param name="amount">Payment amount.</param>
        /// <param name="reference">Current logging reference</param>
        /// </summary>
        void ClearPayment(string cardNumber, string cardProductName, uint amount, string reference = null);

        /// <summary>
        /// Restart the payment timeout timer.
        /// </summary>
        /// <param name="timeout">Timeout to use, in seconds.</param>
        /// <param name="reference">Current logging reference</param>
        void ResetPaymentTimeout(int timeout, string reference = null);

        /// <summary>
        /// Check whether an authorisation can be sent to the pump.
        /// The pump will need to have an approved payment.
        /// If ANPR Link is available, the pump will need to have received a valid ANPR check.
        /// If the pump needed a Third Party POS authorisation, this will need to have been received.
        /// The pump will need to be requesting an authorisation (nozzle up).
        /// Once this returns true (with the maximum amount set), it is assumed the authorisation is sent to the pump,
        /// and it will not return true again until after the next time the pump returns to Idle (nozzle down).
        /// </summary>
        /// <param name="amount">If authorised, this will be the amount of the maximum amount of the authorisation.</param>
        /// <param name="reference">Current logging reference</param>
        /// <returns>True if an authorisation can be sent to the pump.</returns>
        bool IsAuthorised(out uint amount, string reference = null);

        /// <summary>
        /// Flag the pump as having a SecAuth request sent.
        /// Starts the SecAuth request timer.
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void SecAuthRequestSent(string reference = null);

        /// <summary>
        /// Set Secondary Auth response.
        /// </summary>
        /// <param name="response">Set Success if true, Failure if false, <see cref="SecAuthState"/></param>
        /// <param name="reference">Current logging reference</param>
        void SetSecAuthResponse(bool response, string reference = null);

        /// <summary>
        /// Flag the pump as being reserved.
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void PumpReserved(string reference = null);

        /// <summary>
        /// Set the pump to requesting (nozzle up) state.
        /// <param name="grades">Grade or grades being requested.</param>
        /// <param name="reference">Current logging reference</param>
        /// </summary>
        void Requesting(IList<byte> grades, string reference = null);

        /// <summary>
        /// Set the pump to idle (nozzle down) state.
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void Idle(string reference = null);

        /// <summary>
        /// Set the pump to delivering state.
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void Delivering(string reference = null);

        /// <summary>
        /// Set the pump to delivered state.
        /// </summary>
        void Delivered(bool deliveredRequestSent = false, string reference = null);

        /// <summary>
        /// Process a command from Hydra POS.
        /// Kiosk command sets Kiosk Only mode.
        /// OptUse command sets Mixed mode.
        /// Close command closes pump or sets close pending if pump is "in use".
        /// Open command opens pump or cancels close pending.
        /// OutSide command sets Outside Only mode.
        /// MixMode command sets Mixed mode.
        /// NightMode command sets Mixed mode pump to OutSide Only mode and Kiosk Only mode pump to Closed.
        /// EveningMode as NightMode.
        /// DayMode sets pump to default mode.
        /// </summary>
        /// <returns>True if command successful, false otherwise.</returns>
        // TODO: Remove and use GetWorker<IPosIntegratorInMode<IMessageTracking>>();
        bool HydraPosCommand(HydraPosCommand command, string reference = null);

        /// <summary>
        /// Sets the OPT mapping for this pump.
        /// </summary>
        /// <param name="opt">OPT to map.</param>
        void SetOpt(IOpt opt);

        /// <summary>
        /// Sets the TID mapping for this pump.
        /// </summary>
        /// <param name="tid"></param>
        void SetTid(string tid);

        /// <summary>
        /// Sets the pump to Kiosk Use for one transaction.
        /// </summary>
        /// <param name="isMobile">Set flag indicating the source is from a Mobile</param>
        /// <param name="reference">Current logging reference</param>
        void SetKioskUse(string reference = null, bool isMobile = false);

        /// <summary>
        /// Sets the mode of the pump to Kiosk Only.
        /// If the setDefault flag is true, set the default mode but not the current mode.
        /// If the setDefault flag is false, set the current mode but not the default mode.
        /// If the store flag is true, store the change in the database.
        /// </summary>
        /// <param name="setDefault">If true set default mode, otherwise set current mode.</param>
        /// <param name="store">If true, store mode in database.</param>
        /// <param name="reference">Current logging reference</param>
        void SetKioskOnly(bool setDefault = false, bool store = true, string reference = null);

        /// <summary>
        /// Sets the mode of the pump to Mixed.
        /// If the setDefault flag is true, set the default mode but not the current mode.
        /// If the setDefault flag is false, set the current mode but not the default mode.
        /// If the store flag is true, store the change in the database.
        /// </summary>
        /// <param name="setDefault">If true set default mode, otherwise set current mode.</param>
        /// <param name="store">If true, store mode in database.</param>
        /// <param name="reference">Current logging reference</param>
        void SetMixed(bool setDefault = false, bool store = true, string reference = null);

        /// <summary>
        /// Sets the mode of the pump to Outside Only.
        /// If the setDefault flag is true, set the default mode but not the current mode.
        /// If the setDefault flag is false, set the current mode but not the default mode.
        /// If the store flag is true, store the change in the database.
        /// </summary>
        /// <param name="setDefault">If true set default mode, otherwise set current mode.</param>
        /// <param name="store">If true, store mode in database.</param>
        /// <param name="reference">Current logging reference</param>
        void SetOutsideOnly(bool setDefault = false, bool store = true, string reference = null);

        /// <summary>
        /// Set pump as authorised by ThirdParty POS.
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void ThirdPartyAuth(string reference = null);

        /// <summary>
        /// Open the pump.
        /// </summary>
        /// <param name="store">If true, store mode in database.</param>
        /// <param name="reference">Current logging reference</param>
        void OpenPump(bool store = true, string reference = null);

        /// <summary>
        /// Close the pump, or if the pump is "in use" set close pending.
        /// </summary>
        /// <param name="store">If true, store mode in database.</param>
        /// <param name="reference">Current logging reference</param>
        void ClosePump(bool store = true, string reference = null);

        /// <summary>
        /// Force Close the pump.
        /// </summary>
        /// <param name="store">If true, store mode in database.</param>
        void ForceClosePump(bool store = true);

        /// <summary>
        /// Set the pump to the Card Inserted state..
        /// </summary>
        /// <param name="reference">Current logging reference</param>
        void CardInserted(string reference = null);

        void SetDeliveredHose(byte hose, ushort ppu, string reference = null);
        bool ResetPump(string reference = null);
        void SetOptPayment(string reference = null);
        void SetPaid(bool paid, string reference = null);
        void Authorised(string reference = null);
        void SetMaxFillOverrideForFuelCards(bool flag, bool store = true);
        void SetMaxFillOverrideForPaymentCards(bool flag, bool store = true);
        void SetAllGrades(IList<byte> allGrades, string reference = null);
        void SetUnmannedPseudoPos(bool isOn);
        void SetHasKioskPayment(bool value = true);

        PumpTransaction TransactionSummary { get; }
        bool IsKioskMode { get; }
        void LogTransactionState(PumpState incomingState, bool useOpt, bool tellOpt, bool isKioskOnly, uint volume, uint amount, string reference);
        void LogTransactionState(PaymentResult paymentResult, string reference, bool success = true);
        void ResetTransactionState(string reference);


        /// <summary>
        /// Gets the breakdown of conditions for why the pump isn't authorised.
        /// </summary>
        /// <returns>JSON formatted string</returns>
        string GetNotAuthorisedReasons();

        bool OptIsClosed { get; set; }

        /// <summary>
        /// Sets the current pump mode from the saved default mode
        /// </summary>
        /// <param name="reference">The logging reference</param>
        /// <param name="ignoreMixedModeCheck">Ignore the MixedMode/Kiosk mode check, default true</param>
        /// <returns></returns>
        bool SetModeFromDefault(string reference = null, bool ignoreMixedModeCheck = true);

        bool IsInError { get; set; }

        /// <summary>
        /// Instruct the Pump to start the PayAtKiosk timer
        /// </summary>
        /// <param name="timeout">Timeout value (in seconds)</param>
        void StartPayAtKioskPressedTimer(int timeout);

        /// <summary>
        /// Instruct the Pump to stop (and clear) the PayAtKiosk timer
        /// </summary>
        void ClearPayAtKioskPressedTimer();

        /// <summary>
        /// Check whether the timeout between selecting pay at kiosk and lifting a nozzle has expired (for any Pump linked to the OPT.
        /// </summary>
        /// <returns>True if either timeout has expired, false otherwise.</returns>
        bool HasPayAtKioskTimeoutExpired();

        /// <summary>
        /// Pump mode
        /// </summary>
        PumpModeType PumpMode { get; }

        void PaymentApprovedFailed(string reference);

        /// <summary>
        /// Returns the current PumpState
        /// </summary>
        PumpStateType PreviousPumpState { get; }

        /// <summary>
        /// Persists and returns the current PumpState
        /// </summary>
        /// <returns><see cref="PumpState"/></returns>
        PumpStateType SetPreviousPumpState();

        /// <summary>
        /// Indicates if the pump has been reserved
        /// </summary>
        bool IsReserved { get; }

        /// <summary>
        /// Is this the last pump on the linked Opt
        /// </summary>
        bool IsLastPumpOnOpt { get; }
    }
}