﻿using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Repositories.Interfaces;
using System.Collections.Specialized;
using System.Configuration;
using System.Runtime.CompilerServices;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;

namespace OPT.Common.Configuration
{
    /// <summary>
    /// Custom implementation of <see cref="IConfigurationManager"/> which uses the standard Configuration database for key/value pairs rather than app.config.
    /// </summary>
    public class DbCachedConfigurationManager : Loggable, IConfigurationManager
    {
        private ICacheHelper _cacheHelper;
        private IConfigurationRepository _repository;
        private DbCachedNameValueCollection _appSettings;

        public DbCachedConfigurationManager(IHtecLogger logger, string typeName = null) : base(logger, null, typeName)
        {
        }

        public void Register(ICacheHelper cacheHelper, IConfigurationRepository repository)
        {
            _cacheHelper = cacheHelper;
            _repository = repository;

            RefreshSection(null);
        }

        public NameValueCollection AppSettings
        {
            get
            {
                if (!(_cacheHelper?.IsCachedItemValid(ConfigurationConstants.CachedItemTypeConfiguration, ConfigurationConstants.CachedItemCategories) ?? true))
                {
                    RefreshSection(null);
                }
                return _appSettings;
            }
        }

        public ConnectionStringSettingsCollection ConnectionStrings => System.Configuration.ConfigurationManager.ConnectionStrings;

        public string ConnectionString(string connectionName)
        {
            string connection = null;
            var connectionCollection = System.Configuration.ConfigurationManager.ConnectionStrings[connectionName];
            if (connectionCollection != null)
            {
                connection = connectionCollection.ConnectionString;
            }
            return connection;
        }

        public string GetAppSetting(string settingName, string section = "")
        {
            InvalidOperation();
            return null;
        }

        public bool IsAvailable(string settingName, string section = "")
        {
            InvalidOperation();
            return false;
        }

        public IConfiguration OpenExeConfiguration(ConfigurationUserLevel userLevel)
        {
            InvalidOperation();
            return null;
        }

        public IConfiguration OpenExeConfiguration(string exePath)
        {
            InvalidOperation();
            return null;
        }

        public IConfiguration OpenMappedExeConfiguration(ExeConfigurationFileMap fileMap, ConfigurationUserLevel userLevel)
        {
            InvalidOperation();
            return null;
        }

        public void RefreshSection(string sectionName)
        {
            _appSettings = new DbCachedNameValueCollection(Logger, _repository, _cacheHelper);
        }

        public void SetAppSetting(string settingName, string settingValue, string section = "")
        {
            InvalidOperation();
        }

        private void InvalidOperation([CallerMemberName] string methodName = null)
        {
            GetLogger().Error("NotImplementedException", () => new[] {$"Method: {methodName} being called!"});
        }
    }
}
