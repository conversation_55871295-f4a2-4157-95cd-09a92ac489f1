import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { Observable, of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { CardProperty } from 'src/app/core/enums/cardProperty.enum';
import { CardReferenceDetails } from 'src/app/core/models/cardReferenceDetails.model';
import { ESocketPosConfigDetails } from 'src/app/core/models/eSocketPosConfigDetails.model';
import { ADVANCED_SERVICE_PROVIDER, ADVANCED_SERVICE_SPY } from 'src/app/services/advanced-config.service.spy';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { LOCAL_ACCOUNT_SERVICE_PROVIDER, LOCAL_ACCOUNT_SERVICE_SPY } from 'src/app/services/local-account.service.spy';
import { SIGNAL_R_SERVICE_PROVIDER, SIGNAL_R_SERVICE_SPY } from 'src/app/services/signal-r.service.spy';
import { NGX_LOGGER_PROVIDER, NGX_LOGGER_SPY } from 'src/app/testing/ngxlogger.spy';
import { AdvancedConfigComponent } from './advanced-config.component';

describe('AdvancedConfigComponent', () => {
  let advancedConfigComponent: AdvancedConfigComponent;  

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        AdvancedConfigComponent,
        FormBuilder,
        NGX_LOGGER_PROVIDER(),
        ADVANCED_SERVICE_PROVIDER(),
        SIGNAL_R_SERVICE_PROVIDER(),
        LOADING_SERVICE_PROVIDER(),
        LOCAL_ACCOUNT_SERVICE_PROVIDER(),
      ],
    });

    LOCAL_ACCOUNT_SERVICE_SPY().getLocalAccounts.and.returnValue(of({}));
    ADVANCED_SERVICE_SPY().getConfigDataChanged.and.returnValue(of());
    SIGNAL_R_SERVICE_SPY().getDivertDetailsSignalRMessage.and.returnValue(of());
    SIGNAL_R_SERVICE_SPY().getLocalAccountsSignalRMessage.and.returnValue(of());

    advancedConfigComponent = TestBed.inject(AdvancedConfigComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(advancedConfigComponent).toBeTruthy();
  });

  it('.refreshData() should handle success response from service', ()=>{
    //Arrange
    let fakeAdvancedConfig = {
      AsdaDayEndReport: false,
      AutoAuth: true,
      CardReferences: [{
        Acquirer: "Visa,Mastercard,Delta&Electron",
        FuelCard: false,
        InUse: false,
        Name: "VISA",
        Reference: 1,
        External: "External"
      }],
      FilePruneDays: 30,
      ForwardFuelPriceUpdate: true,
      FuelCategory: 99,
      FuellingBackoffAuth: 0,
      FuellingBackoffPreAuth: 0,
      FuellingBackoffStopOnly: 0,
      FuellingBackoffStopStart: 0,
      FuellingIndefiniteWait: true,
      FuellingWaitMinutes: 5,
      LoyaltyAvailable: ["Morrisons"],
      MediaChannel: true,
      MorrisonLoyaltyAvailable: true,
      PaymentTimeoutMixed: 15,
      PaymentTimeoutNozzleDown: 15,
      PaymentTimeoutOpt: 15,
      PaymentTimeoutPod: 300,
      TimeoutKiosk: 14,
      TimeoutSecAuth: 12,
      PosClaimNumber: 99,
      ReceiptMaxCount: 12,
      ReceiptPruneDays: 14,
      ReceiptTimeout: 3600,
      TillNumber: 99,
      TransactionPruneDays: 50,
      UnmannedPseudoPos: true,
      MorrisonsLoyaltyAvailable: true,
      NozzleUpForKioskUse: false,
      UseReplaceNozzleScreen: false,
      MaxFillOverride: 0,
      SiteType: 'Retail',
      SiteName: '',
      VatNumber: '',
      CurrencyCode: 0,
      LocalAccountsEnabled: false,
      ConfigurationCategories: [],
      ESocketPosConfig: {} as ESocketPosConfigDetails,
      PosType: 'Hydra',
      PosTypes: [{ Key: 'Hydra/VBO.Net', Value: 'HYDRA' }, { Key: 'Morrions Bos', Value: 'RETALIX' }, { Key: 'None', Value: 'NONE' }],
      PumpType: 'HSC',
      PumpTypes: [{ Key: 'HSC', Value: 'HSC' }, { Key: 'DOMS', Value: 'DOMS' }],
      MobilePaymentType: 'None',
      MobilePaymentTypes: [{ Key: 'None', Value: 'NONE' }, { Key: 'HydraMobile', Value: 'HYDRAMOBILE' }],
      MobilePosType: 'None',
      MobilePosTypes: [{ Key: 'None', Value: 'NONE' }, { Key: 'HydraMobile', Value: 'HYDRAMOBILE' }],
      BosType: 'HYDRA',
      BosTypes: [{ Key: 'Hydra', Value: 'HYDRA' }, { Key: 'Retalix', Value: 'RETALIX' }, { Key: 'None', Value: 'NONE' }],
      SecAuthType: 'HYDRA',
      SecAuthTypes: [{ Key: 'Hydra ANPR', Value: 'HYDRA' }, { Key: 'None', Value: 'NONE' }],
      PaymentConfigType: 'ESOCKETPOS-SQLSERVER',
      PaymentConfigTypes: [{ Key: 'eSocket.POS (SqlServer)', Value: 'ESOCKETPOS-SQLSERVER' }, {Key: 'eSocket.POS (HSqlDb)', Value: 'ESOCKETPOS-HSQLDB' }, { Key: 'None', Value: 'NONE' }]
    };
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(of(fakeAdvancedConfig));

    //Act
    advancedConfigComponent.refreshData();

    //Assert
    expect(advancedConfigComponent.advancedConfigData).toEqual(fakeAdvancedConfig);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', ()=>{
    //Arrange
    ADVANCED_SERVICE_SPY().getAdvancedConfig.and.returnValue(throwError({status:500}));

    //Act
    advancedConfigComponent.refreshData();

    //Assert
    expect(advancedConfigComponent.advancedConfigData).toBeUndefined();
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.onCardFormSubmit() should handle success response from service', ()=>{
    //Arrange
    advancedConfigComponent.cardReferenceData = new CardReferenceDetails();
    advancedConfigComponent.cardReferenceData.Name = 'ddd';
    advancedConfigComponent.cardReferenceData.FuelCard = true;
    ADVANCED_SERVICE_SPY().setCardProperty.and.returnValue(of({}));

    //Act
    advancedConfigComponent.onCardFormSubmit();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setCardProperty).toHaveBeenCalledOnceWith(CardProperty.FuelCard,advancedConfigComponent.cardReferenceData);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  })

  it('.onCardFormSubmit() should handle unsuccess response from service', ()=>{
    //Arrange
    advancedConfigComponent.cardReferenceData = new CardReferenceDetails();
    advancedConfigComponent.cardReferenceData.Name = 'ddd';
    advancedConfigComponent.cardReferenceData.FuelCard = true;
    ADVANCED_SERVICE_SPY().setCardProperty.and.returnValue(throwError({status:500}));

    //Act
    advancedConfigComponent.onCardFormSubmit();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setCardProperty).toHaveBeenCalledOnceWith(CardProperty.FuelCard,advancedConfigComponent.cardReferenceData);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  })

  it('.updateSiteType() should handle success response from service', () =>{
    advancedConfigComponent.advancedForm.controls['SiteType'].setValue('');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));
    
    advancedConfigComponent.updateSiteType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.siteTypeError).toBe(false);
  });

  it('.updateSiteType() should handle failure response from service', () =>{
    advancedConfigComponent.advancedForm.controls['SiteType'].setValue('');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({status:500}));
    
    advancedConfigComponent.updateSiteType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.siteTypeError).toBe(true);
  });

  it('.updatePosType() should handle success response from service', () =>{
    advancedConfigComponent.advancedForm.controls['PosType'].setValue('HydraPos');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));
    
    advancedConfigComponent.updatePosType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.posTypeError).toBe(false);
  });

  it('.updatePosType() should handle failure response from service', () =>{
    advancedConfigComponent.advancedForm.controls['PosType'].setValue('HydraPos');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({status:500}));

    advancedConfigComponent.updatePosType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.posTypeError).toBe(true);
  });

  it('.updatePumpType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['PumpType'].setValue('HSC');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updatePumpType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.pumpTypeError).toBe(false);
  });

  it('.updatePumpType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['PumpType'].setValue('HSC');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updatePumpType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.pumpTypeError).toBe(true);
  });

  it('.updateMobilePaymentType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['MobilePaymentType'].setValue('HYDRAMOBILE');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updateMobilePaymentType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.mobilePaymentTypeError).toBe(false);
  });

  it('.updateMobilePaymentType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['MobilePaymentType'].setValue('NONE');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updateMobilePaymentType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.mobilePaymentTypeError).toBe(true);
  });

  it('.updateMobilePosType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['MobilePosType'].setValue('HYDRAMOBILE');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updateMobilePosType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.mobilePosTypeError).toBe(false);
  });

  it('.updateMobilePosType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['MobilePosType'].setValue('NONE');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updateMobilePosType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.mobilePosTypeError).toBe(true);
  });

  it('.updateBackOfficeType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['BosType'].setValue('HYDRA');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updateBackOfficeType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.bosTypeError).toBe(false);
  });

  it('.updateBackOfficeType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['BosType'].setValue('IFSF');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updateBackOfficeType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.bosTypeError).toBe(true);
  });

  it('.updateSecAuthType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['SecAuthType'].setValue('HYDRA');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updateSecAuthType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.secAuthTypeError).toBe(false);
  });

  it('.updateSecAuthType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['SecAuthType'].setValue('IFSF');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updateSecAuthType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.secAuthTypeError).toBe(true);
  });

  it('.updatePaymentConfigType() should handle success response from service', () => {
    advancedConfigComponent.advancedForm.controls['PaymentConfigType'].setValue('NONE');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(of({}));

    advancedConfigComponent.updatePaymentConfigType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.paymentConfigTypeError).toBe(false);
  });

  it('.updatePaymentConfigType() should handle failure response from service', () => {
    advancedConfigComponent.advancedForm.controls['PaymentConfigType'].setValue('ESOCKETPOS');
    ADVANCED_SERVICE_SPY().setIntegrationType.and.returnValue(throwError({ status: 500 }));

    advancedConfigComponent.updatePaymentConfigType();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.paymentConfigTypeError).toBe(true);
  });

  it('.updateLocalAccountsEnabled() should handle success response from service', () =>{
    advancedConfigComponent.advancedForm.controls['LocalAccountsEnabled'].setValue('');
    LOCAL_ACCOUNT_SERVICE_SPY().setLocalAccountsEnabled.and.returnValue(of({}));
    
    advancedConfigComponent.updateLocalAccountsEnabled();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.localAccountsEnabledError).toBe(false);
  });

  it('.updateLocalAccountsEnabled() should handle failure response from service', () =>{
    advancedConfigComponent.advancedForm.controls['LocalAccountsEnabled'].setValue('');
    LOCAL_ACCOUNT_SERVICE_SPY().setLocalAccountsEnabled.and.returnValue(throwError({status:500}));
    
    advancedConfigComponent.updateLocalAccountsEnabled();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.localAccountsEnabledError).toBe(true);
  });

  it('.updateAutoAuth() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateAutoAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateAutoAuth() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateAutoAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateMediaChannel() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateMediaChannel();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateMediaChannel() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateMediaChannel();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateUnmannedPseudoPos() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateUnmannedPseudoPos();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateUnmannedPseudoPos() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateUnmannedPseudoPos();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutOpt() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutOpt();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutOpt() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutOpt();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutPod() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutPod();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutPod() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutPod();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutMixed() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutMixed();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutMixed() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutMixed();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutNozzleDown() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutNozzleDown();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updatePaymentTimeoutNozzleDown() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updatePaymentTimeoutNozzleDown();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateTimeoutKiosk() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));

    //Act
    advancedConfigComponent.updateTimeoutKiosk();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateTimeoutKiosk() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({ status: 500 }))

    //Act
    advancedConfigComponent.updateTimeoutKiosk();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateTimeoutSecAuth() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(of({}));

    //Act
    advancedConfigComponent.updateTimeoutSecAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateTimeoutSecAuth() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setPaymentTimeout.and.returnValue(throwError({ status: 500 }))

    //Act
    advancedConfigComponent.updateTimeoutSecAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setPaymentTimeout).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateTillNumber() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateTillNumber();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateTillNumber() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateTillNumber();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuelCategory() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuelCategory();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuelCategory() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuelCategory();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updatePosClaimNumber() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updatePosClaimNumber();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updatePosClaimNumber() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updatePosClaimNumber();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFilePruneDays() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFilePruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFilePruneDays() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFilePruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateTransactionPruneDays() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateTransactionPruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateTransactionPruneDays() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateTransactionPruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateReceiptPruneDays() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateReceiptPruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateReceiptPruneDays() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateReceiptPruneDays();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingIndefiniteWait() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingIndefiniteWait();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingIndefiniteWait() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingIndefiniteWait();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingWaitMinutes() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingWaitMinutes();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingWaitMinutes() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingWaitMinutes();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffAuth() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingBackoffAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffAuth() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingBackoffAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffPreAuth() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingBackoffPreAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffPreAuth() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingBackoffPreAuth();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffStopStart() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingBackoffStopStart();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffStopStart() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingBackoffStopStart();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffStopOnly() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateFuellingBackoffStopOnly();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateFuellingBackoffStopOnly() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateFuellingBackoffStopOnly();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.updateForwardFuelPriceUpdate() should handle success response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(of({}));
    
    //Act
    advancedConfigComponent.updateForwardFuelPriceUpdate();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
  });

  it('.updateForwardFuelPriceUpdate() should handle unsuccess response from service', () => {
    //Arrange
    ADVANCED_SERVICE_SPY().setProperty.and.returnValue(throwError({status: 500}))
    
    //Act
    advancedConfigComponent.updateForwardFuelPriceUpdate();

    //Assert
    expect(ADVANCED_SERVICE_SPY().setProperty).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
  });

  it('.clearMaxFillOverride() should handle success response from service', () =>{
    ADVANCED_SERVICE_SPY().clearMaxFillOverride.and.returnValue(of({}));

    advancedConfigComponent.clearMaxFillOverride();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(0);
    expect(advancedConfigComponent.maxFillOverrideError).toBe(false);
  });

  it('.clearMaxFillOverride() should handle failure response from service', () =>{
    ADVANCED_SERVICE_SPY().clearMaxFillOverride.and.returnValue(throwError({status:500}));

    advancedConfigComponent.clearMaxFillOverride();

    expect(NGX_LOGGER_SPY().info).toHaveBeenCalledTimes(1);
    expect(NGX_LOGGER_SPY().debug).toHaveBeenCalledTimes(0);
    expect(NGX_LOGGER_SPY().error).toHaveBeenCalledTimes(1);
    expect(advancedConfigComponent.maxFillOverrideError).toBe(true);
  });
});
