﻿using CSharpFunctionalExtensions;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Connections.Adapters;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Models.MediaMessage;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers.Interfaces;
using System;
using System.IO;
using System.IO.Abstractions;
using System.Runtime.CompilerServices;
using System.Text;
using System.Xml.Linq;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;

namespace OPT.Common.Workers
{
    public class MediaChannelWorker : ListenerWorker<MediaMessage, IHydraDb, ITelemetryWorker>, IMediaChannelWorker
    {
        /// <summary>
        /// Config key for the date format used for rolling log files.
        /// </summary>
        public const string ConfigKeyLogFileDateFormat = "LogFileDateFormat";

        /// <summary>
        /// Default value for the date format used for rolling log files.
        /// </summary>
        public const string DefaultValueLogFileDateFormat = "yyyy-MM-dd_HH";

        public const string ConfigKeyLogFolderDateFormat = ConfigConstants.ConfigKeyLogFolderDateFormat;
        public const string DefaultValueLogFolderDateFormat = ConfigConstants.DefaultValueLogFolderDateFormat;
        private readonly IFileSystem _fileSystem;
        private readonly IOptCollection _allOpts;

        private const string HeaderTlv = MediaMessageAdapter.HeaderTlv;

        /// <summary>Constructor for ANPR Worker.</summary>
        /// <param name="hydraDb">Hydra Database.</param>
        /// <param name="logger">Htec Logger for this class.</param>
        /// <param name="telemetryWorker">ITelemetryWorker instance</param>
        /// <param name="connectionThread">ConnectionThread instance</param>
        /// <param name="configurationManager">IConfigurationManager instance</param>
        /// <param name="fileSystem">IFileSystem instance</param>
        /// <param name="allOpt">IOptCollection instance</param>
        public MediaChannelWorker(IHtecLogger logger, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, IListenerConnectionThread<MediaMessage> connectionThread,
            IConfigurationManager configurationManager, IFileSystem fileSystem, IOptCollection allOpt) : base(logger, telemetryWorker, connectionThread, hydraDb, configurationManager)
        {
            _fileSystem = fileSystem ?? throw new ArgumentNullException(nameof(fileSystem));
            _allOpts = allOpt ?? throw new ArgumentNullException(nameof(allOpt));
        }

        protected override GenericEndPoint DoGetEndPoint()
        {
            var endPoints = GetEndPoints();

            var endpoint = endPoints.MediaChannelBindEndPoint;

            return new GenericEndPoint(endpoint.Address, endpoint.Port);
        }

        protected override Result<MediaMessage> DoOnMessageReceived(IMessageTracking<MediaMessage> message, int id)
        {
            var request = message.Request;
            MediaMessage result = null;
            switch (request.Type)
            {
                case MediaMessageType.GetFile:
                    result = DoOnMessageReceivedGetFile(request, id, message.FullId);
                    break;

                case MediaMessageType.LogFile:
                    result = DoOnMessageReceivedLogFile(request, id, message.FullId);
                    break;

                case MediaMessageType.FileDownloaded:
                case MediaMessageType.GetFileResponse:
                default:
                    break;
            }

            return Result.Success<MediaMessage>(result);
        }

        private MediaMessage DoOnMessageReceivedGetFile(MediaMessage message, int id, string loggingReference, [CallerMemberName] string methodName = null)
        {
            var optId = $"OPTId: n/a ({id})";
            DoDeferredLogging(LogLevel.Debug, HeaderTlv, () => new[] {"Get File Message Received", optId}, methodName: methodName, reference: loggingReference);
            var fileName = string.Empty;
            foreach (var component in message.Components)
            {
                if (component.Type == MediaComponentType.FileName)
                {
                    fileName = Encoding.ASCII.GetString(component.Bytes);
                }
            }

            if (fileName.IsNullOrWhiteSpace())
            {
                return null;
            }

            var fileLocations = HydraDb.GetFileLocations();

            string fullFileName;
            DoDeferredLogging(LogLevel.Info, $"{HeaderTlv}.FindingFile",
                () => new[] {$"FileName: {fileName}; MediaDirectory: {fileLocations?.MediaDirectory}; Playlist Directory is {fileLocations?.PlaylistDirectory}", optId}, methodName: methodName,
                reference: loggingReference);

            if (fileLocations?.MediaDirectory != null && File.Exists(fileLocations.MediaDirectory + fileName))
            {
                fullFileName = fileLocations.MediaDirectory + fileName;
            }
            else if (fileLocations?.PlaylistDirectory != null && File.Exists(fileLocations.PlaylistDirectory + fileName))
            {
                fullFileName = fileLocations.PlaylistDirectory + fileName;
            }
            else
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"File does not exist: {fileName}", optId}, methodName: methodName, reference: loggingReference);
                return null;
            }

            DoDeferredLogging(LogLevel.Info, $"{HeaderTlv}.FileFound", () => new[] {fullFileName, optId}, methodName: methodName, reference: loggingReference);
            var stagedMessage = new MediaMessage(fullFileName);
            return stagedMessage;
        }

        private MediaMessage DoOnMessageReceivedLogFile(MediaMessage message, int id, string loggingReference, [CallerMemberName] string methodName = null)
        {
            var optId = "UnknownOPT";

            var bytes = new byte[0];
            foreach (var component in message.Components)
            {
                switch (component.Type)
                {
                    case MediaComponentType.OptId:
                        optId = Encoding.ASCII.GetString(component.Bytes);
                        break;

                    case MediaComponentType.FileContent:
                        bytes = component.Bytes;
                        break;
                }
            }

            DoDeferredLogging(LogLevel.Info, $"{HeaderTlv}.LogFile", () => new[] {$"OPTId: {optId} ({id}); FileSize: {bytes.Length}"}, methodName: methodName, reference: loggingReference);
            if (bytes.Length > 0)
            {
                var filename = GetLogFileName(optId);
                try
                {
                    CheckLogFileDirectory(_fileSystem.Path.GetDirectoryName(filename));

                    // Handle duplicates and BST/GMT changes
                    if (_fileSystem.File.Exists(filename))
                    {
                        filename = filename.Replace(".log", $"_{DateTime.Now:mmssfff}.log"); 
                    }

                    _fileSystem.File.WriteAllBytes(filename, bytes);
                    RecordLogFileReceived(optId, $"Log file written: {filename}");
                }
                catch (Exception ex)
                {
                    DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {$"OPTId: {optId} ({id})", "Error saving log file"}, ex, methodName: methodName, reference: loggingReference);
                    RecordLogFileReceived(optId, $"Save log file failed: {filename}");
                }
            }

            return new MediaMessage(MediaMessageType.LogFile, null);
        }

        private Result RecordLogFileReceived(string optId, string infoMessage)
        {
            var opt = _allOpts.GetOptForIdString(optId);
            if (opt == null)
            {
                return Result.Failure<XElement>("Invalid Opt id");
            }

            opt.LogFileRequestSent = false;
            GetWorker<IControllerWorker>().SendInformation(infoMessage);
            NotificationWorker.PushChange(EventType.OPTChanged, opt.IdString);
            return Result.Success();
        }

        private string GetLogFileName(string optId)
        {
            var fileLocations = HydraDb.GetFileLocations();

            var dateTime = DateTime.Now;
            return _fileSystem.Path.Combine(fileLocations.OptLogFileDirectory,
                $"{dateTime.ToString(ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyLogFolderDateFormat, DefaultValueLogFolderDateFormat, Logger))}", 
                "OptLogs",
                $"{optId}",
                $"OPT_{optId}_{dateTime.ToString(ConfigurationManager.AppSettings.GetAppSettingOrDefault(ConfigKeyLogFileDateFormat, DefaultValueLogFileDateFormat, Logger))}.log");
        }

        private void CheckLogFileDirectory(string getDirectoryName)
        {
            DoAction(() =>
            {
                try
                {
                    Directory.CreateDirectory(getDirectoryName);
                }
                catch (Exception ex)
                {
                    GetLogger().Error(HeaderException, () => new[] {"Error creating log file directory"}, ex);
                }
            }, LoggingReference);
        }
    }
}

