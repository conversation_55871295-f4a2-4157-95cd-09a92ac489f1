﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class SaleLine
    {
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        [JsonProperty("saleItem")]
        public JObject SaleItem { get; set; }
        [JsonProperty("saleQuantity")]
        public double SaleQuantity { get; set; }
        [JsonProperty("promotionEligible")]
        public double PromotionEligible { get; set; }
        [JsonProperty("promotionApplied")]
        public double PromotionApplied { get; set; }
        [JsonProperty("saleAmount")]
        public double SaleAmount { get; set; }
        [JsonProperty("saleTaxAmount")]
        public double SaleTaxAmount { get; set; }
        [JsonProperty("originalSaleAmount")]
        public double OriginalSaleAmount { get; set; }
        [JsonProperty("itemReturn")]
        public bool ItemReturn { get; set; }
        [JsonProperty("overrides")]
        public List<string> Overrides { get; set; }
        [JsonProperty("discountModifiers")]
        public List<string> DiscountModifiers { get; set; }
        [JsonProperty("promotionModifiers")]
        public List<string> PromotionModifiers { get; set; }
        [JsonProperty("priceChangeModifiers")]
        public List<string> PriceChangeModifiers { get; set; }
        [JsonProperty("fuelSale")]
        public FuelSale FuelSale { get; set; }
    }
}
