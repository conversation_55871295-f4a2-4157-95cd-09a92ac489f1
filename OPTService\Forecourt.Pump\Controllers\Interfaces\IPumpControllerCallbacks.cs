﻿using CSharpFunctionalExtensions;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Pump.Messages;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Pump.Controllers.Interfaces
{
    /// <summary>
    /// Any and all callbacks of a PumpController
    /// </summary>
    public interface IPumpControllerCallbacks : IWorkerable
    {
        /// <summary>
        /// Called when a MaxPumps (or similar) message is received.
        /// </summary>
        /// <param name="numberOfPumps">Number of Pumps</param>
        /// <param name="loggingReference">Logging reference</param>
        void OnMaxPumps(int numberOfPumps, string loggingReference);

        /// <summary>
        /// Called when a Meter Readings (or similar) message is received.
        /// </summary>
        /// <param name="readings">List of meter readings</param>
        /// <param name="loggingReference">Logging reference</param>
        void OnMeterReadings(IEnumerable<MeterReadings> readings, string loggingReference);

        /// <summary>
        /// Called when a Pump Data (or similar) message is received.
        /// </summary>
        /// <param name="pumpData">Pump Data in received message.</param>
        void OnPumpData(PumpData pumpData);

        /// <summary>
        /// Called when the controller connects
        /// </summary>
        /// <param name="ipAddress">IpAddress of the controller</param>
        /// <param name="port">Port of the controller</param>
        void OnConnected(IPAddress ipAddress = null, int? port = null);

        /// <summary>
        /// Called when the controller disconnects
        /// </summary>
        /// <param name="id">Id of the connection</param>
        void OnDisconnected(int? id = null);

        /// <summary>
        /// Called when new prices are avialable
        /// </summary>
        /// <param name="timeStamp">When the prices are available from</param>
        void OnNewPricesAvailable(DateTime timeStamp);

        /// <summary>
        /// Called when a Pumps Grade Price is received.
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <param name="gradeId">Grade id</param>
        /// <param name="gradeName">Grade name</param>
        /// <param name="hoseIds">Hose/Nozzle id list</param>
        /// <param name="price">Price per unit/litre</param>
        /// <param name="loggingReference">Logging reference</param>
        void OnGradePriceChange(byte pump, byte gradeId, string gradeName, uint price, IEnumerable<byte> hoseIds, string loggingReference = null);

        /// <summary>
        /// Interval that regular heartmessages are sent
        /// </summary>
        TimeSpan HeartbeatInterval { get; }

        /// <summary>
        /// Interval after heartbeat messages expire
        /// </summary>
        TimeSpan HeartbeatTimeout { get; }

        /// <summary>
        /// Interval for when Completed Transactions are checked for clearing
        /// </summary>
        TimeSpan TransactionCashedOutStateCheckInterval { get; }

        /// <summary>
        /// Flush pending OnPumpData queue
        /// </summary>
        /// <param name="pump">Pump number</param>
        /// <returns>Result</returns>
        Result FlushOnPumpData(byte pump);
    }
}
