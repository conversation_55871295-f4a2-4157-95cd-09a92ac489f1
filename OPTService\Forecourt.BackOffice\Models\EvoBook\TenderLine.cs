﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class TenderLine
    {
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        [JsonProperty("methodOfPayment")]
        public JObject MethodOfPayment { get; set; }
        [Json<PERSON>roperty("tenderAmount")]
        public double TenderAmount { get; set; }
        [Json<PERSON>roperty("foreignCurrencyAmount")]
        public double ForeignCurrencyAmount { get; set; }
        [JsonProperty("cardDetails")]
        public CardDetail CardDetails { get; set; }
        [Json<PERSON>roperty("changeLine")]
        public bool ChangeLine { get; set; }
        [JsonProperty("voucherBarcode")]
        public string VoucherBarcode { get; set; }
        [JsonProperty("donation")]
        public bool Donation { get; set; }
        [JsonProperty("donationBaseTenderId")]
        public string DonationBaseTenderId { get; set; }
    }
}
