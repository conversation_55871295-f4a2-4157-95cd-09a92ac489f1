﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class TaxLine
    {
        [JsonProperty("lineItemSequenceNumber")]
        public int LineItemSequenceNumber { get; set; }
        [JsonProperty("voidedFlag")]
        public bool VoidedFlag { get; set; }
        [JsonProperty("startTime")]
        public string StartTime { get; set; }
        [JsonProperty("endTime")]
        public string EndTime { get; set; }
        [JsonProperty("fiscalReceipt")]
        public bool FiscalReceipt { get; set; }
        [JsonProperty("taxAuthorityId")]
        public int TaxAuthorityId { get; set; }
        [JsonProperty("taxGroupId")]
        public string TaxGroupId { get; set; }
        [JsonProperty("taxGroupName")]
        public string TaxGroupName { get; set; }
        [JsonProperty("taxAmount")]
        public double TaxAmount { get; set; }
        [JsonProperty("taxableAmount")]
        public double TaxableAmount { get; set; }
        [JsonProperty("taxPercent")]
        public double TaxPercent { get; set; }
        [JsonProperty("taxReclaimable")]
        public bool TaxReclaimable { get; set; }
    }
}
