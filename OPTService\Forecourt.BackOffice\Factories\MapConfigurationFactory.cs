﻿using Forecourt.Bos.Factories.Interfaces;
using Forecourt.Bos.Orchestrations.Interfaces;
using Forecourt.Core.Pos.Enums;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Factories;
using Htec.Foundation.Models.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Bos.Factories
{
    /// <summary>
    /// Map configuration data factory
    /// </summary>
    public class MapConfigurationFactory: Factory<string, IMapConfigurationOrchestration<ILogTracking>>, IMapConfigurationFactory
    {
        private readonly Func<string, IMapConfigurationOrchestration<ILogTracking>> _resolveTypeInstance;

        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public MapConfigurationFactory(IHtecLogManager logManager, IConfigurationManager configurationManager, Func<string, IMapConfigurationOrchestration<ILogTracking>> resolveTypeInstance) :
            base(logManager, $"{nameof(MapConfigurationFactory).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _resolveTypeInstance = resolveTypeInstance ?? throw new ArgumentNullException(nameof(resolveTypeInstance));

            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => null);
            AddItem($"{PosType.HydraPos}", "Htec Hydra/VBO.net", (key) => null);
            AddItem($"{PosType.Retalix}", "Morrisons BOS", (key) => null);
            AddItem($"{PosType.GenericApiSignalR}", "Generic RestAPI and SignalR", (key) => _resolveTypeInstance(key));
            AddItem($"{PosType.MadicApiSignalR}", "MADIC RestAPI and SignalR", (key) => _resolveTypeInstance(key));
        }
    }
}
