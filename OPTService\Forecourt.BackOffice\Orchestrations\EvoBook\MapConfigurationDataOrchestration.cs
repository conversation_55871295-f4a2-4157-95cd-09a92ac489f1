﻿using Forecourt.Bos.Configuration;
using Forecourt.Bos.Configuration.EvoBook;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.Orchestrations.Interfaces;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Attributes;
using Htec.Foundation.Models;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using configEvoBook = Htec.Hydra.Core.Bos.Configuration.EvoBook;
using configHydra = Htec.Hydra.Core.Bos.Configuration.Hydra;

namespace Forecourt.Bos.Orchestrations.EvoBook
{
    /// <summary>
    /// Orchestration that manages the process mapping Hydra configuration data to an external systems equivalent
    /// </summary>
    /// <inheritdoc/>
    [HasConfiguration]
    public class MapConfigurationDataOrchestration : MapConfigurationDataOrchestration<HydraToEvoBookMapping>, IMapEvoBookConfigDataOrchestration
    {        
        private readonly IGradeHelper _gradeHelper;

        /// <inheritdoc/>
        public MapConfigurationDataOrchestration(IHtecLogManager logManager, IConfigurationManager configurationManager, IHydraDb hydraDb, IGradeHelper gradeHelper, IHttpClientFactory httpClientFactory) : 
            base(logManager, nameof(HydraToEvoBookMapping), configurationManager, hydraDb, httpClientFactory)
        {
            _gradeHelper = gradeHelper ?? throw new ArgumentNullException(nameof(gradeHelper));
        }

        private async Task<StatusCodeResult> GetBosConfig(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetBosConfig);

            model.BosConfig = HydraDb.BosConfig;
            model.EndPointsConfig = HydraDb.EndPointsConfig;

            if (model.BosConfig == null)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception("No BosConfiguration defined"));
            }

            if (model.EndPointsConfig == null) 
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception("No EndPointsConfiguration defined"));
            }

            var result = model.BosConfig.IsValid();
            if (!result.IsSuccess)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(result.Error));
            }

            result = model.EndPointsConfig.IsValid();
            if (!result.IsSuccess)
            {
                return StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception(result.Error));
            }

            return StatusCodeResult.Success;
        }

        #region GetHydraXxx

        private async Task<StatusCodeResult> GetHydraVatRates(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetHydraVatRates);

            var vatRates = _gradeHelper.Grades.Select(x => x.VatRate).Distinct();
            model.HydraData.VatRates = vatRates.Select(x => new configHydra.VatRate($"VAT{x:00}", $"VAT Rate {x:00}", x));

            foreach (var item in model.HydraData.VatRates)
            {
                model.HydraVatRates.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetHydraGrades(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetHydraGrades);

            foreach (var item in _gradeHelper.Grades)
            {
                var vatId = model.HydraData.VatRates.FirstOrDefault(x => x.Id == $"VAT{item.VatRate:00}")?.Id ?? "VAT20";
                model.HydraGrades.Add(item.Grade, new configHydra.Grade(item.Grade, item.Name, vatId));
            }
            model.HydraData.Grades = model.HydraGrades.SourceValues;

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetHydraProducts(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetHydraProducts);

            model.HydraData.Products = HydraDb.FetchTariffMappings();
            foreach (var item in model.HydraData.Products)
            {
                model.HydraProducts.Add(item.ProductCode, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetHydraCurrencies(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetHydraCurrencies);

            model.HydraData.Currencies = new List<configHydra.Currency>() { new("GBP", "Great British Pound", "£") };
            foreach (var item in model.HydraData.Currencies)
            {
                model.HydraCurrencies.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetHydraCardReferences(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetHydraCardReferences);

            model.HydraData.CardReferences = HydraDb.FetchCardReferences();
            foreach(var item in model.HydraData.CardReferences)
            {
                model.HydraCardReferences.Add(item.CardRef, item);
            }

            return StatusCodeResult.Success;
        }

        #endregion

        #region GetExternalXxx

        private async Task<StatusCodeResult> GetConfigClientHttp(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetConfigClientHttp);

            model.ConfigHttpClient = HttpClientFactory.GetHttpClient();

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetEvoBookVatRates(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetEvoBookVatRates);

            var apiUrl = $"{model.EndPointsConfig.ApiConfig}/v1/TaxGroups?c={model.BosConfig.CompanyId}&s={model.BosConfig.StoreId}";

            var result = await MakeApiCall<IEnumerable<configEvoBook.TaxLevel>>(() => model.ConfigHttpClient.GetAsync(apiUrl), model.LoggingReference).ConfigureAwait(false);
            if (!result.IsSuccess)
            {
                return StatusCodeResult.Specific(result.StatusCode, result.Exception);
            }

            model.ExternalData.VatRates = result.Results;

            foreach (var item in model.ExternalData.VatRates)
            {
                model.ExternalVatRates.Add(item.TaxLevelId, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetEvoBookGrades(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetEvoBookGrades);

            var apiUrl = $"{model.EndPointsConfig.ApiConfig}/v1/Grades?c={model.BosConfig.CompanyId}&s={model.BosConfig.StoreId}";

            var result = await MakeApiCall<IEnumerable<configEvoBook.Grade>>(() => model.ConfigHttpClient.GetAsync(apiUrl), model.LoggingReference).ConfigureAwait(false);
            if (!result.IsSuccess)
            {
                return StatusCodeResult.Specific(result.StatusCode, result.Exception);
            }

            model.ExternalData.Grades = result.Results;

            foreach (var item in model.ExternalData.Grades)
            {
                model.ExternalGrades.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetEvoBookProducts(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetEvoBookProducts);

            var products = model.ExternalData.Grades.Select(x => new configEvoBook.Product(x.Product.Value<int>("id"))
            {
                Description = x.Product.Value<string>("description"),
                GradeId = x.Id,
                TaxLevelId = x.Product.Value<string>("taxLevelID"),
                Item = x.CloneProductItem(),
            }).Distinct().ToList();

            model.ExternalData.Products = products;

            foreach (var item in model.ExternalData.Products)
            {
                model.ExternalProducts.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetEvoBookCurrencies(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetEvoBookCurrencies);

            var currencies = model.ExternalData.CardReferences.Select(x=> new configEvoBook.Currency(x.Item.Value<int>("currencyID"))
            { 
                IsoAlpha = x.Item.Value<string>("currencyIsoAlpha"),
                IsoNumeric = x.Item.Value<int>("currencyIsoNumeric"),
                Symbol = x.Item.Value<string>("currencySymbol"),
                DecimalPlaces = x.Item.Value<int>("currencyDecimalPlaces"),
            }).Distinct().ToList();

            model.ExternalData.Currencies = currencies;

            foreach (var item in model.ExternalData.Currencies)
            {
                model.ExternalCurrencies.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> GetEvoBookCardReferences(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(GetEvoBookCardReferences);

            var apiUrl = $"{model.EndPointsConfig.ApiConfig}/v1/Tenders?c={model.BosConfig.CompanyId}&s={model.BosConfig.StoreId}";

            var result = await MakeApiCall<IEnumerable<JObject>>(() => model.ConfigHttpClient.GetAsync(apiUrl), model.LoggingReference).ConfigureAwait(false);
            if (!result.IsSuccess)
            {
                return StatusCodeResult.Specific(result.StatusCode, result.Exception);
            }

            var tenders = result.Results
                .Where(x => x.Value<string>("type").Equals("PAYMENTTERMINAL", StringComparison.OrdinalIgnoreCase))
                .Select(x=> new configEvoBook.Tender(x.Value<string>("id"))
                {
                    Description = x.Value<string>("description"),
                    Item = x
                });

            model.ExternalData.CardReferences = tenders;

            foreach (var item in model.ExternalData.CardReferences)
            {
                model.ExternalCardReferences.Add(item.Id, item);
            }

            return StatusCodeResult.Success;
        }

        #endregion

        #region Create map

        private async Task<StatusCodeResult> MapVatRates(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(MapVatRates);

            var externalData = model.ExternalData;
            var hydraData = model.HydraData;

            var prodVatRates = externalData.Products.Select(x => x.TaxLevelId).Distinct().ToList();

            foreach(var pvr in prodVatRates)
            {
                var external = externalData.VatRates.FirstOrDefault(x => x.TaxLevelId == pvr);
                var hydra = hydraData.VatRates.FirstOrDefault(x => x.Rate == external.TaxRate);

                if (hydra != null)
                {
                    model.HydraVatRates.Map[hydra.Id].Target = external;
                    model.ExternalVatRates.Map[external.TaxLevelId].Target = hydra;
                }
            }

            var missingHydra = model.HydraVatRates.Map.Values.Where(x => x.Target == null);
            foreach(var hydra in missingHydra)
            {
                var external = externalData.VatRates.FirstOrDefault(x => x.TaxRate == hydra.Source.Rate);

                if (external != null)
                {
                    model.HydraVatRates.Map[hydra.Source.Id].Target = external;
                    model.ExternalVatRates.Map[external.TaxLevelId].Target = hydra.Source;
                }
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> MapGrades(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(MapGrades);

            var externalData = model.ExternalData.Grades;
            var hydraData = model.HydraData.Grades;

            foreach(var hydra in hydraData)
            {
                var external =
                    externalData.FirstOrDefault(x => x.Description.Equals(hydra.Name, StringComparison.InvariantCultureIgnoreCase)) ??
                    externalData.FirstOrDefault(x => x.Description.Contains(hydra.Name, StringComparison.InvariantCultureIgnoreCase));

                if (external != null)
                {
                    model.HydraGrades.Map[hydra.Id].Target = external;
                    model.ExternalGrades.Map[external.Id].Target = hydra;
                }
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> MapProducts(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(MapProducts);

            var hydraData = model.HydraData.Products;
            var externalData = model.ExternalData.Products;

            foreach (var hydra in hydraData)
            {
                var external = externalData.FirstOrDefault(x => x.GradeId.Equals(hydra.Grade));

                if (external != null)
                {
                    model.HydraProducts.Map[hydra.ProductCode].Target = external;
                    model.ExternalProducts.Map[external.Id].Target = hydra;
                }
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> MapCurrencies(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(MapCurrencies);

            var hydraData = model.HydraData.Currencies;
            var externalData = model.ExternalData.Currencies;

            foreach (var hydra in hydraData)
            {
                var external = externalData.FirstOrDefault(x => x.IsoAlpha.Equals(hydra.Id, StringComparison.InvariantCultureIgnoreCase));

                if (external != null)
                {
                    model.HydraCurrencies.Map[hydra.Id].Target = external;
                    model.ExternalCurrencies.Map[external.Id].Target = hydra;
                }
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> MapCardReferences(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(MapCardReferences);

            var hydraData = model.HydraData.CardReferences;
            var externalData = model.ExternalData.CardReferences;

            foreach (var hydra in hydraData)
            {
                var external =
                    externalData.FirstOrDefault(x =>
                        (!hydra.CardExternalName.IsNullOrWhiteSpace() && x.Description.Equals(hydra.CardExternalName, StringComparison.InvariantCultureIgnoreCase)) ||
                        x.Description.Equals($"{model.BosConfig.CardRefererPrefix?.Trim()} {hydra.CardProductName}", StringComparison.InvariantCultureIgnoreCase) ||
                        x.Description.Equals(model.BosConfig.DefaultCardReferer, StringComparison.InvariantCultureIgnoreCase));

                if (external != null)
                {
                    model.HydraCardReferences.Map[hydra.CardRef].Target = external;
                    model.ExternalCardReferences.Map[external.Id].Target = hydra;
                }
            }

            return StatusCodeResult.Success;
        }

        #endregion

        #region Validation

        private async Task<StatusCodeResult> DefineSourceDefaults(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(DefineSourceDefaults);

            var hydraVAT = model.HydraData.VatRates.FirstOrDefault(x => x.Id.Equals("VAT20", StringComparison.InvariantCultureIgnoreCase));
            if (hydraVAT != null)
            {
                model.HydraVatRates.Map[hydraVAT.Id].IsDefault = true;
            }

            var hydraGrade = model.HydraData.Grades.FirstOrDefault(x => x.Name.Equals("UNLEADED", StringComparison.InvariantCultureIgnoreCase));
            if (hydraGrade != null)
            {
                model.HydraGrades.Map[hydraGrade.Id].IsDefault = true;
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> DefineTargetDefaults(HydraToEvoBookMapping model)
        {
            model.StepName = nameof(DefineTargetDefaults);

            if (!string.IsNullOrWhiteSpace(model.BosConfig.DefaultCardReferer))
            {
                var externalTender = model.ExternalData.CardReferences.FirstOrDefault(x=> x.Description.Equals(model.BosConfig.DefaultCardReferer, StringComparison.InvariantCultureIgnoreCase));
                model.ExternalCardReferences.Map[externalTender.Id].IsDefault = true;
            }

            return StatusCodeResult.Success;
        }

        private async Task<StatusCodeResult> ValidateMap(HydraToEvoBookMapping model)
        {
            bool isValid<TSourceId, TTargetId, TSource, TTarget>(IEnumerable<MappingData<TSourceId, TSource, TTarget>.MappingDataItem> source, IEnumerable<MappingData<TTargetId, TTarget, TSource>.MappingDataItem> target)
            {
                const byte maxLength = 75;

                var validSource = source.Where(x => x.Target != null);
                foreach (var item in validSource)
                {
                    var jsonSource = JsonConvert.SerializeObject(item.Source);
                    var jsonTarget = JsonConvert.SerializeObject(item.Target);
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Source.{typeof(TSource).Name}",
                        () => new[] 
                        {
                            $"Valid.Item: {jsonSource.Substring(0, Math.Min(jsonSource.Length, maxLength))}", 
                            $"Target: {jsonTarget.Substring(0, Math.Min(jsonTarget.Length, maxLength))}" 
                        }, reference: model.LoggingReference);
                }

                var invalidSource = source.Where(x => x.Target == null);
                foreach (var item in invalidSource)
                {
                    var json = JsonConvert.SerializeObject(item.Source);
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Source.{typeof(TSource).Name}", 
                        () => new[] { $"Invalid.Item: {json.Substring(0, Math.Min(json.Length, maxLength))}" }, reference: model.LoggingReference);
                }

                if (!source.Any(x => x.IsDefault))
                {
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Source.{typeof(TSource).Name}", () => new[] { "No IsDefault defined!" });
                }

                var validTarget = target.Where(x => x.Target != null);
                foreach (var item in validTarget)
                {
                    var jsonSource = JsonConvert.SerializeObject(item.Source);
                    var jsonTarget = JsonConvert.SerializeObject(item.Target);
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Target.{typeof(TTarget).Name}",
                        () => new[]
                        {
                            $"Valid.Item: {jsonSource.Substring(0, Math.Min(jsonSource.Length, maxLength))}",
                            $"Source: {jsonTarget.Substring(0, Math.Min(jsonTarget.Length, maxLength))}"
                        }, reference: model.LoggingReference);
                }

                var invalidTarget = target.Where(x => x.Target == null);
                foreach (var item in invalidTarget)
                {
                    var json = JsonConvert.SerializeObject(item.Source);
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Target.{typeof(TTarget).Name}",
                        () => new[] { $"Invalid.Item: {json.Substring(0, Math.Min(json.Length, maxLength))}" }, reference: model.LoggingReference);
                }

                if (!target.Any(x => x.IsDefault))
                {
                    DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, $"Target.{typeof(TSource).Name}", () => new[] { "No IsDefault defined!" });
                }

                return invalidSource.Any() || invalidTarget.Any();
            }

            model.StepName = nameof(ValidateMap);

            var isValidVatRates = isValid(model.HydraVatRates.Map.Values, model.ExternalVatRates.Map.Values);
            var isValidGrades = isValid(model.HydraGrades.Map.Values, model.ExternalGrades.Map.Values);
            var isValidCurrencies = isValid(model.HydraCurrencies.Map.Values, model.ExternalCurrencies.Map.Values);
            var isValidProducts = isValid(model.HydraProducts.Map.Values, model.ExternalProducts.Map.Values);
            var isValidCardReferences = isValid(model.HydraCardReferences.Map.Values, model.ExternalCardReferences.Map.Values);

            var result = isValidVatRates && isValidGrades && isValidCurrencies && isValidProducts && isValidCardReferences ? 
                StatusCodeResult.Success : 
                StatusCodeResult.Specific(HttpStatusCode.BadRequest, new Exception("IsValid: VatRates / Grades / Currencies / Products / CardReferences: " +
                    $"{!isValidVatRates} / {!isValidGrades} / {!isValidCurrencies} / {!isValidProducts} / {!isValidCardReferences}"));

            if (!result.IsSuccess)
            {
                DoDeferredLogging(Htec.Foundation.Core.LogLevel.Warn, "Validation.Status", () => new[] { result.Exception.Message });
            }

            return result.IsSuccess || !model.BosConfig.StrictConfigMappingValidation ? StatusCodeResult<HydraToEvoBookMapping>.Success(model) : StatusCodeResult<HydraToEvoBookMapping>.Specific(result.StatusCode, result.Exception);
        }

        #endregion

        /// <inheritdoc/>
        protected override IEnumerable<Func<HydraToEvoBookMapping, Task<StatusCodeResult>>> GetSteps()
        {
            return new List<Func<HydraToEvoBookMapping, Task<StatusCodeResult>>>()
            {
                GetBosConfig, GetConfigClientHttp,
                GetHydraVatRates, GetHydraGrades, GetHydraProducts, GetHydraCurrencies, GetHydraCardReferences,
                GetEvoBookVatRates, GetEvoBookGrades, GetEvoBookProducts, GetEvoBookCardReferences, GetEvoBookCurrencies,
                MapVatRates, MapGrades, MapProducts, MapCardReferences, MapCurrencies,
                DefineSourceDefaults, DefineTargetDefaults,
                ValidateMap
            };
        }
    }
}
