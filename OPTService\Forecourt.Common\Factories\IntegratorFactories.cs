﻿using System;
using Forecourt.Bos.Factories.Interfaces;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.PaymentConfiguration.Factories.Interfaces;
using Forecourt.Pos.Factories.Interfaces;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.SecondaryAuth.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Core;
using Htec.Logger.Interfaces.Tracing;

namespace Forecourt.Common.Factories
{
    /// <summary>
    /// Model that holds references to all Integrator factories
    /// </summary>
    public class IntegratorFactories : Disposable, IIntegratorFactories
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <inheritdoc cref="Disposable"/>
        public IntegratorFactories(IHtecLogger logger, IConfigurationManager configuratioManager,
            IPumpIntegratorInFactory pumpIntegratorInFactory, 
            ITankGaugeIntegratorInFactory tankGaugeIntegratorInFactory, 
            IPosIntegratorOutFactory posIntegratorOutFactory,
            IPosIntegratorInFactory posIntegratorInFactory, 
            IPosInModeFactory posInModeFactory,
            IMobilePosIntegratorInFactory mobilePosIntegratorInFactory, 
            IMobileBosIntegratorInFactory mobileBosIntegratorInFactory,
            IBosIntegratorOutFactory bosIntegratorOutFactory,
            ISecAuthIntegratorOutFactory secAuthIntegratorOutFactory,
            IPaymentConfigIntegratorFactory paymentConfigIntegratorOutFactory) :
            base(logger, configuratioManager)
        {
            if (configuratioManager == null)
            {
                throw new ArgumentNullException(nameof(configuratioManager));
            }

            PumpIntegratorInFactory = pumpIntegratorInFactory ?? throw new ArgumentNullException(nameof(pumpIntegratorInFactory));
            TankGaugeIntegratorInFactory = tankGaugeIntegratorInFactory ?? throw new ArgumentNullException(nameof(tankGaugeIntegratorInFactory));
            PosIntegratorOutFactory = posIntegratorOutFactory ?? throw new ArgumentNullException(nameof(posIntegratorOutFactory));
            PosIntegratorInFactory = posIntegratorInFactory ?? throw new ArgumentNullException(nameof(posIntegratorInFactory));
            PosInModeFactory = posInModeFactory ?? throw new ArgumentNullException(nameof(posInModeFactory));
            MobilePosIntegratorInFactory = mobilePosIntegratorInFactory ?? throw new ArgumentNullException(nameof(mobilePosIntegratorInFactory));
            MobileBosIntegratorInFactory = mobileBosIntegratorInFactory ?? throw new ArgumentNullException(nameof(mobileBosIntegratorInFactory));
            BosIntegratorOutFactory = bosIntegratorOutFactory ?? throw new ArgumentNullException(nameof(bosIntegratorOutFactory));
            SecAuthIntegratorOutFactory = secAuthIntegratorOutFactory ?? throw new ArgumentNullException(nameof(secAuthIntegratorOutFactory));
            PaymentConfigIntegratorOutFactory = paymentConfigIntegratorOutFactory ?? throw new ArgumentNullException(nameof(paymentConfigIntegratorOutFactory));
        }

        /// <inheritdoc />
        public IPumpIntegratorInFactory PumpIntegratorInFactory { get; protected set; }

        /// <inheritdoc />
        public ITankGaugeIntegratorInFactory TankGaugeIntegratorInFactory { get; protected set; }

        /// <inheritdoc />
        public IMobilePosIntegratorInFactory MobilePosIntegratorInFactory { get; protected set; }

        /// <inheritdoc />
        public IMobileBosIntegratorInFactory MobileBosIntegratorInFactory { get; protected set; }

        /// <inheritdoc />
        public IPosIntegratorOutFactory PosIntegratorOutFactory { get; protected set; }

        /// <inheritdoc />
        public IPosIntegratorInFactory PosIntegratorInFactory { get; protected set; }

        /// <inheritdoc />
        public IPosInModeFactory PosInModeFactory { get; protected set; }

        /// <inheritdoc />
        public IBosIntegratorOutFactory BosIntegratorOutFactory { get; protected set; }

        /// <inheritdoc />
        public ISecAuthIntegratorOutFactory SecAuthIntegratorOutFactory { get; protected set; }
   
        /// <inheritdoc />
        public IPaymentConfigIntegratorFactory PaymentConfigIntegratorOutFactory { get; protected set; }

        /// <inheritdoc />
        protected override void DoDisposeUnHookInstances()
        {
            PumpIntegratorInFactory = null;
            TankGaugeIntegratorInFactory = null;
            MobilePosIntegratorInFactory = null;
            MobileBosIntegratorInFactory = null;
            PosIntegratorOutFactory = null;
            PosIntegratorInFactory = null;
            PosInModeFactory = null;
            BosIntegratorOutFactory = null;
            SecAuthIntegratorOutFactory = null;
            PaymentConfigIntegratorOutFactory = null;

            base.DoDisposeUnHookInstances();
        }
    }
}
