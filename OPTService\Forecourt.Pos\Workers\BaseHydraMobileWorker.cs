﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Workers;
using Forecourt.Pos.Extensions;
using Forecourt.Pos.HydraDb.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Pos.Interfaces.Core;
using Htec.Hydra.Core.Pos.Messages;
using Htec.Hydra.Core.Pos.Messages.HydraMobile;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Xml.Linq;
using bosCore = Htec.Hydra.Core.Bos.Interfaces.Core;
using ConfigurationConstants = Forecourt.Core.Configuration.Constants;
using DayEnd = Htec.Hydra.Core.Pos.Messages.HydraMobile.DayEnd;
using GenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Pos.Workers.Interfaces.ITelemetryWorker;
using MessageGenerator = Htec.Hydra.Core.Pos.Messages.HydraMobile.MessageGenerator;
using ModeChangeType = Htec.Hydra.Core.Pos.Common.ModeChangeType;
using posIntf = Htec.Hydra.Core.Pos.Interfaces;
using Transaction = Htec.Hydra.Core.Pos.Messages.HydraMobile.Transaction;

namespace Forecourt.Pos.Workers
{
    /// <summary>
    /// Represents the base class for Hydra Mobile workers, providing common functionality and properties for handling Hydra Mobile operations.
    /// </summary>
    /// <typeparam name="T">The type of message being processed.</typeparam>
    /// <typeparam name="THydraDb">The type of Hydra database interface.</typeparam>
    /// <typeparam name="TTelemetryWorker
    public abstract class BaseHydraMobileWorker : ClientWorker<XElement, IHydraDb, ITelemetryWorker>, IHydraMobileWorker
    {
        private const string DefaultHydraMobileTransactionPoll = "00:05:00";

        /// <summary>
        /// Header constant for HydraMobile
        /// </summary>
        protected const string HeaderHydraMobile = "HMOB";

        /// <summary>
        /// Single instance pseudo OptId, for HydraMobile
        /// </summary>
        public const int HydraMobileOptId = 1024 * 8;

        /// <summary>
        /// Gets access to the Controller worker for handling notifications
        /// </summary>
        protected INotificationWorker<string> ControllerWorker => GetWorker<INotificationWorker<string>>();

        /// <summary>
        /// Get access to the transient POS integrator output worker
        /// </summary>
        protected posIntf.IPosIntegratorOutTransient<IMessageTracking> PosOut => GetWorker<posIntf.IPosIntegratorOutTransient<IMessageTracking>>();

        protected IJournalWorkerReceipt JournalWorker => GetWorker<IJournalWorkerReceipt>();

        protected IBosIntegratorOut<IMessageTracking> BosOut => GetWorker<IBosIntegratorOut<IMessageTracking>>();

        protected IOptCollection AllOpts { get; private set; }

        protected IPumpCollection AllPumps { get; private set; }

        protected IGradeHelper GradeHelper { get; private set; }

        protected IDictionary<int, string> PumpTxnNumbers { get; } = new ConcurrentDictionary<int, string>();

        /// <summary>
        /// Configurable value for, SecAuth Timed Out Response
        /// </summary>
        protected ConfigurableBool ConfigValueSecAuthTimedOutResponse { get; set; }

        /// <inheritdoc />
        public DateTime ShiftEndTime => DateTime.MinValue;

        /// <inheritdoc />
        public DateTime DayEndTime => DateTime.MinValue;

        protected ConcurrentDictionary<byte, HoseTotals> CurrentInfo { get; } = new ConcurrentDictionary<byte, HoseTotals>();

        /// <inheritdoc/>
        private const string ConfigKeyMaxRetalixTransactionNumber = ConfigurationConstants.CategoryNamePOS + ConfigurationConstants.CategorySeparator + ConfigurationConstants.ConfigKeyMaxTransNumber;

        protected ConfigurableUInt ConfigValueMaxRetalixTransactionNumber { get; }

        #region Initialisation

        /// <inheritdoc cref="ClientWorker{T, THydraDb, TTelemetryWorker}"/>
        protected BaseHydraMobileWorker(IHtecLogManager logMan, IHydraDb hydraDb, IConfigurationManager configurationManager, ITelemetryWorker telemetryWorker, IClientConnectionThread<XElement> connectionThread,
            posIntf.IPosIntegratorOutTransient<IMessageTracking> posOut, IJournalWorkerReceipt journalWorker, IBosIntegratorOut<IMessageTracking> bosOut, IOptCollection allOpt, IPumpCollection allPumps,
            IGradeHelper gradeHelper, ISecAuthIntegratorOutTransient<IMessageTracking> secAuthOut, ITimerFactory timerFactory) :
            base(logMan, "HydraMobile", telemetryWorker, connectionThread, hydraDb, configurationManager, timerFactory, DefaultHydraMobileTransactionPoll)
        {
            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            ConfigValueSecAuthTimedOutResponse = new ConfigurableBool(this, ConfigurationConstants.ConfigKeyPrefixSecAuthTimedOutResponse, ConfigurationConstants.DefaultValueSecAuthTimedOutResponse);
            ConfigValueMaxRetalixTransactionNumber = new ConfigurableUInt(this, ConfigurationConstants.CategoryNamePOS + ConfigurationConstants.CategorySeparator + ConfigurationConstants.ConfigKeyMaxTransNumber, ConfigurationConstants.DefaultMaxTransactionNumber);

            RegisterWorker(posOut ?? throw new ArgumentNullException(nameof(posOut)));
            RegisterWorker(journalWorker ?? throw new ArgumentNullException(nameof(journalWorker)));
            RegisterWorker(bosOut ?? throw new ArgumentNullException(nameof(bosOut)));
            RegisterWorker(secAuthOut ?? throw new ArgumentNullException(nameof(secAuthOut)));

            AllOpts = allOpt ?? throw new ArgumentNullException(nameof(allOpt));
            AllPumps = allPumps ?? throw new ArgumentNullException(nameof(allPumps));

            GradeHelper = gradeHelper ?? throw new ArgumentNullException(nameof(gradeHelper));
        }

        /// <inheritdoc />
        protected override GenericEndPoint DoGetEndPoint()
        {
            return HydraDb.FetchHydraMobileEndPoint();
        }
        #endregion

        #region Actions

        /// <inheritdoc />
        public Result RequestStatus(byte pump, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return SendRequest(MessageGenerator.ConstructPumpStatusRequestMessage(pump), message);
        }

        /// <inheritdoc />
        public Result RequestStatus(IPump pump, string loggingReference = null)
        {
            var message = new MessageTracking { IdAsString = loggingReference };
            return RequestStatus(pump.Number, message);
        }

        private static Result<StatusCodeResult> MapResult(Result result) => !result.IsSuccess ? Result.Success(StatusCodeResult.Specific(HttpStatusCode.BadRequest)) : Result.Success(StatusCodeResult.Success);

        Result<StatusCodeResult> IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result<StatusCodeResult>>.RequestStatus(byte pump, IMessageTracking message)
        {
            return MapResult(RequestStatus(pump, message));
        }

        Result<StatusCodeResult> IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result<StatusCodeResult>>.RequestStatus(IPump pump, string loggingReference)
        {
            return MapResult(RequestStatus(pump.Number, loggingReference.ToMessageTracking()));
        }

        /// <inheritdoc />
        public Result RequestReceipt(byte pump, IMessageTracking message = null)
        {
            return SendRequest(MessageGenerator.ConstructPumpReceiptRequestMessage(pump), message);
        }

        /// <inheritdoc />
        public Result<StatusCodeResult> RequestReceipt(GetReceiptRequest request, IMessageTracking message = null)
        {
            return MapResult(RequestReceipt(request.Pump, message));
        }

        /// <inheritdoc />
        public Result PreAuthReponse(SecAuthResponse response, IMessageTracking message)
        {
            return PostAuthResponse(response, message);
        }

        /// <inheritdoc />
        public Result PostAuthResponse(SecAuthResponse response, IMessageTracking message)
        {
            message ??= new MessageTracking();
            return DoAction(() =>
            {
                NotificationWorker?.SendInformation($"Sending SecAuth {(response.IsAuthorised ? "Approved" : "Rejected")} to Hydra Mobile");
                return response.IsAuthorised ?
                    SendRequest(MessageGenerator.ConstructPumpAuthorisationCommandMessage(response.Pump), message) :
                    SendRequest(MessageGenerator.ConstructPumpCancellationCommandMessage(response.Pump), message); ;
            }, message.FullId);
        }

        /// <inheritdoc />
        public Result RequestTimedOut(SecAuthResponse response, IMessageTracking message)
        {
            response.IsAuthorised = ConfigValueSecAuthTimedOutResponse.GetValue();
            return PostAuthResponse(response, message);
        }

        /// <inheritdoc />
        public Result RequestDayEnd(IMessageTracking message = null)
        {
            return SendRequest(MessageGenerator.ConstructDayEndCommandMessage(), message);
        }

        /// <inheritdoc />
        public Result RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message = default)
        {
            return Result.Success();
        }

        Result<StatusCodeResult> IPosIntegratorInMode<IMessageTracking, Result<StatusCodeResult>>.RequestModeChange(byte pump, ModeChangeType mode, IMessageTracking message)
        {
            return Result.Success(StatusCodeResult.Success);
        }

        /// <inheritdoc />
        protected override Result<XElement> DoOnMessageReceived(IMessageTracking<XElement> message)
        {            
            var controllerWorker = ControllerWorker;
            var request = message.Request;
            var result = Result.Failure<XElement>("Unknown Message");

            DoDeferredLogging(LogLevel.Info, "Command.Xml", () => new[] {request.ToString(SaveOptions.DisableFormatting)});

            try
            {
                var response = MessageParser.GetInstance(request);
                string sendInfo = null;

                if (response is Htec.Hydra.Core.Pos.Messages.HydraMobile.Status responseStatus)
                {
                    AllPumps.UpdateParentId((byte)responseStatus.Pump, message);

                    sendInfo = $"{responseStatus.GetType().Name}:- Pump: {responseStatus.Pump}; State: {responseStatus.PumpState}; IsClosed: {responseStatus.IsClosed}; IsOffline: {responseStatus.IsOffline}";

                    var resultStatus = DoOnMessageReceived_Status(message, responseStatus, sendInfo);
                    if (!resultStatus.IsSuccess)
                    {
                        result = Result.Failure<XElement>(resultStatus.Error);
                    }
                    else
                    {
                        PosOut.StatusResponse(resultStatus.Value.Item2, message);

                        if (responseStatus.PumpState == $"{PumpState.Reserved}")
                        {
                            var secAuthRequest = new SecAuthRequest()
                            {
                                Pump = (byte)responseStatus.Pump,
                                TransactionId = message.ParentIdAsString,
                                MessageId = message.IdAsString,
                                TimeStamp = DateTime.UtcNow,
                            };
                            GetWorker<ISecAuthIntegratorOutTransient<IMessageTracking>>()?.PostAuthRequest(secAuthRequest, message);
                        }
                    }
                }
                else if (response is ReceiptData responseReceipt)
                {
                    AllPumps.UpdateParentId((byte)responseReceipt.Pump, message);

                    sendInfo = responseReceipt.Receipt == null ?
                        $"{responseReceipt.GetType().Name}:- Pump: {responseReceipt.Pump}; No receipt data" :
                        $"{responseReceipt.GetType().Name}:- Pump: {responseReceipt.Pump}; Number: {responseReceipt.Receipt.Number}; Title: {responseReceipt.Receipt.Title}; Lines: {string.Join(" ", responseReceipt.Receipt.Lines)}";

                    result = DoOnMessageReceived_Receipt(message, responseReceipt);

                    PosOut.ReceiptResponse((byte)responseReceipt.Pump, message);
                }
                else if (response is Transaction responseTransaction)
                {
                    AllPumps.UpdateParentId((byte)responseTransaction.Pump, message);

                    sendInfo = $"{responseTransaction.GetType().Name}:- Pump: {responseTransaction.Pump}; Time: {responseTransaction.TimeStamp:yyyy-MM-ddTHH:mm:ss}; " +
                        $" Amount: £{responseTransaction.TotalAmount:F2}; CardCircuit: {responseTransaction.CardCircuit}";

                    result = DoOnMessageReceived_Transaction(message, responseTransaction);
                }
                else if (response is DayEnd responseDayEnd)
                {
                    controllerWorker?.SendInformation($"Raw Xml: {JsonConvert.SerializeObject(response)}");
                    sendInfo = $"{responseDayEnd.GetType().Name}:- Number: {responseDayEnd.ShiftNumber}; Start: {responseDayEnd.StartTime:F}; End: {responseDayEnd.EndTime:F}";
                    var bosDayEnd = responseDayEnd.CastTo();
                    BosOut.DayEndResponse(bosDayEnd.ConvertTo(), Enumerable.Empty<ItemSalesItem>(), Enumerable.Empty<CategorySalesItem>(), Enumerable.Empty<CardSalesItem>(),
                        Enumerable.Empty<CardAmountSalesItem>(), Enumerable.Empty<CardVolumeSalesItem>(), message);
                    
                    result = DoOnMessageReceived_DayEnd(message, responseDayEnd);
                }

                if (!sendInfo.IsNullOrWhiteSpace())
                {
                    controllerWorker?.SendInformation($"Hydra Mobile Command received: {sendInfo}");
                    //controllerWorker?.SendInformation($"Raw Xml: {JsonConvert.SerializeObject(response)}");
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] {ex.Message}, ex);
                return Result.Failure<XElement>(ex.Message);
            }

            return result;
        }

        /// <inheritdoc />
        public Result RequestDefaultMode(string loggingReference = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result RequestTimeModeChange(Htec.Hydra.Core.Pos.Common.TimeModeChangeType mode, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result RequestShiftEnd(IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result<TransactionItem> RequestTransaction(Htec.Hydra.Core.Bos.Messages.IdInfo transaction, IMessageTracking message = null)
        {
            return Result.Success<TransactionItem>(default);
        }

        /// <inheritdoc />
        public Result ModeChangedResponse(byte pump, ModeChangeType mode, Htec.Hydra.Core.Pos.Common.PumpOptType previousState, IMessageTracking message = null, bool suppressModeChange = false)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result ReceiptResponse(ReceiptInfo receipt, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result ReceiptResponse(byte pump, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result StatusResponse(StatusResponse status, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result StatusResponse(byte pump, IMessageTracking message = null)
        {
            return Result.Success();
        }

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestDayEnd(IMessageTracking message)
        {
            var result = RequestDayEnd(message);
            return ResultHelpers.MapResult(result);
        }

        Result<StatusCodeResult> bosCore.IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>.RequestShiftEnd(IMessageTracking message)
        {
            var result = RequestShiftEnd(message);
            return ResultHelpers.MapResult(result);
        }

        Result IPosIntegratorInTransient<IMessageTracking, IPump, ReceiptInfo, StatusResponse, Result>.RequestReceipt(GetReceiptRequest request, IMessageTracking message)
        {
            var result = RequestReceipt(request, message);
            return !result.IsSuccess ? Result.Failure(result.Error) :
                result.Value == null ? Result.Failure("Invalid result") :
                result.Value.IsSuccess ? Result.Failure(result.Value.Exception.Message) : Result.Success();
        }

        /// <inheritdoc />
        public virtual void OnPumpState(Htec.Hydra.Core.Pump.Messages.PumpStateChange pumpState, IMessageTracking message = null)
        {
            var disp = pumpState.PumpData.Dispenser;

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "CurrentInfo.FpId", () => new[] { $"{disp.Number}; {JsonConvert.SerializeObject(disp.CurrentInfo)}"});
            CurrentInfo.AddOrUpdate(disp.Number, disp.CurrentInfo, (key, oldValue) => disp.CurrentInfo);
        }

        #endregion

        /// <summary>
        /// Handles the processing of received status message, updates the pump state and sends the appropriate response.
        /// </summary>
        /// <param name="message">The message tracking object containing the received XML message.</param>
        /// <param name="status">The status information associated with the message.</param>
        /// <param name="sendInfo">Additional information about the sender or the context of the message.</param>
        /// <returns>
        /// A <see cref="Result{T}" /> containing a tuple with the processed XML element and status response or an error.
        /// </returns>
        protected virtual Result<(XElement, StatusResponse)> DoOnMessageReceived_Status(IMessageTracking<XElement> message, Htec.Hydra.Core.Pos.Messages.HydraMobile.Status status, string sendInfo)
        {
            return Result.Success<(XElement, StatusResponse)> ((null, null));
        }

        /// <summary>
        /// Handles the processing of a received receipt message for Hydra Mobile.
        /// </summary>
        /// <param name="message">The message tracking object containing the received XML message.</param>
        /// <param name="responseReceipt">The receipt data associated with the message.</param>
        /// <returns>
        /// A <see cref="Result{T}"/> containing the processed XML element or an error.
        /// </returns>
        protected virtual Result<XElement> DoOnMessageReceived_Receipt(IMessageTracking<XElement> message, ReceiptData responseReceipt)
        {
            return Result.Success<XElement>(null);
        }

        /// <summary>
        /// Handles the processing of a received transaction message for Hydra Mobile.
        /// </summary>
        /// <param name="message">The message tracking object containing the received XML message.</param>
        /// <param name="transaction">The transaction data associated with the message.</param>
        /// <returns>
        /// A <see cref="Result{T}"/> containing the processed XML element or an error.
        /// </returns>
        protected virtual Result<XElement> DoOnMessageReceived_Transaction(IMessageTracking<XElement> message, Transaction transaction)
        {
            return Result.Success<XElement>(null);
        }

        /// <summary>
        /// Handles the processing of a received day-end message for Hydra Mobile.
        /// </summary>
        /// <param name="message">The message tracking object containing the received XML message.</param>
        /// <param name="dayEnd">The day-end data associated with the message.</param>
        /// <returns>
        /// A <see cref="Result{T}"/> containing the processed XML element or an error.
        /// </returns>
        protected virtual Result<XElement> DoOnMessageReceived_DayEnd(IMessageTracking<XElement> message, DayEnd dayEnd)
        {
            return Result.Success<XElement>(null);
        }
    }
}