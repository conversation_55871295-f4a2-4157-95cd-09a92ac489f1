﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Configuration;
using Forecourt.Core.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Models;
using Forecourt.Pump.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Workers;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Hydra.Core.Pump.Messages.Doms.State;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Logger.Interfaces;
using OPT.Common;
using PSS_Forecourt_Lib;
using PSS_TcpIp_Lib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using coreMessages = Htec.Hydra.Core.Pump.Messages;
using domsForecourt = PSS_Forecourt_Lib.Forecourt;
using Transaction = Htec.Hydra.Core.Pump.Messages.Transaction;

namespace Forecourt.Pump.DomsSetupClasses
{
    [HasConfiguration]
    public class DomsSetup : Queueable<byte, coreMessages.PumpData>, IDomsSetup
    {
        private Queueable<byte, (DomsSetupPump, IDomsSetup, DomsState, (DomsMessageCode, byte))> _fetchStatePumpsQueue;

        private SemaphoreLockManager<byte, LockData> _domsPumpLocks;

        private ITimerWrapper _timerFuellingData;
        private ITimerWrapper _timerHeartbeat;
        private ITimerWrapper _timerPruneCashedOutState;

        /// <summary>
        /// Config key for, whether the Rx/Tx details is logged.  Suffix for uniqueness.
        /// // TODO: This should be "ConnectionThread:Log:RxTx:DOMS" but the values are NOT being read properly - one for another day! So...
        /// </summary>
        public const string ConfigKeyLogRxTx = Htec.Foundation.Connections.Common.Constants.ConfigKeyCategoryConnectivity + "DomsSetup:Log:RxTx:DOMS";

        /// <summary>
        /// Default value for, whether the Rx/Tx details is logged.
        /// </summary>
        public const bool DefaultValueLogRxTx = false;

        private ForecourtClass _fc;
        private IDomsPos DomsPos => _fc;
        private domsForecourt Forecourt => _fc;
        private IFCConfig Config => _fc;
        private readonly PSSTcpIpInterface _tcpIp1;
        private readonly PSSTcpIpInterface _tcpIp2;
        private readonly PSSTcpIpInterface _tcpIp4;
        private readonly PSSTcpIpInterface _tcpIp5;
        private IPSSTcpIpInterface TcpIp1 => _tcpIp1;
        private IPSSTcpIpInterface TcpIp2 => _tcpIp2;
        private IPSSTcpIpInterface TcpIp4 => _tcpIp4;
        private IPSSTcpIpInterface TcpIp5 => _tcpIp5;
        public bool Enabled { get; private set; } = false;
        public bool Reload { get; private set; } = false;
        private bool _updateSetup;

        public bool Connected => (_connectedApc1 || _sender == null) && (_connectedApc2 || _sender2 == null) &&
                                 (_connectedApc4 || _sender4 == null) && (_connectedApc5 || _sender5 == null);

        protected INotificationWorker<EventType> ControllerWorker => GetWorker<INotificationWorker<EventType>>();

        protected IPumpControllerCallbacks PumpControllerCallbacks => GetWorker<IPumpControllerCallbacks>();

        protected IPumpCollection CorePumps { get; private set; }

        private readonly IDictionary<byte, DomsSetupGrade> _grades = new ConcurrentDictionary<byte, DomsSetupGrade>();
        private readonly IDictionary<byte, DomsSetupPump> _pumps = new ConcurrentDictionary<byte, DomsSetupPump>();
        private IEnumerable<byte> _installedPumps = new ConcurrentBag<byte>();
        private IEnumerable<byte> _installedEpts = new ConcurrentBag<byte>();
        private IEnumerable<byte> _installedTankGauges = new ConcurrentBag<byte>();
        private IEnumerable<byte> _installedPricePoles = new ConcurrentBag<byte>();
        private readonly byte _posOpt;
        private readonly byte _posId;
        private bool _fcInit = false;
        private bool _tcpInit1 = false;
        private bool _tcpInit2 = false;
        private bool _tcpInit4 = false;
        private bool _tcpInit5 = false;
        private bool _fcDone = false;
        private bool _tcpDone1 = false;
        private bool _tcpDone2 = false;
        private bool _tcpDone4 = false;
        private bool _tcpDone5 = false;
        private bool _connectedApc1 = false;
        private bool _connectedApc2 = false;
        private bool _connectedApc4 = false;
        private bool _connectedApc5 = false;

        public byte PriceSetId { get; private set; } = 0;
        public DateTime PriceSetTimestamp { get; private set; } = DateTime.MinValue;

        public string SoftwareVersion { get; private set; } = null;
        public string SoftwareDate { get; private set; } = null;

        private List<byte> _allInstalledPumps => _installedPumps.ToList();
        private List<byte> _allInstalledTankGauges => _installedTankGauges.ToList();

        public byte NumberOfPumps => (byte)_allInstalledPumps.Count();

        public bool GotMaxPumps => _allInstalledPumps.Any();

        private readonly IDomsMessageSender _sender;
        private readonly IDomsMessageSender _sender2;
        private readonly IDomsMessageSender _sender4;
        private readonly IDomsMessageSender _sender5;
        private readonly IDomsMessageReader _reader;

        protected DomsSetup(IPumpControllerCallbacks callbacks, byte posOpt, byte posId, IHtecLogManager logManager, bool update, IConfigurationManager configurationManager, IPumpCollection corePumps,
            bool processMessages = false, ITimerFactory timerFactory = null, string defaultTimerInterval = "00:00:01") : base(logManager, nameof(DomsSetup), configurationManager, timerFactory: timerFactory, defaultTimerInterval: defaultTimerInterval)
        {
            RegisterWorker(callbacks ?? throw new ArgumentNullException(nameof(callbacks)));

            _posOpt = posOpt;
            _posId = posId;
            _reader = processMessages ? new DomsMessageReader(this, logManager, update, configurationManager) : null;
            _sender = processMessages ? new DomsMessageSender(DomsPos, _reader, _posId, logManager, configurationManager) : null;
            _sender2 = null;
            _sender4 = null;
            _sender5 = null;
            _tcpIp1 = null;
            _tcpIp2 = null;
            _tcpIp4 = null;
            _tcpIp5 = null;

            DoCtor(update, corePumps, processMessages, timerFactory);
        }

        void DoCtor(bool update, IPumpCollection corePumps, bool processMessages, ITimerFactory timerFactory)
        {
            ConfigValueDOMSServiceModeKioskOnly = new ConfigurableInt(this, ConfigKeyDOMSServiceModeKioskOnly, DefaultValueDOMSServiceModeKioskOnly);
            ConfigValueDOMSServiceModeKioskUse = new ConfigurableInt(this, ConfigKeyDOMSServiceModeKioskUse, DefaultValueDOMSServiceModeKioskUse);
            ConfigValueDOMSServiceModeOptOnly = new ConfigurableInt(this, ConfigKeyDOMSServiceModeOptOnly, DefaultValueDOMSServiceModeOptOnly);
            ConfigValueDOMSServiceModeMixed = new ConfigurableInt(this, ConfigKeyDOMSServiceModeMixed, DefaultValueDOMSServiceModeMixed);
            ConfigValueDOMSAlwaysQueueMessages = new ConfigurableBool(this, ConfigKeyPrefixDOMSAlwaysQueueMessages, DefaultValueDOMSAlwaysQueueMessages);

            ConfigValueSendResponseIntervalReserve = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalReserve, DefaultValueSendResponseIntervalReserve);
            ConfigValueSendResponseIntervalAuthorise = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalAuthorise, DefaultValueSendResponseIntervalAuthorise);
            ConfigValueSendResponseIntervalCancelAuthorise = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalCancelAuthorise, DefaultValueSendResponseIntervalCancelAuthorise);
            ConfigValueSendResponseIntervalClear = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalClear, DefaultValueSendResponseIntervalClear);
            ConfigValueSendResponseIntervalClaim = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalClaim, DefaultValueSendResponseIntervalClaim);
            ConfigValueSendResponseIntervalLogon = new ConfigurableTimeSpan(this, ConfigKeyPrefixSendResponseIntervalLogon, DefaultValueSendResponseIntervalLogon);

            _updateSetup = update;
            CorePumps = corePumps;

            if (processMessages)
            {
                _fetchStatePumpsQueue = new FetchStatePumpsQueue(base.LogManager, base.ConfigurationManager);
                _domsPumpLocks = new SemaphoreLockManager<byte, LockData>(Logger, ShutdownToken);

                if (timerFactory != null)
                {
                    _timerFuellingData = timerFactory.GetTimer(TimeSpan.FromSeconds(1).TotalMilliseconds);
                    _timerFuellingData.Elapsed += OnEventElapsedEventFuelling;

                    _timerHeartbeat = timerFactory.GetTimer((GetWorker<IPumpControllerCallbacks>()?.HeartbeatInterval ?? TimeSpan.FromSeconds(10)).TotalMilliseconds);
                    _timerHeartbeat.Elapsed += OnEventElapsedEventHeartbeat;

                    _timerPruneCashedOutState = timerFactory.GetTimer((GetWorker<IPumpControllerCallbacks>()?.TransactionCashedOutStateCheckInterval ?? TimeSpan.FromHours(1)).TotalMilliseconds);
                    _timerPruneCashedOutState.Elapsed += OnEventElapsedEventCheckTransactionCashedOutState;
                }
            }
        }

        private void OnEventElapsedEventCheckTransactionCashedOutState(object sender, ElapsedEventArgs e)
        {
            for (var i = 0; i < NumberOfPumps; i++)
            {
                Pump((byte)(i + 1)).ClearCashedOutState(TimeSpan.Zero);
            }
        }

        protected DomsSetup(IPumpControllerCallbacks callbacks, byte posOpt, byte posId1, byte posId2, byte posId4, byte posId5, IHtecLogManager logManager, bool update, IConfigurationManager configurationManager,
            IPumpCollection allPumps, bool processMessages = false, ITimerFactory timerFactory = null, string defaultTimerInterval = "00:00:01") :
            base(logManager, nameof(DomsSetup), configurationManager, timerFactory: timerFactory, defaultTimerInterval: defaultTimerInterval)
        {
            RegisterWorker(callbacks ?? throw new ArgumentNullException(nameof(callbacks)));

            try
            {
                _tcpIp1 = new PSSTcpIpInterface();
                if (update)
                {
                    _tcpIp2 = new PSSTcpIpInterface();
                    _tcpIp4 = new PSSTcpIpInterface();
                    _tcpIp5 = new PSSTcpIpInterface();
                }
                else
                {
                    _tcpIp2 = null;
                    _tcpIp4 = null;
                    _tcpIp5 = null;
                }
            }
            // ReSharper disable once EmptyGeneralCatchClause
            catch
            {
            }

            _posOpt = posOpt;
            _posId = posId1;
            _reader = new DomsMessageReader(this, logManager, update, ConfigurationManager);
            _sender = new DomsMessageSender(TcpIp1, _reader, posId1, 1, logManager, ConfigurationManager);
            if (update)
            {
                _sender2 = new DomsMessageSender(TcpIp2, _reader, posId2, 2, logManager, ConfigurationManager);
                _sender4 = new DomsMessageSender(TcpIp4, _reader, posId4, 4, logManager, ConfigurationManager);
                _sender5 = new DomsMessageSender(TcpIp5, _reader, posId5, 5, logManager, ConfigurationManager);
            }
            else
            {
                _sender2 = null;
                _sender4 = null;
                _sender5 = null;
            }

            _fc = null;

            DoCtor(update, allPumps, processMessages, timerFactory);
        }

        protected override void OnEventElapsedEvent(object sender, ElapsedEventArgs e)
        {
            DoProcessOnPumpData();
        }

        private void OnEventElapsedEventFuelling(object sender, ElapsedEventArgs e)
        {
            DoProcessFuellingData();
        }

        private void OnEventElapsedEventHeartbeat(object sender, ElapsedEventArgs e)
        {
            RequestHeartbeat();
        }

        protected override Result DoStart(params object[] startParams)
        {
            var result = base.DoStart(startParams);
            _timerFuellingData?.Start();
            _timerHeartbeat?.Start();
            _timerPruneCashedOutState?.Start();
            return result;
        }

        protected override Result DoStop()
        {
            _timerPruneCashedOutState?.Stop();
            _timerHeartbeat?.Stop();
            _timerFuellingData?.Stop();
            return base.DoStop();
        }

        public DomsSetupPump Pump(byte fpId)
        {
            if (!_pumps.ContainsKey(fpId))
            {
                _pumps[fpId] = new DomsSetupPump(fpId);
                SetCleared(fpId);
            }

            return _pumps[fpId];
        }

        public DomsSetupGrade Grade(byte gradeId)
        {
            if (!_grades.ContainsKey(gradeId))
            {
                _grades[gradeId] = new DomsSetupGrade(gradeId);
            }

            return _grades[gradeId];
        }

        public IList<byte> AllPriceGroups()
        {
            IEnumerable<byte> result = new List<byte>();
            foreach (DomsSetupGrade grade in _grades.Values)
            {
                result = result.Union(grade.AllPriceGroups());
            }

            return result.ToList();
        }

        public IList<byte> AllGradeIds() => !_grades.Any() ? new List<byte>() : _grades.Where(x => !x.Value.IsEmpty()).Select(x => x.Key).ToList();

        private IList<DomsSetupGrade> AllGrades() => !_grades.Any() ? new List<DomsSetupGrade>() : _grades.Where(x => !x.Value.IsEmpty()).Select(x => x.Value).ToList();

        public IList<DomsSetupPump> AllPumps(bool checkIsEmpty = false) => !_pumps.Any() ? new List<DomsSetupPump>() : _pumps.Where(x => !(checkIsEmpty && x.Value.IsEmpty())).Select(x => x.Value).ToList();

        public void SetGradePrice(byte gradeId, byte priceGroup, uint price)
        {
            Grade(gradeId).SetPrice(priceGroup, price);
        }

        public void SetPumpGradePrice(byte fpId, byte gradeId, uint price)
        {
            var pump = Pump(fpId);
            pump.SetGradePrice(gradeId, price);

            var hoses = pump.AllGradeOptions().Select(x => pump.OptionInfo(x)).Where(x => x.Item1 == gradeId && x.Item2 != 0).Select(x => x.Item2).ToList();
            PumpControllerCallbacks.OnGradePriceChange(fpId, gradeId, Grade(gradeId).Name.Trim(), price, hoses);
        }

        public void SetGradeName(byte gradeId, string name)
        {
            Grade(gradeId).Name = name;
        }

        public void SetGradeColour(byte gradeId, string colour)
        {
            Grade(gradeId).Colour = colour;
        }

        public void SetGradeOption(byte fpId, byte gradeOption, byte gradeId, byte hoseId, byte tankId, byte tankParts)
        {
            Pump(fpId).SetGradeOption(gradeOption, gradeId, hoseId == 0 ? gradeOption : hoseId, tankId, tankParts);
        }

        public void SetPumpIpAddressAndPort(byte fpId, string ipAddress, ushort port)
        {
            Pump(fpId).SetIpAddressAndPort(ipAddress, port);
        }

        public void SetPumpPhysicalAddress(byte fpId)
        {
            Pump(fpId).SetPhysicalAddress();
        }

        public void SetPumpDecimalPositionInMoney(byte fpId, byte position)
        {
            Pump(fpId).SetDecimalPositionInMoney(position);
        }

        public void ClearPumpDecimalPositionInMoney(byte fpId)
        {
            Pump(fpId).ClearDecimalPositionInMoney();
        }

        public void SetPriceSetId(byte priceSetId)
        {
            PriceSetId = priceSetId;
        }

        public void SetPriceSetTimestamp(DateTime timestamp)
        {
            PriceSetTimestamp = timestamp;

            PumpControllerCallbacks.OnNewPricesAvailable(timestamp);
        }

        private uint IntPrice(byte gradeId, byte priceGroup) => _grades.ContainsKey(gradeId) ? _grades[gradeId].Price(priceGroup) : 0;
        public float Price(byte gradeId, byte priceGroup) => IntPrice(gradeId, priceGroup) / (float)PumpWorker.VolumeFactor;

        public string GradeName
            (byte gradeId) => _grades.ContainsKey(gradeId) && !string.IsNullOrEmpty(_grades[gradeId].Name)
            ? _grades[gradeId].Name
            : $"GRADE {gradeId:D2}";

        public string GradeColour
            (byte gradeId) => _grades.ContainsKey(gradeId) && !string.IsNullOrEmpty(_grades[gradeId].Colour)
            ? _grades[gradeId].Colour
            : "FFFFFF";

        public void SendToDoms(bool withClear)
        {
            _sender.FcPriceSetRequest(PriceSetId, AllPriceGroups(), AllGrades());

            foreach (byte gradeId in AllGradeIds())
            {
                _sender.SetGradeTextRequest(gradeId, GradeName(gradeId));
                _sender.SetGradeColourRequest(gradeId, GradeColour(gradeId));
            }


            if (withClear)
            {
                _sender.FetchFcInstallStatusRequest();
                foreach (var fpId in _allInstalledPumps)
                {
                    _sender.FpClearRequest(Pump(fpId));
                }
            }

            foreach (DomsSetupPump pump in AllPumps())
            {
                _sender.FpInstallRequest(pump);
            }

            ControllerWorker?.SendInformation("DOMS - New setup sent");
        }

        /// <inheritdoc/>
        public Result FetchFromDoms(FetchFcRequestType fcRequestType = FetchFcRequestType.All, FetchFpRequestType fpRequestType = FetchFpRequestType.All, byte? fpId = null)
        {
            if (!Enabled || !Connected)
            {
                return Result.Failure(ConfigConstants.NotConnected);
            }

            if (fcRequestType != FetchFcRequestType.None)
            {
                if (fcRequestType.HasFlag(FetchFcRequestType.PriceSet))
                {
                    _sender.FetchFcPriceSetRequest();
                }
                if (fcRequestType.HasFlag(FetchFcRequestType.GradeText))
                {
                    _sender.FetchFcGradeTextRequest();
                }
                if (fcRequestType.HasFlag(FetchFcRequestType.GradeColour))
                {
                    _sender.FetchFcGradeColourRequest();
                }
                if (fcRequestType.HasFlag(FetchFcRequestType.InstallStatus))
                {
                    _sender.FetchFcInstallStatusRequest();
                }
            }

            if (fpRequestType != FetchFpRequestType.None)
            {
                foreach (var id in _allInstalledPumps.Where(x => fpId == 0 || x == fpId))
                {
                    Task.Run(() =>
                    {
                        var pump = Pump(id);

                        if (fpRequestType.HasFlag(FetchFpRequestType.InstallData))
                        {
                            _sender.FetchFpInstallDataRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.Info))
                        {
                            _sender.FetchFpInfoRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.Status))
                        {
                            _sender.FetchFpStatusRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.SupTransBufferStatus))
                        {
                            _sender.FetchFpSupTransBufferStatusRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.UnSupTransBufferStatus))
                        {
                            _sender4.FetchFpUnSupTransBufferStatusRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.MeterStatus))
                        {
                            _sender.FetchFpMeterStatusRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.FuellingData))
                        {
                            _sender.FetchFpFuellingDataRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.Error))
                        {
                            _sender.FetchFpErrorRequest(pump);
                        }
                        if (fpRequestType.HasFlag(FetchFpRequestType.DipStatus))
                        {
                            // TODO: see ADO #648209
                            //_sender.FetchFpXxxRequest(pump);
                        }
                    });
                }
            }

            foreach (var tgId in _allInstalledTankGauges)
            {
                //GetLogger().Info($"DOMS - Tank Gauge {tgId}");
            }

            return Result.Success();
        }

        /// <inheritdoc/>
        public void OpenPump(byte fpId)
        {
            if (!Enabled || !Connected)
            {
                return;
            }

            _sender.OpenFpRequest(Pump(fpId));
        }

        /// <inheritdoc/>
        public void ClosePump(byte fpId)
        {
            if (!Enabled || !Connected)
            {
                return;
            }

            _sender.CloseFpRequest(Pump(fpId));
        }

        /// <inheritdoc/>
        public bool SendOneLogin(string accessCode, uint countryCode, string posVersionId, IList<DomsMessageId> unsolMsgs)
        {
            if (TcpIp1 != null && Connected)
            {
                return _sender.LoginRequest(accessCode, countryCode, posVersionId, unsolMsgs);
            }

            return false;
        }

        public bool InitialiseTcpConnection(byte apc, IPSSTcpIpInterface Itcp, PSSTcpIpInterface tcp, _IPSSTcpIpInterfaceEvents_MessageReceivedEventHandler received, string ipAddress, bool isSolicitored = true)
        {
            Itcp.Hostname = ipAddress;
            tcp.MessageReceived += received;
            tcp.ConnectionLost += _tcpIpXxx_ConnectionLost;
            ControllerWorker?.SendInformation($"DOMS - APC is {apc}, TCP Protocol is {Itcp.Protocol}, RxTimeout is {Itcp.RxTimeOut}, Port Base is {Itcp.PortBase}");
            Itcp.set_APCs(ProtocolTypes.PROTOCOL_DOMS_POS, new byte[] { apc });
            if (isSolicitored)
            {
                Itcp.MakeSolicited(apc);
            }

            try
            {
                Itcp.Initialize(ConnectWhen.CONNECT_LATER);
                return true;
            }
            catch (Exception ex)
            {
                DoDeferredLogging(Htec.Foundation.Core.LogLevel.Error, HeaderException, () => new[] { $"Error initialising TCP/IP for APC {apc}" }, ex);
                ClearDownTcpConnection(apc);
            }

            return false;
        }

        /// <inheritdoc/>
        public bool LoginDoms(string ipAddress, string accessCode, string accessCode2, string accessCode4, string accessCode5, uint countryCode, string posVersionId,
            IList<DomsMessageId> unsolMsgs, IList<DomsMessageId> unsolMsgs2, IList<DomsMessageId> solMsgs4, IList<DomsMessageId> unsolMsgs5)
        {
            if (!Enabled)
            {
                return false;
            }

            try
            {
                if (_fc == null)
                {
                    _fc = new ForecourtClass();
                }
            }
            catch (Exception ex)
            {
                DoDeferredLogging(LogLevel.Error, HeaderException, () => new[] { ex.Message }, ex);
            }

            if (_fc != null)
            {
                if (!_fcInit)
                {
                    Forecourt.HostName = ipAddress;
                    Forecourt.PosId = _posId;
                    Forecourt.Initialize();
                    _fcInit = true;
                }

                if (_fcInit && !_fcDone)
                {
                    _fcDone = _sender.LoginRequest(accessCode, countryCode, posVersionId, unsolMsgs);
                }
            }

            if (TcpIp1 != null && !_tcpInit1)
            {
                _tcpInit1 = InitialiseTcpConnection(1, TcpIp1, _tcpIp1, TcpMessageReceived1, ipAddress);
            }

            if (_tcpInit1 && !_tcpDone1)
            {
                _tcpDone1 = _sender.LoginRequest(accessCode, countryCode, posVersionId, unsolMsgs) && _connectedApc1;
            }

            if (TcpIp2 != null && !_tcpInit2)
            {
                _tcpInit2 = InitialiseTcpConnection(2, TcpIp2, _tcpIp2, TcpMessageReceived2, ipAddress, false);
            }

            if (_tcpInit2 && !_tcpDone2)
            {
                _tcpDone2 = _sender2.LoginRequest(accessCode2, countryCode, posVersionId, unsolMsgs2) && _connectedApc2;
            }

            if (TcpIp4 != null && !_tcpInit4)
            {
                _tcpInit4 = InitialiseTcpConnection(4, TcpIp4, _tcpIp4, TcpMessageReceived4, ipAddress);
            }

            if (_tcpInit4 && !_tcpDone4)
            {
                _tcpDone4 = _sender4.LoginRequest(accessCode4, countryCode, posVersionId, solMsgs4) && _connectedApc4;
            }

            if (TcpIp5 != null && !_tcpInit5)
            {
                _tcpInit5 = InitialiseTcpConnection(5, TcpIp5, _tcpIp5, TcpMessageReceived5, ipAddress, false);
            }

            if (_tcpInit5 && !_tcpDone5)
            {
                _tcpDone5 = _sender5.LoginRequest(accessCode5, countryCode, posVersionId, unsolMsgs5) && _connectedApc5;
            }

            // Reset pump Heartbeats
            foreach (var pump in _allInstalledPumps)
            {
                Pump(pump).ReceivedHeartbeat();
            }

            // Wait for messages to be received
            SnoozeAsync((int)ConfigValueSendResponseIntervalLogon.GetValue().TotalMilliseconds);
            _tcpDone1 = _tcpDone1 && _connectedApc1;
            _tcpDone2 = _tcpDone2 && _connectedApc2;
            _tcpDone4 = _tcpDone4 && _connectedApc4;
            _tcpDone5 = _tcpDone5 && _connectedApc5;

            var na = "n/a";
            DoDeferredLogging(LogLevel.Info, $"Connection.{HeaderStatus}", () => new[] {
                $"APC1: {_tcpDone1}; " +
                $"APC2: {(TcpIp2 == null ? na: _tcpDone2)}; APC4: {(TcpIp4 == null? na: _tcpDone4)}; APC5: {(TcpIp5 ==  null ? na: _tcpDone5)}; " +
                $"Forecourt: {(_fc == null ? na : _fcDone)}"});

            return _tcpDone1 && (TcpIp2 == null || _tcpDone2) && (TcpIp4 == null || _tcpDone4) && (TcpIp5 == null || _tcpDone5);
        }

        private void _tcpIpXxx_ConnectionLost(int APC, APCTypes APCType)
        {
            DoDeferredLogging(Htec.Foundation.Core.LogLevel.Error, HeaderException, () => new[] { $"APC: {APC}; APCType: {APCType}" });
            SetReload();
        }

        public void RequestHeartbeat(byte fpId = 0)
        {
            if (!Enabled)
            {
                return;
            }

            var timeout = GetWorker<IPumpControllerCallbacks>()?.HeartbeatTimeout ?? TimeSpan.FromSeconds(30);

            // Solicitored channels
            if (_reader.ApcHeartbeats.Where(x => x.Key == 1 || x.Key == 4).Any(x => x.Value > DateTime.MinValue && x.Value < DateTime.UtcNow.AddSeconds(-timeout.TotalSeconds)) && !Reload)
            {
                SetReload();
                return;
            }

            Task.Run(() => _sender.FetchFpStatusHeartbeat(fpId), ShutdownToken);
            if (_updateSetup)
            {
                Task.Run(() => _sender4.FetchFpStatusHeartbeat(fpId), ShutdownToken);
            }
        }

        private byte GetPosId(byte posId = 0) => posId == 0 ? _posId : posId;

        /// <inheritdoc/>
        public bool Reserve(byte fpId, uint limit, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var pump = Pump(fpId);
            pump.SetIsReserved(false);
            _sender.ReserveRequest(pump, limit, GetPosId(posId));
            SnoozeAsync((int)ConfigValueSendResponseIntervalReserve.GetValue().TotalMilliseconds);
            return pump.IsReserved;
        }

        /// <inheritdoc/>
        public bool Authorise(byte fpId, uint limit, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var _posId = GetPosId(posId);
            var pump = Pump(fpId);

            if (!pump.IsReserved)
            {
                return false;
            }

            pump.SetIsAuthorised(false);
            _sender.AuthoriseRequest(pump, limit, _posId, (byte)ConfigValueDOMSServiceModeMixed.GetValue());
            SnoozeAsync((int)ConfigValueSendResponseIntervalAuthorise.GetValue().TotalMilliseconds);
            return pump.IsAuthorised;
        }

        /// <inheritdoc/>
        public bool CancelAuthorise(byte fpId, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var pump = Pump(fpId);
            if ((pump.IsReserved || pump.IsAuthorised) && pump.LockId == GetPosId(posId))
            {
                _sender.CancelAuthoriseRequest(pump, GetPosId(posId));
                SnoozeAsync((int)ConfigValueSendResponseIntervalCancelAuthorise.GetValue().TotalMilliseconds);
            }
            return !pump.IsAuthorised;
        }

        /// <inheritdoc/>
        public bool ReadClaimTransaction(byte fpId, byte transNum, int seqNo, byte posId = 0)
        {
            return ReadClaimTransactionFpUnSup(fpId, transNum, seqNo, posId);
        }

        /// <inheritdoc/>
        public bool ReadClaimTransactionFpUnSup(byte fpId, byte transNum, int seqNo, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            posId = GetPosId(posId);
            var pump = Pump(fpId);

            var transaction = pump.Transaction(seqNo, transNum);
            var gradeMismatch = transaction != null && pump.GradeId != transaction.GradeId;

            pump.SetIsLocked(transaction?.TransLockId == posId);
            if (gradeMismatch || (!pump.IsLocked && transaction?.TransLockId == 0x00))
            {
                _sender4.ReadLockRequestFpUnSup(pump, transaction, posId);
                SnoozeAsync((int)ConfigValueSendResponseIntervalClaim.GetValue().TotalMilliseconds);
            }
            return pump.IsLocked;
        }

        /// <inheritdoc/>
        public bool ReadClaimTransactionFpSup(byte fpId, byte transNum, int seqNo, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            posId = GetPosId(posId);
            var pump = Pump(fpId);

            var transaction = pump.Transaction(seqNo, transNum);
            var gradeMismatch = transaction != null && pump.GradeId != transaction.GradeId;

            //pump.SetIsLocked(transaction?.TransLockId == posId);
            if (gradeMismatch || (!pump.IsLocked && transaction?.TransLockId == 0x00))
            {
                _sender.ReadLockRequestFpSup(pump, transaction, posId);
                SnoozeAsync((int)ConfigValueSendResponseIntervalClaim.GetValue().TotalMilliseconds);
            }
            return true; // pump.IsLocked;
        }

        /// <inheritdoc/>
        public bool ClearTransaction(byte fpId, byte transNum, int seqNo, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var pump = Pump(fpId);
            var transaction = pump.Transaction(seqNo, transNum);

            return ClearTransaction(fpId, transaction.SequenceNumber, transaction.Volume, transaction.Amount, posId);
        }

        /// <inheritdoc/>
        public bool ClearTransaction(byte fpId, int seqNo, uint volume, uint amount, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var pump = Pump(fpId);

            if (pump.IsLocked)
            {
                pump.SetIsCleared(false);
                _sender4.ClearTransactionRequest(fpId, seqNo, volume, amount, GetPosId(posId));
                SnoozeAsync((int)ConfigValueSendResponseIntervalClear.GetValue().TotalMilliseconds);
            }
            if (pump.IsCleared)
            {
                pump.AddCashedOutState(seqNo, TransactionCashedOutState.CashedOut);
            }
            return pump.IsCleared;
        }

        /// <inheritdoc/>
        public Result AutoClearTransaction(byte fpId, byte transNum, int seqNo, byte smId, uint volume, uint amount, byte pos = 0)
        {
            DoDeferredLogging(LogLevel.Warn, "FpId", () => new[] { $"{fpId}; Auto-clearing StuckSale, Trans: {transNum}; SeqNum: {seqNo}; SMId: {smId}; LockId: {pos}; Volume: {volume}; Amount: {amount}" });
            SetLocked(fpId);
            var result = ClearTransaction(fpId, seqNo, volume, amount, pos);

            if (result)
            {
                Pump(fpId).AddCashedOutState(seqNo, TransactionCashedOutState.AutoCleared);
            }

            return Result.SuccessIf(result, $"Could not Clear transaction, FpId: {fpId}; TranNum: {transNum}; Pos: {pos}; SeqNum: {seqNo}");
        }

        /// <inheritdoc/>
        public bool ReleaseTransaction(byte fpId, byte transNum, int seqNo, byte posId = 0)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            var pump = Pump(fpId);
            var transaction = pump.Transaction(seqNo, transNum);

            if (!pump.IsCleared)
            {
                _sender4.UnlockRequest(pump, transaction, GetPosId(posId));
            }

            return true;
        }

        /// <inheritdoc/>
        public bool EmergencyStop(byte fpId)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            _sender.EmergencyStopRequest(Pump(fpId));
            return true;
        }

        /// <inheritdoc/>
        public bool CancelEmergencyStop(byte fpId)
        {
            if (!Enabled || !Connected)
            {
                return false;
            }

            _sender.CancelEmergencyStopRequest(Pump(fpId));
            return true;
        }

        public void SetSoftwareVersion(string softwareVersion, string softwareDate)
        {
            SoftwareVersion = softwareVersion;
            SoftwareDate = softwareDate;
        }

        public void SetInstalledPumps(IList<byte> devices)
        {
            _installedPumps = new ConcurrentBag<byte>(devices);
            PumpControllerCallbacks?.OnMaxPumps(devices.Count, LoggingReference);
        }

        public void SetInstalledEpts(IList<byte> devices)
        {
            _installedEpts = new ConcurrentBag<byte>(devices);
        }

        public void SetInstalledTankGauges(IList<byte> devices)
        {
            _installedTankGauges = new ConcurrentBag<byte>(devices);
        }

        public void SetInstalledPricePoles(IList<byte> devices)
        {
            _installedPricePoles = new ConcurrentBag<byte>(devices);
        }

        public void SetLoggedOn(byte apc)
        {
            if (apc == 1)
            {
                _connectedApc1 = true;
            }
            else if (apc == 2)
            {
                _connectedApc2 = true;
            }
            else if (apc == 4)
            {
                _connectedApc4 = true;
            }
            else if (apc == 5)
            {
                _connectedApc5 = true;
            }
        }

        public void SetFuellingData(byte fpId, uint volume, uint cash)
        {
            var data = Pump(fpId).FuellingData;

            data.Amount = cash;
            data.Volume = volume;
            data.Count++;
        }

        private void ClearFuellingData(DomsSetupPump pump)
        {
            var data = pump.FuellingData;
            data.Amount = 0;
            data.Volume = 0;
            data.Count = 0;
        }

        public void SetReserved(byte fpId)
        {
            var pump = Pump(fpId);
            pump.SetIsReserved(true);

            ClearFuellingData(pump);
        }

        public void SetAuthorised(byte fpId)
        {
            var pump = Pump(fpId);
            pump.SetIsAuthorised(true);

            ClearFuellingData(pump);
        }

        public void SetCancelAuthorised(byte fpId)
        {
            var pump = Pump(fpId);
            pump.SetIsAuthorised(false);
            pump.SetIsLocked(false);
            pump.SetIsReserved(false);
            pump.ClearTransactionList();

            ClearFuellingData(pump);
        }

        public void SetCleared(byte fpId)
        {
            Pump(fpId).SetIsCleared(true);

            SetCancelAuthorised(fpId);
        }

        public void SetLocked(byte fpId)
        {
            Pump(fpId).SetIsLocked(true);
        }

        public void SetEnabled(bool isEnabled)
        {
            Enabled = isEnabled;
        }

        public void SetReload()
        {
            Reload = Enabled;

            Disconnect();
        }

        private bool ClearDownTcpConnection(byte apc)
        {
            switch (apc)
            {
                case 1:
                    _connectedApc1 = false;
                    _tcpDone1 = false;
                    break;

                case 2:
                    _connectedApc2 = false;
                    _tcpDone2 = false;
                    break;

                case 4:
                    _connectedApc4 = false;
                    _tcpDone4 = false;
                    break;

                case 5:
                    _connectedApc5 = false;
                    _tcpDone5 = false;
                    break;

                default:
                    break;
            }
            return true;
        }

        public void Disconnect()
        {
            if (Connected && (Reload || !Enabled))
            {
                ClearDownTcpConnection(1);
                ClearDownTcpConnection(2);
                ClearDownTcpConnection(4);
                ClearDownTcpConnection(5);

                try
                {
                    //Forecourt?.Disconnect();
                    //_fc = null;
                    //_fcInit = false;
                    _fcDone = false;
                }
                catch (Exception e)
                {
                    GetLogger().Error($"Exception disconnecting DOMS (POS {_posId})", e);
                    GetLogger().Info($"DOMS error HResult is {e.HResult:x8}");
                    GetLogger().Info($"DOMS error Text is {Forecourt?.HResult2Text(e.HResult) ?? "Unavailable"}");
                    ControllerWorker?.SendInformation($"DOMS - Disconnection Failed (POS {_posId})");
                }

                Reload = false;
                ControllerWorker?.SendInformation($"DOMS - DOMS Disconnected (POS {_posId})");
                PumpControllerCallbacks.OnDisconnected();
            }
        }

        private IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfoIdle(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId, bool paid, int posClaim)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"1.{HeaderStatus}.FpId", () => new[] { $"{pump}; smId: {smId}; prevState: {prevState}; state: {state}; prevDispState: {prevDispState}; paid: {paid}; posClaim: {posClaim}" });

            // OPT - ServiceMode=51
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                if (prevState == DomsSetupMainState.Fuelling && prevDispState.HasFlag(DispenserState.Delivering) && posClaim != 0)
                {
                    return paid ?
                        new[] { (DispenserState.Finished, true, 0) } :
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }

                // ADO #699588, Error blip
                if (prevState == DomsSetupMainState.Error && state == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }

                // OPT - ServiceMode=51
                if (prevState == DomsSetupMainState.Idle && (prevDispState.HasFlag(DispenserState.Delivering) || prevDispState == DispenserState.Authorised) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                // ADO #697962
                if (prevState.HasFlag(DomsSetupMainState.Starting) && state == DomsSetupMainState.Idle && prevDispState == DispenserState.Authorised && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                if (prevState == DomsSetupMainState.Idle && prevDispState == DispenserState.Finished && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim) };
                }

                if (prevState == DomsSetupMainState.Unavailable && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                if (prevState == DomsSetupMainState.Unavailable && prevDispState.HasFlag(DispenserState.Finished) && state == DomsSetupMainState.Idle && posClaim != 0)
                {
                    return prevDispState == DispenserState.Finished ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }

                // Zero-value transaction
                if (prevState.HasFlag(DomsSetupMainState.Starting) && state == DomsSetupMainState.Preauthorised && prevDispState.HasFlag(DispenserState.Authorised) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Authorised, true, 0) };
                }
                // ADO #782550
                if (prevState == DomsSetupMainState.Preauthorised && state == DomsSetupMainState.Idle && prevDispState == DispenserState.Authorised && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                // ADO #697900
                if (prevState == DomsSetupMainState.Idle && (prevDispState == DispenserState.Finished || prevDispState == DispenserState.Idle || prevDispState.HasFlag(DispenserState.Requested)) && !paid && posClaim != 0)
                {
                    return prevDispState == DispenserState.Finished ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim) } :
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, posClaim) };
                }
                // ADO #697985
                if (prevState == DomsSetupMainState.Idle && state == DomsSetupMainState.Preauthorised && (prevDispState.HasFlag(DispenserState.Requested) || prevDispState.HasFlag(DispenserState.Idle)) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Authorised, true, 0) };
                }
                // ADO #697985
                if (prevState == DomsSetupMainState.Preauthorised && prevDispState == DispenserState.Authorised && state == DomsSetupMainState.Preauthorised && paid && posClaim != 0)
                {
                    return null;
                }
                if (prevState == DomsSetupMainState.Unavailable && prevDispState == DispenserState.Authorised && state == DomsSetupMainState.Preauthorised && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0) };
                }
                if (prevState == DomsSetupMainState.Unavailable && prevDispState == DispenserState.Finished && state == DomsSetupMainState.Preauthorised && posClaim != 0)
                {
                    return null;
                }
                // ADO #697985 / #705669
                if ((prevState == DomsSetupMainState.Calling || prevState == DomsSetupMainState.Starting) && state == DomsSetupMainState.Preauthorised && prevDispState.HasFlag(DispenserState.Requested) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Authorised, true, 0) };
                }
                if (prevState == DomsSetupMainState.Calling && prevDispState.HasFlag(DispenserState.Requested) && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim) };
                }

                // Stopped, but Completing
                if (prevDispState == DispenserState.StoppedDelivering && (prevState == DomsSetupMainState.FuellingPaused || prevState == DomsSetupMainState.Unavailable) && !paid)
                {
                    return new[] { (DispenserState.IdleUnpaid, false, 0) };
                }
                if (prevDispState == DispenserState.FinishedUnpaid && prevState == DomsSetupMainState.Unavailable && !paid)
                {
                    return new[] { (DispenserState.IdleUnpaid, false, 0) };
                }
                if (prevDispState == DispenserState.IdleUnpaid && (prevState == DomsSetupMainState.Unavailable || prevState == DomsSetupMainState.Idle) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.IdleUnpaidClaimed, false, posClaim) };
                }
                // Stopped, but Completed
                if (prevDispState == DispenserState.IdleUnpaidClaimed && (prevState == DomsSetupMainState.Unavailable || prevState == DomsSetupMainState.Idle) && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }

                // Pending Transactions (coming from global FpUnSupTransBufStatus), following re-start, Pump-Open
                if (prevDispState == DispenserState.Initialise || prevDispState == DispenserState.Closed && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }
            else
            // Kiosk - ServiceMode=12/13
            if (smId == ConfigValueDOMSServiceModeKioskOnly.GetValue() || smId == ConfigValueDOMSServiceModeKioskUse.GetValue())
            {
                if (prevState == DomsSetupMainState.Fuelling && (prevDispState.HasFlag(DispenserState.Delivering) || prevDispState.HasFlag(DispenserState.Authorised)) && posClaim == 0)
                {
                    return new[] { (DispenserState.Finished, true, 0) };
                }

                if (prevState.HasFlag(DomsSetupMainState.Starting) && prevDispState.HasFlag(DispenserState.Authorised) && paid && posClaim == 0)
                {
                    // ADO #747433
                    return state == DomsSetupMainState.Preauthorised ?
                        new[] { (DispenserState.Authorised, true, 0) } :
                        new[] { (DispenserState.Delivering, true, 0), (DispenserState.Delivering, true, 0) };
                }
                // Nozzle juggling
                if (prevState.HasFlag(DomsSetupMainState.Starting) && state == DomsSetupMainState.Preauthorised && (prevDispState.HasFlag(DispenserState.Requested) || prevDispState == DispenserState.Authorised) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Authorised, true, 0) };
                }

                // Kiosk - ServiceMode=12
                if (prevState == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0) };
                }
                if (prevState == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.Finished) && !paid)
                {
                    return posClaim != 0 ?
                        new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, (int)posClaim) } :
                        new[] { (DispenserState.FinishedUnpaid, false, 0) };
                }
                if (prevState == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.IdleUnpaidClaimed) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }
                if (prevState == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.Finished) && paid && posClaim == 0)
                {
                    return null;
                }

                // Zero-value transaction
                if (prevDispState == DispenserState.IdleUnpaidClaimed && prevState == DomsSetupMainState.Preauthorised && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }
                if ((prevDispState.HasFlag(DispenserState.Delivering) || prevDispState.HasFlag(DispenserState.Finished) || prevDispState.HasFlag(DispenserState.Idle)) && prevState.HasFlag(DomsSetupMainState.Preauthorised) && paid && posClaim == 0)
                {
                    return
                        prevDispState.HasFlag(DispenserState.Delivering) ? new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        prevDispState == DispenserState.Finished ? new[] { (DispenserState.Finished, true, 0) } : null;
                }
                // ADO #697900
                if (prevState == DomsSetupMainState.Calling && state == DomsSetupMainState.Idle && (prevDispState.HasFlag(DispenserState.Idle) || prevDispState.HasFlag(DispenserState.Requested)) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.RequestStarted, true, 0) };
                }
                // ADO #709329
                if (prevState == DomsSetupMainState.Idle && state == DomsSetupMainState.Idle && prevDispState.HasFlag(DispenserState.Requested) && paid && posClaim != 0)
                {
                    return new[] { (DispenserState.RequestStarted, true, 0) };
                }
                if (prevState == DomsSetupMainState.Unavailable && state == DomsSetupMainState.Preauthorised && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Authorised, true, 0) }; // ADO #747433
                }
                if (prevState == DomsSetupMainState.Unavailable && prevDispState == DispenserState.Finished && paid && posClaim == 0)
                {
                    return null;
                }
                // ADO #747433 / #772460
                if (prevState == DomsSetupMainState.Preauthorised && state == DomsSetupMainState.Idle && prevDispState == DispenserState.Authorised && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }

                // Stopped, but Completing
                if (prevDispState == DispenserState.StoppedDelivering && (prevState == DomsSetupMainState.FuellingPaused || prevState == DomsSetupMainState.Unavailable) && posClaim == 0)
                {
                    return !paid ?
                        new[] { (DispenserState.IdleUnpaid, false, 0) } :
                        new[] { (DispenserState.Finished, true, 0) };
                }
                if (prevDispState == DispenserState.FinishedUnpaid && prevState == DomsSetupMainState.Unavailable && !paid)
                {
                    return new[] { (DispenserState.IdleUnpaid, false, 0) };
                }
                if (prevDispState == DispenserState.IdleUnpaid && (prevState == DomsSetupMainState.Unavailable || prevState == DomsSetupMainState.Idle) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                // Stopped, but Completed
                if (prevDispState == DispenserState.IdleUnpaidClaimed && (prevState == DomsSetupMainState.Unavailable || prevState == DomsSetupMainState.Idle) && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }

                // Pending Transactions (coming from global FpUnSupTransBufStatus), following re-start, Pump-Open
                if (prevState == DomsSetupMainState.Closed && prevDispState == DispenserState.Initialise || prevDispState == DispenserState.Closed && paid && posClaim == 0)
                {
                    return prevDispState == DispenserState.Closed ?
                        new[] { (DispenserState.Initialise, true, 0) } :
                        new[] { (DispenserState.Idle, true, 0) };
                }
            }

            // ADO #705669
            return state == DomsSetupMainState.Preauthorised ?
                new[] { (DispenserState.Authorised, true, 0) } :
                new[] { (DispenserState.Idle, true, 0) };
        }

        private IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfoFinished(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId, bool paid, byte posClaim, int stateCycleNo = HscPumpData.StateStartingCycleThreshold)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"1.{HeaderStatus}.FpId", () => new[] { $"{pump}; smId: {smId}; prevState: {prevState}; state: {state}; prevDispState: {prevDispState}; paid: {paid}; posClaim: {posClaim}" });

            // Kiosk - ServiceMode=12/13 (HydraMobile)
            if (smId == ConfigValueDOMSServiceModeKioskOnly.GetValue() || smId == ConfigValueDOMSServiceModeKioskUse.GetValue())
            {
                if (state == DomsSetupMainState.Unavailable && (prevDispState == DispenserState.Idle || prevDispState.HasFlag(DispenserState.Requested)) && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Reserved, true, 0) };
                }
                if (state == DomsSetupMainState.UnavailableAndCalling && (prevDispState == DispenserState.Idle || prevDispState == DispenserState.Reserved) && paid && posClaim == 0)
                {
                    return new[] { stateCycleNo < HscPumpData.StateStartingCycleThreshold ? (DispenserState.RequestStarted, true, 0) : (DispenserState.Requested, true, 0) };
                }
            }

            // Error, now Unavailable but Completing (and Stalled!) ADO #694844
            if (state.HasFlag(DomsSetupMainState.Unavailable) && (prevState.HasFlag(DomsSetupMainState.Unavailable) || prevState == DomsSetupMainState.Error) &&
                (prevDispState.HasFlag(DispenserState.Idle) || prevDispState.HasFlag(DispenserState.Finished) && !paid && posClaim != 0))
            {
                return
                    prevDispState == DispenserState.Finished ? new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                    prevDispState == DispenserState.FinishedUnpaid ? new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                    prevDispState == DispenserState.IdleUnpaid ? new[] { (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                    prevDispState == DispenserState.IdleUnpaidClaimed ? new[] { (DispenserState.Idle, true, 0) } : null;
            }
            if (smId == ConfigValueDOMSServiceModeKioskOnly.GetValue() || smId == ConfigValueDOMSServiceModeKioskUse.GetValue())
            {
                if (state == DomsSetupMainState.Unavailable && prevState.HasFlag(DomsSetupMainState.Fuelling) && prevDispState.HasFlag(DispenserState.Delivering))
                {
                    // ADO #732046 / #772460
                    return !paid && posClaim != 0 ?
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        new[] { (DispenserState.Finished, true, 0) };
                }
                if (state == DomsSetupMainState.Unavailable && prevState.HasFlag(DomsSetupMainState.Fuelling) && prevDispState == DispenserState.Finished)
                {
                    // ADO #732046 / #772460
                    return !paid && posClaim != 0 ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        null;
                }
            }
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                if (state == DomsSetupMainState.Unavailable && (prevState.HasFlag(DomsSetupMainState.Fuelling) || prevState == DomsSetupMainState.Unavailable) && !paid && posClaim != 0)
                {
                    // ADO #772127 / #782550
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }

            // Zero-value transaction
            if (prevState == DomsSetupMainState.Unavailable && prevDispState == DispenserState.Finished && state == DomsSetupMainState.Preauthorised && posClaim != 0)
            {
                return null;
            }
            if (prevDispState.HasFlag(DispenserState.Finished) && prevState.HasFlag(DomsSetupMainState.Preauthorised) && paid && posClaim == 0)
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }
            if (prevDispState == DispenserState.Authorised && prevState == DomsSetupMainState.Starting && paid)
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }
            if (prevState == DomsSetupMainState.Starting && prevDispState == DispenserState.Authorised && state == DomsSetupMainState.Unavailable && paid && posClaim == 0)
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }

            // Stopped/Starting
            if ((prevDispState == DispenserState.Authorised || prevDispState == DispenserState.Idle) && prevState == DomsSetupMainState.StartingPaused && paid && posClaim == 0)
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }

            // Stopped, Completing but Stalled! ADO#694857
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                if (prevDispState == DispenserState.StoppedDelivering && prevState.HasFlag(DomsSetupMainState.Unavailable) && state.HasFlag(DomsSetupMainState.Unavailable) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }

            // Stopped, but Completing
            if (prevDispState == DispenserState.StoppedDelivering && (prevState == DomsSetupMainState.FuellingPaused || prevState == DomsSetupMainState.Unavailable))
            {
                return paid && posClaim == 0 ?
                    new[] { (DispenserState.StoppedDelivering, true, 0) } :
                    new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0) };
            }
            if ((prevState == DomsSetupMainState.FuellingPaused || prevState == DomsSetupMainState.Unavailable) && !paid && posClaim != 0)
            {
                return new[] { (DispenserState.FinishedUnpaid, false, 0) };
            }
            if (prevDispState == DispenserState.FinishedUnpaid && prevState == DomsSetupMainState.Error && !paid && posClaim != 0)
            {
                return new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
            }
            // Stopped, but Completed
            if (prevDispState == DispenserState.IdleUnpaidClaimed && paid && posClaim == 0)
            {
                return new[] { (DispenserState.Idle, true, 0) };
            }

            if (prevDispState.HasFlag(DispenserState.Delivering) && (prevState == DomsSetupMainState.Fuelling || prevState == DomsSetupMainState.FuellingPaused || prevState == DomsSetupMainState.FuellingTerminated))
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }

            if (prevDispState.HasFlag(DispenserState.Delivering) || prevDispState == DispenserState.Finished && prevState == DomsSetupMainState.Unavailable && posClaim == 0)
            {
                return new[] { (DispenserState.Finished, true, 0) };
            }

            // Pending Transactions (coming from global FpUnSupTransBufStatus), following re-start, Pump-Open
            if ((prevState == DomsSetupMainState.Idle || prevState == DomsSetupMainState.Closed) && (prevDispState == DispenserState.Initialise || prevDispState == DispenserState.Closed || prevDispState.HasFlag(DispenserState.Idle)) && paid && posClaim == 0)
            {
                return prevDispState == DispenserState.Closed ?
                    new[] { (DispenserState.Initialise, true, 0) } :
                    new[] { (DispenserState.Idle, true, 0) };
            }

            return new[] { (DispenserState.FinishedUnpaid, false, 0) };
        }

        private IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfoError(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId, bool paid, int posClaim)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"1.{HeaderStatus}.FpId", () => new[] { $"{pump}; smId: {smId}; prevState: {prevState}; state: {state}; prevDispState: {prevDispState}; paid: {paid}; posClaim: {posClaim}" });

            // OPT - ServiceMode=51
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                // Starting/Errored
                if (prevState.HasFlag(DomsSetupMainState.Starting) && state == DomsSetupMainState.StartingPaused && prevDispState == DispenserState.Authorised && paid)
                {
                    return posClaim == 0 ?
                        new[] { (DispenserState.Finished, true, 0) } :
                        new[] { (DispenserState.Authorised, true, 0) };
                }
                // ADO#696492
                if (prevState.HasFlag(DomsSetupMainState.Starting) && state == DomsSetupMainState.Error && prevDispState == DispenserState.Authorised && paid)
                {
                    return new[] { (DispenserState.Finished, true, 0) };
                }

                // Stopped/Errored, but Completing
                if (prevState == DomsSetupMainState.FuellingPaused && (prevDispState == DispenserState.StoppedDelivering || prevDispState == DispenserState.Finished) && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Finished, true, 0) };
                }

                // Errored, but Completing ADO#694844
                if (state == DomsSetupMainState.Error && prevState == DomsSetupMainState.Error && prevDispState.HasFlag(DispenserState.Finished) && !paid && posClaim != 0)
                {
                    return prevDispState == DispenserState.Finished ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                // Errored, but Completing, ADO #699588
                if (prevState.HasFlag(DomsSetupMainState.Fuelling) && state == DomsSetupMainState.Error && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                if (prevState == DomsSetupMainState.Error && (prevDispState == DispenserState.StoppedDelivering || prevDispState == DispenserState.Finished) && posClaim != 0)
                {
                    return prevDispState == DispenserState.Finished ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0) } :
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0) };
                }
                if (prevState == DomsSetupMainState.Error && (prevDispState == DispenserState.FinishedUnpaid) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
                // ADO #691311
                if (prevState == DomsSetupMainState.Error && (prevDispState == DispenserState.Finished || prevDispState.HasFlag(DispenserState.Delivering)) && !paid && posClaim != 0)
                {
                    return prevDispState == DispenserState.Finished ?
                        new[] { (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) } :
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }

                // Stopped/Errored, but Completed
                if (prevDispState == DispenserState.IdleUnpaidClaimed && paid && posClaim == 0)
                {
                    return new[] { (DispenserState.Idle, true, 0) };
                }

                // Re-started/Error, Fuelling
                if (prevState == DomsSetupMainState.Fuelling && prevDispState.HasFlag(DispenserState.Delivering) && posClaim == 0)
                {
                    return new[] { (DispenserState.Finished, true, 0) };
                }

                // Error-Overruns, ADO #698285
                if (prevState.HasFlag(DomsSetupMainState.Fuelling) && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }
            else
            // Kiosk - ServiceMode=12/13 (HydraMobile)
            if (smId == ConfigValueDOMSServiceModeKioskOnly.GetValue() || smId == ConfigValueDOMSServiceModeKioskUse.GetValue())
            {
                // Error, Fuelling, Completing
                if (prevState == DomsSetupMainState.Fuelling && prevDispState.HasFlag(DispenserState.Delivering))
                {
                    return paid && posClaim == 0 ?
                        new[] { (DispenserState.Finished, true, 0) } :
                        new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }

            return new[] { (DispenserState.Idle, true, 0) };
        }

        private IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfoFuelling(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId, bool paid, int posClaim, int stateCycleNo)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"1.{HeaderStatus}.FpId", () => new[] { $"{pump}; smId: {smId}; prevState: {prevState}; state: {state}; prevDispState: {prevDispState}; paid: {paid}; posClaim: {posClaim}" });

            // ADO #738139
            if (state == DomsSetupMainState.Fuelling && prevState == DomsSetupMainState.Starting && prevDispState.HasFlag(DispenserState.Requested))
            {
                return new[] { (DispenserState.Authorised, true, 0), (DispenserState.DeliveringStarted, true, 0) };
            }

            // OPT - ServiceMode=51
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                // ADO #753650
                if (state == DomsSetupMainState.Fuelling && prevState == DomsSetupMainState.Fuelling && prevDispState.HasFlag(DispenserState.Delivering) && !paid && posClaim != 0)
                {
                    return new[] { (DispenserState.Finished, true, 0), (DispenserState.FinishedUnpaid, false, 0), (DispenserState.IdleUnpaid, false, 0), (DispenserState.IdleUnpaidClaimed, false, posClaim), (DispenserState.Idle, true, 0) };
                }
            }
            else
            {
            }

            return new[] { stateCycleNo < HscPumpData.StateStartingCycleThreshold ? (DispenserState.DeliveringStarted, true, 0) : (DispenserState.Delivering, true, 0) };
        }

        private Result FlushQueues(byte pump, string info)
        {
            var result = Result.Combine(
                       Flush(pump),
                       _fetchStatePumpsQueue.Flush(pump),
                       PumpControllerCallbacks.FlushOnPumpData(pump));

            DoDeferredLogging(LogLevel.Info, $"Flush.{HeaderStatus}.FpId", () => new[] { $"{pump}; {info};{(result.IsSuccess ? string.Empty : $" Results: [{result.Error}")}]" });

            return result;
        }

        private IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfoCalling(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId, bool paid, int posClaim, int stateCycleNo)
        {
            var info = $"smId: {smId}; prevState: {prevState}; state: {state}; prevDispState: {prevDispState}; paid: {paid}; posClaim: {posClaim}";
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"1.{HeaderStatus}.FpId", () => new[] { $"{pump}; {info}" });

            // OPT - ServiceMode=51
            if (smId == ConfigValueDOMSServiceModeMixed.GetValue() || smId == ConfigValueDOMSServiceModeOptOnly.GetValue())
            {
                // TODO: Nozzle jiggling, with DOMS throwing £0 transaction in the middle - ADO #697990
            }
            else
            // Kiosk - ServiceMode = 12 / 13 (HydraMobile)
            if (smId == ConfigValueDOMSServiceModeKioskOnly.GetValue() || smId == ConfigValueDOMSServiceModeKioskUse.GetValue())
            {
                // ADO #753951 - Nozzle jiggling
                if (prevState == DomsSetupMainState.Idle && state == DomsSetupMainState.Calling && prevDispState == DispenserState.Idle && paid && posClaim == 0)
                {
                    FlushQueues(pump, info);
                }
            }

            return
                prevDispState == DispenserState.Requested ? null :
                prevDispState == DispenserState.RequestStarted ? new[] { (DispenserState.Requested, true, 0) } :
                new[] { (DispenserState.RequestStarted, true, 0) };
        }

        // This is the sequence for, ServiceModeId: 12/13 P@K, 51=P@P
        public IEnumerable<(DispenserState, bool, int)> ToDispenserStateInfo(byte pump, DomsSetupMainState state, DomsSetupMainState prevState, DispenserState prevDispState, int smId,
            bool paid = true, byte posClaim = 0, int stateCycleNo = HscPumpData.StateStartingCycleThreshold) =>
        state switch
        {
            DomsSetupMainState.Idle => ToDispenserStateInfoIdle(pump, state, prevState, prevDispState, smId, paid, posClaim),
            DomsSetupMainState.Preauthorised => ToDispenserStateInfoIdle(pump, state, prevState, prevDispState, smId, paid, posClaim),
            DomsSetupMainState.Calling => ToDispenserStateInfoCalling(pump, state, prevState, prevDispState, smId, paid, posClaim, stateCycleNo),
            DomsSetupMainState.Starting => new[] { (DispenserState.Authorised, true, 0) },
            DomsSetupMainState.StartingPaused => ToDispenserStateInfoError(pump, state, prevState, prevDispState, smId, paid, posClaim),
            DomsSetupMainState.Fuelling => ToDispenserStateInfoFuelling(pump, state, prevState, prevDispState, smId, paid, posClaim, stateCycleNo),
            DomsSetupMainState.FuellingPaused => new[] { (DispenserState.StoppedDelivering, true, 0) },
            DomsSetupMainState.FuellingTerminated => new[] { (DispenserState.StoppedDelivering, true, 0) },
            DomsSetupMainState.Closed => new[] { (DispenserState.Closed, true, 0) },
            DomsSetupMainState.Unavailable => ToDispenserStateInfoFinished(pump, state, prevState, prevDispState, smId, paid, posClaim, stateCycleNo),
            DomsSetupMainState.UnavailableAndCalling => ToDispenserStateInfoFinished(pump, state, prevState, prevDispState, smId, paid, posClaim, stateCycleNo),
            DomsSetupMainState.Unconfigured => new[] { (DispenserState.Initialise, true, 0) },
            DomsSetupMainState.Error => ToDispenserStateInfoError(pump, state, prevState, prevDispState, smId, paid, posClaim),
            _ => new[] { (DispenserState.InvalidState, true, 0) }
        };

        private DomsState GetState()
        {
            return new DomsState(Enabled, Connected, Forecourt, Config);
        }

        /// <inheritdoc/>
        public void FetchStatePump(IDomsSetup prevSetup, (DomsMessageCode, byte) msgCodes, DomsState state = null)
        {
            state ??= GetState();

            foreach (var pump in AllPumps())
            {
                FetchStatePump(pump, prevSetup, state, msgCodes);
            }
        }

        public static Hose ToHose(DomsSetupPump pump, IDomsSetup previousSetup, byte number, byte grade, byte tank = 0)
        {
            var price = (float)pump.GradePrice(grade);
            price /= (float)(pump.HasDecimalPositionInMoney && (pump.DecimalPositionInMoney > 0) ?
                Math.Pow(10, pump.DecimalPositionInMoney) / PumpWorker.CurrencyFactor :
                PumpWorker.CurrencyFactor);
            var hose = new Hose(number, tank, grade, Convert.ToDecimal(price));
            if (hose.Grade != null)
            {
                hose.Grade.Name = previousSetup.GradeName(grade).TrimEnd();
            }
            return hose;
        }

        /// <inheritdoc/>
        public void FetchStatePump(DomsSetupPump pump, IDomsSetup previousSetup, DomsState state, (DomsMessageCode, byte) msgCodes)
        {
            if ((msgCodes.Item1 == DomsMessageCode.FpUnSupTransBufStatus || msgCodes.Item1 == DomsMessageCode.FpSupTransBufStatus) &&
                (pump.MainState == DomsSetupMainState.Idle || pump.MainState == DomsSetupMainState.Error))
            {
                FlushQueues(pump.FpId, $"DOMSState: {pump.MainState}; Code/SubCode: {msgCodes.Item1}/{msgCodes.Item2}");
            }

            _fetchStatePumpsQueue.Enqueue(pump.FpId, (pump, previousSetup, state, msgCodes));
        }

        /// <inheritdoc/>
        private void DoFetchStatePump(DomsSetupPump pump, IDomsSetup previousSetup, DomsState state)
        {
            Hose _ToHose(byte gradeOption, byte tank = 0) => ToHose(pump, previousSetup, _ToHoseIdFromGradeOption(gradeOption), pump.OptionInfo(gradeOption).Item1, tank);

            byte _ToHoseIdFromGradeOption(byte gradeOption)
            {
                var info = pump.OptionInfo(gradeOption);
                return info.Item2 == 0 ? gradeOption : info.Item2;
            }

            void _UpdateHoseTotals(HoseTotals totals, byte number, byte gradeOption, uint volume, uint amount, byte tank = 0, int factorVolume = PumpWorker.VolumeFactor, int factorCurrency = PumpWorker.CurrencyFactor)
            {
                if (totals == null)
                {
                    return;
                }
                totals.Number = number;
                totals.Hose = _ToHose(gradeOption, tank);
                totals.Volume = Convert.ToDecimal(volume) / factorVolume;
                totals.Amount = Convert.ToDecimal(amount) / factorCurrency;
                totals.Price = totals.Hose?.Grade?.Price ?? 0;
            }

            Transaction _ToTransaction(byte number, bool isPaid, DomsSetupTransaction tran, byte gradeOption, byte tank)
            {
                var result = tran == null ? Transaction.Empty : new Transaction { IsPaid = isPaid, SequencNumber = tran?.SequenceNumber ?? 0 };
                if (number == 1 && tran == null)
                {
                    result.IsPaid = isPaid;
                }
                _UpdateHoseTotals(result, number, tran == null ? (byte)0 : gradeOption, tran?.ActualVolume ?? 0, tran?.ActualAmount ?? 0, tank);
                return result;
            }

            HoseTotals _GetTotals(DomsSetupPump pump, DomsSetupTransaction tran1 = null, DomsSetupTransaction tran2 = null)
            {
                var tank = pump.TankId;
                // Default!!
                var result = new HoseTotals() { Number = _ToHoseIdFromGradeOption(pump.GradeOption), Hose = _ToHose(pump.GradeOption, tank) };
                var volume = tran2?.ActualVolume ?? tran1?.ActualVolume ?? pump.FuellingData.ActualVolume;
                var amount = tran2?.ActualAmount ?? tran1?.ActualAmount ?? pump.FuellingData.ActualAmount;
                _UpdateHoseTotals(result, result.Number, pump.GradeOption, volume, amount, tank);
                return result;
            }

            var dtmStarted = DateTime.UtcNow;

            var allTrans = pump.AllTransactions.Distinct().ToList();

            var prevPump = previousSetup.AllPumps().FirstOrDefault(x => x.FpId == pump.FpId) ?? new DomsSetupPump(pump.FpId);
            var corePumpMode = CorePumps.GetPump(pump.FpId).PumpMode;

            var tran1 = allTrans.FirstOrDefault();
            var tran2 = allTrans.Count > 1 ? allTrans[1] : null;

            var totals = _GetTotals(pump, tran1, tran2);
            var prevTotals = _GetTotals(prevPump);

            var hoseInfo = (from option in pump.AllGradeOptions()
                            let info = pump.OptionInfo(option)
                            select new { Key = option, Value = _ToHose(info.Item5, info.Item3) })
                             .ToDictionary(k => k.Key, v => v.Value);
            var lockId = tran2?.TransLockId ?? tran1?.TransLockId ?? pump.LockId;
            var smId = tran2?.ServiceModeId ?? tran1?.ServiceModeId ?? pump.ActiveSmId;
            var optionInfo = pump.OptionInfo(pump.GradeOption);
            var tankId = optionInfo.Item3 == 0 ? (byte)1 : optionInfo.Item3;

            var lastQueuedState = pump.LastQueuedState;
            var lastQueuedState1 = pump.LastQueuedState;
            var pumpStateInfo = ToDispenserStateInfo(pump.FpId, pump.MainState, prevPump.MainState, prevPump.State, smId, allTrans.Count == 0, lockId, pump.FuellingData.Count);
            if (pumpStateInfo == null)
            {
                return;
            }

            var firstMsg = true;
            foreach (var info in pumpStateInfo)
            {
                if (!(info.Item1.HasFlag(DispenserState.Delivering) || info.Item1.HasFlag(DispenserState.Finished)))
                {
                    totals.Volume = 0;
                    totals.Amount = 0;
                }

                var pumpData = new coreMessages.PumpData()
                {
                    Number = pump.FpId,
                    Dispenser = new Dispenser
                    {
                        Number = pump.FpId,
                        State = info.Item1,
                        CurrentInfo = totals,
                        CommErr = !info.Item1.HasFlag(DispenserState.Idle) && allTrans.Any(x => x.ErrorTrans) ||
                            info.Item1 == DispenserState.Idle && pump.MainState == DomsSetupMainState.Error ? CommunicationState.Bad : CommunicationState.Ok,
                        Transaction1 = _ToTransaction(1, info.Item2, tran1, tran1 == null ? (byte)0 : pump.GradeOption, tankId),
                        Transaction2 = _ToTransaction(2, info.Item2, tran2, tran2 == null ? (byte)0 : pump.GradeOption, tankId),
                        PumpInfo = new PumpInfo { },
                        HoseInfo = hoseInfo,
                        PrePayState = PrePayState.NotAllowed,
                        PrePaymentAmount = 0,
                        Pos = (byte)info.Item3,

                        IsOptAvailable = corePumpMode == Core.Pump.Enums.PumpModeType.Mixed || corePumpMode == Core.Pump.Enums.PumpModeType.OutsideOnly,
                        IsOptInControl = pump.IsReserved || lockId == _posOpt || pump.IsLocked || pump.IsAuthorised || (byte)info.Item3 == _posOpt
                    },

                    RegistrationData = new coreMessages.VehicleRegistrationData { }
                };

                // TODO: Add MainState Count and pass through if any Fuelling up to Configurable max
                if ((prevPump.State != DispenserState.Closed || prevPump.MainState != DomsSetupMainState.Closed) &&
                    (pump.MainState == prevPump.MainState && info.Item1 == prevPump.State && totals.Volume > 0 && prevTotals.Volume == totals.Volume))
                {
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"4.{HeaderStatus}.FpId", () => new[]
                    {
                        $"{pump.FpId}; DOMSState: {prevPump.MainState}/{pump.MainState}; DispenserState: {prevPump.State}/{info.Item1}; Totals: {prevTotals.Volume}/{totals.Volume}"
                    });

                    return;
                }

                lastQueuedState1 = info.Item1;
                var processMsg = !ConfigValueDOMSAlwaysQueueMessages.GetValue() && IsEmpty(pump.FpId) && firstMsg;

                ControllerWorker?.SendInformation($"DOMS - Fuelling Point {pump.FpId} state changed from {prevPump.MainState} to {pump.MainState}{(processMsg ? string.Empty : "; Queued")}");

                if (LastFlushed(pump.FpId) < dtmStarted)
                {
                    if (processMsg)
                    {
                        DoProcessOnPumpData(pump.FpId, pumpData, true);
                    }
                    else
                    {
                        Enqueue(pump.FpId, pumpData);
                    }

                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"2.{HeaderStatus}.FpId", () => new[]
                    {
                        $"{pump.FpId}; {(processMsg ? string.Empty : "Queued; ")}DOMSState: {prevPump.MainState}/{pump.MainState}; DispenserState: {prevPump.State}/{info.Item1}; Paid: {info.Item2}; Pos: {info.Item3}/{lockId}; SmId: {smId};" +
                        $"IsOptInControl = {pump.IsReserved} || {lockId == _posOpt} || {pump.IsLocked} || {pump.IsAuthorised} || {(byte)info.Item3 == _posOpt}; " +
                        $"GradeId: {pump.GradeId}; NozzleId: {pump.HoseId}; GradeOption: {pump.GradeOption}"
                    });
                }

                firstMsg = false;
            }

            pump.LastQueuedState = lastQueuedState1;
        }

        private void DoMessageReceived(byte id, int length, ref byte[] message, APCTypes apcType, string loggingReference = null)
        {
            var prevSetup = Replicate();
            var result = _reader.ExtractResponse(message);

            DoAction(() =>
            {
                var msg1 = result.IsSuccess && result.Value.Any() ? result.Value.First().Item3 : (DomsMessageCode.FcLogon, (byte)0);
                var logMsg = _reader.LoggingEnabled(msg1.Item1, msg1.Item2);
                DoDeferredLogging(logMsg ? ToLogLevel(DeveloperLoggingLevelState.GetValue()) : Htec.Foundation.Core.LogLevel.None, $"TCPMessageReceived.{id}", () => new[] { $"Length: {length}; APC Type: {apcType}; Result: {result.IsSuccess}" });

                if (result.IsSuccess)
                {
                    var results = result.Value.Where(x => x.Item1);
                    if (results.Any(x => x.Item2 == 0))
                    {
                        FetchStatePump(prevSetup, results.First().Item3);
                    }
                    else
                    {
                        foreach (var result2 in results)
                        {
                            FetchStatePump(Pump(result2.Item2), prevSetup, null, result2.Item3);
                        }
                    }
                }
            }, loggingReference);
        }

        private void TcpMessageReceived1(int length, ref byte[] message, APCTypes apcType)
        {
            DoMessageReceived(1, length, ref message, apcType);
        }

        private void TcpMessageReceived2(int length, ref byte[] message, APCTypes apcType)
        {
            DoMessageReceived(2, length, ref message, apcType);
        }

        private void TcpMessageReceived4(int length, ref byte[] message, APCTypes apcType)
        {
            DoMessageReceived(4, length, ref message, apcType);
        }

        private void TcpMessageReceived5(int length, ref byte[] message, APCTypes apcType)
        {
            DoMessageReceived(5, length, ref message, apcType);
        }

        /// <inheritdoc/>
        public IDomsSetup Replicate()
        {
            var setup = new DomsSetup(PumpControllerCallbacks, _posOpt, _posId, LogManager, _updateSetup, ConfigurationManager, CorePumps)
            {
                SoftwareVersion = SoftwareVersion,
                SoftwareDate = SoftwareDate,
                PriceSetId = PriceSetId,
                PriceSetTimestamp = PriceSetTimestamp
            };

            foreach (var pump in AllPumps())
            {
                setup._pumps[pump.FpId] = pump.Replicate();
            }

            foreach (var grade in AllGrades())
            {
                setup._grades[grade.GradeId] = grade;
            }

            return setup;
        }

        private Result DoProcessOnPumpData()
        {
            var resultsIn = new ConcurrentDictionary<byte, Result>();
            var resultsOut = new ConcurrentDictionary<byte, Result>();

            foreach (var p in CorePumps.AllPumps)
            {
                var key = p.Number;

                // Outbound to PumpWorker
                Task.Run(() =>
                {
                    resultsOut[key] = CheckAndProcessQueue(key, (msg) =>
                    {
                        return DoProcessOnPumpData(key, msg);
                    });
                }, ShutdownToken);

                // Inbound from DOMS
                Task.Run(() =>
                {
                    resultsIn[key] = _fetchStatePumpsQueue.CheckAndProcessQueue(key, (msg) =>
                    {
                        DoFetchStatePump(msg.Item1, msg.Item2, msg.Item3);
                        return Result.Success();
                    },
                    (key) => { return _domsPumpLocks.CanLock(key); },
                    (key) => { _domsPumpLocks.Unlock(key); }, nap: 5);
                }, ShutdownToken);
            }

            return Result.Combine(resultsOut.Values.Concat(resultsIn.Values));
        }

        private Result DoProcessFuellingData()
        {
            foreach (var p in CorePumps.AllPumps)
            {
                Task.Run(() =>
                {
                    var key = p.Number;
                    var pump = Pump(key);

                    if (pump.MainState == DomsSetupMainState.Fuelling && (pump.State == DispenserState.Delivering || pump.State == DispenserState.DeliveringStarted))
                    {
                        FetchFromDoms(FetchFcRequestType.None, FetchFpRequestType.FuellingData, key);
                    }
                }, ShutdownToken);
            }

            return Result.Success();
        }

        private Result DoProcessOnPumpData(byte key, coreMessages.PumpData msg, bool requestFuellingData = false)
        {
            var pump = Pump(key);

            pump.State = msg.Dispenser.State;

            //DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"3.{HeaderStatus}.FpId", () => new[] { $"{key}; State: {msg.Dispenser.State};  paid: {msg.Dispenser.Transaction1?.IsPaid}/{msg.Dispenser.Transaction2?.IsPaid}; posClaim: {msg.Dispenser.Pos}" });

            PumpControllerCallbacks?.OnPumpData(msg);

            return Result.Success();
        }

        #region Configurbale items

        /// <summary>Config key, for DOMS </summary>
        public const string ConfigKeyDOMS = Constants.CategoryNamePump + Constants.CategorySeparator + "DOMS";

        /// <summary>Config Key, for DOMS Service modes mapping</summary>
        public const string ConfigKeyDOMSServiceMode = ConfigKeyDOMS + ":SERVICE-MODE:";

        /// <summary>Config Key, for DOMS Service modes mapping, for Mixed</summary>
        public const string ConfigKeyDOMSServiceModeMixed = ConfigKeyDOMSServiceMode + "MIXED";
        /// <summary>Default Value, for DOMS Service modes mapping, for Mixed</summary>
        public const int DefaultValueDOMSServiceModeMixed = 51;
        /// <summary>Config Value, for DOMS Service modes mapping, for Mixed</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeMixed { get; set; }

        /// <summary>Config Key, for DOMS Service modes mapping, for KioskOnly</summary>
        public const string ConfigKeyDOMSServiceModeKioskOnly = ConfigKeyDOMSServiceMode + "KIOSKONLY";
        /// <summary>Default Value, for DOMS Service modes mapping, for KioskOnly</summary>
        public const int DefaultValueDOMSServiceModeKioskOnly = 12;
        /// <summary>Config Value, for DOMS Service modes mapping, for KioskOnly</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeKioskOnly { get; set; }

        /// <summary>Config Key, for DOMS Service modes mapping, for OptkOnly</summary>
        public const string ConfigKeyDOMSServiceModeOptOnly = ConfigKeyDOMSServiceMode + "OPTONLY";
        /// <summary>Default Value, for DOMS Service modes mapping, for OptkOnly</summary>
        public const int DefaultValueDOMSServiceModeOptOnly = 51;
        /// <summary>Config Value, for DOMS Service modes mapping, for OptkOnly</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeOptOnly { get; set; }

        /// <summary>Config Key, for DOMS Service modes mapping, for KioskUse</summary>
        public const string ConfigKeyDOMSServiceModeKioskUse = ConfigKeyDOMSServiceMode + "KIOSKUSE";
        /// <summary>Default Value, for DOMS Service modes mapping, for KioskUse</summary>
        public const int DefaultValueDOMSServiceModeKioskUse = 12;
        /// <summary>Config Value, for DOMS Service modes mapping, for KioskUse</summary>
        protected ConfigurableInt ConfigValueDOMSServiceModeKioskUse { get; set; }

        private const string ConfigKeyPrefixSendResponseInterval = "Interval:SendResponse:";

        public const string ConfigKeyPrefixSendResponseIntervalReserve = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "Reserve:";

        public const string DefaultValueSendResponseIntervalReserve = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalReserve { get; private set; }

        public const string ConfigKeyPrefixSendResponseIntervalAuthorise = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "Authorise:";

        public const string DefaultValueSendResponseIntervalAuthorise = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalAuthorise { get; private set; }

        public const string ConfigKeyPrefixSendResponseIntervalCancelAuthorise = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "CancelAuthorise:";

        public const string DefaultValueSendResponseIntervalCancelAuthorise = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalCancelAuthorise { get; private set; }

        public const string ConfigKeyPrefixSendResponseIntervalClear = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "Clear:";

        public const string DefaultValueSendResponseIntervalClear = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalClear { get; private set; }

        public const string ConfigKeyPrefixSendResponseIntervalClaim = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "Claim:";

        public const string DefaultValueSendResponseIntervalClaim = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalClaim { get; private set; }

        public const string ConfigKeyPrefixSendResponseIntervalLogon = Constants.CategoryNamePump + Constants.CategorySeparator + ConfigKeyPrefixSendResponseInterval + "Logon:";

        public const string DefaultValueSendResponseIntervalLogon = "00:00:01";

        protected ConfigurableTimeSpan ConfigValueSendResponseIntervalLogon { get; private set; }

        public const string ConfigKeyPrefixDOMSAlwaysQueueMessages = ConfigKeyDOMS + ":AlwaysQueueMessages:";

        public const bool DefaultValueDOMSAlwaysQueueMessages = true;

        protected ConfigurableBool ConfigValueDOMSAlwaysQueueMessages { get; private set; }

        #endregion

        public void SetMeterStatus(coreMessages.MeterReadings readings, string loggingReference)
        {
            PumpControllerCallbacks.OnMeterReadings(new[] { readings }, loggingReference);
        }

        public void SetErrorInfo(byte fpId, byte code = 0, DateTime? date = null)
        {
            Pump(fpId).SetErrorInfo(code, date ?? DateTime.MinValue);
        }

        public class FetchStatePumpsQueue : Queueable<byte, (DomsSetupPump, IDomsSetup, DomsState, (DomsMessageCode, byte))>
        {
            public FetchStatePumpsQueue(IHtecLogManager logManager, IConfigurationManager configurationManager) : base(logManager, nameof(PumpWorker), configurationManager)
            {
            }
        }
    }
}
