﻿using CSharpFunctionalExtensions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Bos.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using OPT.Common.Workers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using hydra = Htec.Hydra.Core.Bos.Messages.Hydra;
using LocalAccountTransactionItem = Htec.Hydra.Core.Bos.Messages.LocalAccountTransactionItem;

namespace Forecourt.Bos.Workers
{
    /// <inheritdoc />
    public class HydraBosOutWorker : HydraDbable<HydraDb.Interfaces.IHydraDb>, IHydraBosOutWorker
    {
        /// <summary>        
        /// Hydra version of IBosTransactionFileWriter{}
        /// </summary>
        protected IHydraTransactionFile HydraTransactionFile { get; private set; }
        
        /// <inheritdoc />
        public HydraBosOutWorker(IHtecLogger logger, IConfigurationManager configurationManager, IHydraTransactionFile transactionFile, HydraDb.Interfaces.IHydraDb hydraDb) : 
            base(hydraDb, logger, configurationManager)
        {
            DoCtor(transactionFile);
        }

        private void DoCtor(IHydraTransactionFile transactionFile)
        {
            HydraTransactionFile = transactionFile ?? throw new ArgumentNullException(nameof(transactionFile));
        }

        /// <inheritdoc />
        public HydraBosOutWorker(IHtecLogManager logManager, string name, IConfigurationManager configurationManager, IHydraTransactionFile transactionFile, HydraDb.Interfaces.IHydraDb hydraDb) :
            base(hydraDb, logManager, $"{(string.IsNullOrEmpty(name) ? nameof(HydraBosOutWorker) : name).ConvertToPrefixedDottedName(LoggerNamePrefix)}", configurationManager)
        {
            DoCtor(transactionFile);
        }

        /// <inheritdoc />
        public virtual Result MoveOfflineTransactionFiles(IPAddress ipAddress, string loggingReference = null)
        {
            return HydraTransactionFile.MoveOfflineTransactionFiles(ipAddress, loggingReference);
        }      

        /// <inheritdoc />
        public virtual Result WriteTransactionFile(IEnumerable<TransactionItem> items, DateTime dateTime, IEnumerable<IPAddress> devices = null, IMessageTracking message = null)
        {
            var hydraItems = items.Select(x => (hydra.TransactionFileItem)x);

            HydraTransactionFile.WriteTransactionFile(hydraItems, dateTime, devices, message);

            return Result.Success();
        }
        
        /// <inheritdoc />
        public virtual Result SendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            // Do nothing
            return Result.Success();
        }     

        /// <inheritdoc />
        public virtual Result WriteLocalAccountInvoiceFile(LocalAccountTransactionItem item, IMessageTracking message = null)
        {
            var hydraItem = (hydra.LocalAccountTransactionItem)item;

            HydraTransactionFile.WriteLocalAccountInvoiceFile(new List<hydra.LocalAccountTransactionItem>() { hydraItem}, item.Till, item.DateTime, message);

            return Result.Success();
        }

        /// <inheritdoc />
        public virtual Result WriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem,
            IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = default)
        {
            var hydraSales = itemSales.Select(x => (hydra.ItemSalesItem)x);
            var hydraCardSales = cardSales.Select(x => (hydra.CardSalesItem)x);
            var hydraCategorySales = categorySales.Select(x => (hydra.CategorySalesItem)x);
            var hydraCardVolumeSales = cardVolumeSales.Select(x => (hydra.CardVolumeSalesItem)x);

            HydraTransactionFile.WriteShiftEndFiles((hydra.DayEndItem)endItem, isDayEnd, hydraSales,
                // TODO: cardAmountSales
                Enumerable.Empty<hydra.CardAmountItem>(), 
                hydraCardSales, hydraCategorySales, hydraCardVolumeSales, message);

            return Result.Success();
        }

        /// <inheritdoc />
        public virtual Result<string> GetTransactionFileFolder()
        {
            var result = HydraTransactionFile.FileDirectory;
            HydraTransactionFile.CheckFileDirectory();
            return Result.Success(result);
        }

        /// <inheritdoc />
        public Result DayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result ShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }
    }
}
