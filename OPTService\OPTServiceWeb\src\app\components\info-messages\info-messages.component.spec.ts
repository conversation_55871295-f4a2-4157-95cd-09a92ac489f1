import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { InfoMessagesService } from 'src/app/services/info-messages.service';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { SignalRService } from 'src/app/services/signal-r.service';
import { InfoMessagesComponent } from './info-messages.component';

describe('InfoMessagesComponent', () => {
  let infoMessagesComponent: InfoMessagesComponent;  
  let infoMessagesServiceSpy: jasmine.SpyObj<InfoMessagesService>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(() => {
    const infoMessagesSpy = jasmine.createSpyObj('InfoMessagesService', ['getInfoMessageDetails']);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getInfoMessageSignalRMessage']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);

    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
      ],
      providers: [
        InfoMessagesComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: InfoMessagesService, useValue: infoMessagesSpy },
        { provide: SignalRService, useValue: signalRSpy },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    infoMessagesServiceSpy = TestBed.inject(InfoMessagesService) as jasmine.SpyObj<InfoMessagesService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;

    signalRServiceSpy.getInfoMessageSignalRMessage.and.returnValue(of());

    infoMessagesComponent = TestBed.inject(InfoMessagesComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(infoMessagesComponent).toBeTruthy();
  });

  it('.refreshData() should handle success response from service', ()=>{
    //Arrange
    let fakeResponse = [{
      Time: new Date("2021-03-02T17:07:48.1350119+00:00"),
      Message: "Test message"
    }];
    infoMessagesServiceSpy.getInfoMessageDetails.and.returnValue(of(fakeResponse));

    //Act
    infoMessagesComponent.refreshData();

    //Assert
    expect(infoMessagesComponent.infoMessagesData).toEqual(fakeResponse);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', ()=>{
    //Arrange
    infoMessagesServiceSpy.getInfoMessageDetails.and.returnValue(throwError({status:500}));

    //Act
    infoMessagesComponent.refreshData();

    //Assert
    expect(infoMessagesComponent.infoMessagesData).toBeUndefined();
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.getTimestamp() should format AM date correctly', ()=>{
    //Arrange
    let date = new Date('2021-03-01T05:10:15.1234567+00:00');

    //Act
    let result = infoMessagesComponent.getTimestamp(date);

    //Assert
    expect(result).toEqual('01-03-2021 05:10:15.123');
  });

  it('.getTimestamp() should format PM date correctly', ()=>{
    //Arrange
    let date = new Date("2021-03-02T17:07:48.1350119+00:00");
    
    //Act
    let formattedDate = infoMessagesComponent.getTimestamp(date);

    //Assert
    expect(formattedDate).toBe("02-03-2021 17:07:48.135");
  });
});
