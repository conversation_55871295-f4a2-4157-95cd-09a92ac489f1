﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Factories.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Factories;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Logger.Interfaces;
using OPT.Common;
using System;

namespace Forecourt.Pos.Factories
{
    /// <summary>
    /// (Mobile) POS integrator factory
    /// </summary>
    public class MobilePosIntegratorInFactory : PosIntegratorFactory<IPosIntegratorIn<IMessageTracking, IPump, Result>>, IMobilePosIntegratorInFactory
    {
        /// <summary>
        /// Main constructor
        /// </summary>
        /// <param name="resolveTypeInstance">Delegate to resolve a particular type</param>
        /// <inheritdoc cref="Factory{TDiscriminator, T}.Factory(Htec.Logger.Interfaces.Tracing.IHtecLogger, IConfigurationManager)"/>
        public MobilePosIntegratorInFactory(IHtecLogManager logManager, 
            IConfigurationManager configurationManager, 
            Func<string, IPosIntegratorIn<IMessageTracking, IPump, Result>> resolveTypeInstance) 
            : base(logManager, $"{nameof(MobilePosIntegratorInFactory)}", configurationManager, resolveTypeInstance)
        {
            AddItem(ConfigConstants.NoneUpper, ConfigConstants.None, (key) => null);
            AddItem(Core.Configuration.Constants.Integrator.PosMobileTypeHydraMobile, "Htec HydraMobile (Orbis)", (key) => ResolveTypeInstance(key));
            AddItem(Core.Configuration.Constants.Integrator.PosMobileTypeHydraMobileStatus, "Htec HydraMobile (Status only)", (key) => ResolveTypeInstance(key));
        }
    }
}
