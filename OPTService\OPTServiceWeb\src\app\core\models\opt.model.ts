import { Pump } from "./pump.model";

export class Opt{
    StringId: string = '';
    Status: string = '';
    Pumps: Array<Pump> = [];
    Connected: boolean = false;
    SignedIn: boolean = false;
    PrinterError: boolean = false;
    PaperLow: boolean = false;
    HasContactless: boolean = false;
    InUse: boolean = false;
    SoftwareVersion: string = '';
    AvailableSoftware: Array<string> = [];
    AvailableSecureAssets: Array<string> = [];
    AvailableCpatAssets: Array<string> = [];
    IpAddress: string = '';
    Subnet: string = '';
    Gateway: string = '';
    Dns1: string = '';
    Dns2: string = '';
    ReceiptHeaders: Array<string> = [];
    ReceiptFooters: Array<string> = [];
    PlaylistFileName: string = '';
    MediaChannel: boolean = false;
    SecureAssetsVersion: string ='';
    MultimediaAssetsVersion: string = '';
    CpatAssetsVersion: string = '';
    OptFirmwareVersion: string = '';
    EmvKernelVersion: string = '';
    PluginType: string = '';
    PluginVersion: string = '';
    ModeChangePending: boolean = false;
    ConfigChangePending: boolean = false;
    LogFileRequestSent: boolean = false;
    DeviceStatus: string = '';
    DeviceStatusCode: string = '';
}
