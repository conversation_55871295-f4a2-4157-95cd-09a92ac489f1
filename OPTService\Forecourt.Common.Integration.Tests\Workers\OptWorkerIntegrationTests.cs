﻿using FluentAssertions;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Journal.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump.Workers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Messages;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using OPT.Common.HydraDbClasses;
using OPT.Common.Models;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using OPT.TransactionValidator.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Xml.Linq;
using Xunit;
using CardAmountItem = Htec.Hydra.Core.Bos.Messages.Hydra.CardAmountItem;
using CardAmountSaleItem = Htec.Hydra.Core.Bos.Messages.CardAmountSalesItem;
using connGenericEndPoint = Htec.Foundation.Connections.Models.GenericEndPoint;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using PumpStateItem = Htec.Hydra.Messages.Opt.Models.PumpStateItem;
using PumpStateType = Htec.Hydra.Messages.Opt.Models.PumpStateType;
using DeviceState = Htec.Hydra.Messages.Opt.Models.DeviceState;
using ReceiptTransaction = OPT.Common.HydraDbClasses.ReceiptTransaction;
using Wash = OPT.Common.HydraDbClasses.Wash;

namespace OPT.Common.Integration.Tests.Workers
{
    public class OptWorkerIntegrationTests
    {
        private const string HydraId = "Hydra 1";
        private const string OptId = "1234";
        private const string CardNumber = "1234********6789";
        private const string CardProductName = "Visa";
        private const string ReceiptContent = "Receipt Content";
        private const string WhitelistDirectory = "Whitelist";
        private const string SoftwareDirectory = "Software";
        private const string SoftwareVersion = "1.1";
        private const string NewSoftwareVersion = "1.2";
        private const string TicketNumber = "654321";
        private const string Pin = "4321";
        private const string VersionString = "Version";
        private const string TxnNumber = "765";

        private const string DiscountName = "A Discount";
        private const string DiscountType = "AA";
        private const uint DiscountValue = 413;
        private const string DiscountCardNumber = "1111";
        private const uint LocalAccountMileage = 5555;
        private const string LocalAccountRegistration = "DCBA";

        private const string GradeName = "Unleaded";
        private const byte DeliveryPump = 1;
        private const byte DeliveryHose = 2;
        private const byte NextDeliveryPump = 2;
        private const byte DeliveryGrade = 2;
        private const uint DeliveryVolume = 1100;
        private const uint DeliveryAmount = 1320;
        private const ushort DeliveryPrice = 1200;
        private const uint DeliveryNetAmount = 1100;
        private const uint DeliveryVatAmount = 220;
        private const float DeliveryVatRate = 20;
        private const uint AuthAmount = 10000;
        private const int PaymentTimeout = 1000;
        private const int ReceiptLayoutMode = 1;
        private const string SiteName = "A Site";
        private const string VatNumber = "111 1111 11";
        private const int CurrencyCode = 826;

        private const int PaymentTimeoutOptMode = 10;
        private const int PaymentTimeoutPodMode = 20;
        private const int PaymentTimeoutMixedMode = 30;

        private const string TidOne = "99979901";
        private const string TidTwo = "99979902";
        private const string TransactionOne = "100002";
        private const string TransactionTwo = "200003";

        private const string CardAmountCardName = "Visa";
        private const uint CardAmountCardValue = 1000;
        private const int ShiftNumber = 100;

        private readonly TermProcCategory _termProcCategory = new TermProcCategory("", "", "");

        private readonly string _configText = "<config" + " xmlns=\"http://www.htec.co.uk/OPT2\">" + "<site vatNumber=\"" + VatNumber +
                                              "\" name=\"" + SiteName + "\" currencyCode=\"" + CurrencyCode + "\" />" +
                                              "<opt mode=\"0\" receiptLayoutMode=\"1\" receiptHeader=\"\" mixedModeKioskTriggerMode=\"1\" />" +
                                              "<pumps>" + "<pump number=\"1\" tid=\"" + TidOne + "\" transactionNumber=\"" +
                                              TransactionOne + "\" />" + "<pump number=\"2\" tid=\"" + TidTwo + "\" transactionNumber=\"" +
                                              TransactionTwo + "\" />" + "</pumps>" + "<hydraOpt id=\"1234\">" +
                                              "<inbound ip=\"127.0.0.1\" port=\"1263\" />" + "<outbound ip=\"127.0.0.1\" port=\"1262\" />" +
                                              "<heartbeat ip=\"127.0.0.1\" port=\"1264\" />" + "<media ip=\"127.0.0.1\" port=\"1266\" />" +
                                              "</hydraOpt>" + "<esocket />" + "<cards>" + "<aids />" + "<cless_aids />" + "<capks />" +
                                              "<fuelcards />" + "<tariffMapping>" + "<mapping grade=\"1\" productCode=\"11\" />" +
                                              "<mapping grade=\"2\" productCode=\"22\" />" + "</tariffMapping>" + "<discountCards />" +
                                              "<localAccounts />" + "</cards>" + "<loyalty />" + "<washlink />" + "</config>";

        private readonly IFromOptWorker _optWorker;
        private readonly IToOptWorker _toOptProxy;
        private readonly IListenerConnectionThreadXml _fromOptProxy;
        private readonly IOptHeartbeatWorker _heartbeatProxy;
        private readonly IControllerWorker _controllerWorker;
        private readonly IPumpWorker _hscWorker;
        private readonly IDomsWorker _domsWorker;
        private readonly ISecAuthIntegratorOutTransient<IMessageTracking> _secAuthOutWorker;
        private readonly ISecAuthIntegratorInTransient<IMessageTracking> _secAuthInWorker;
        private readonly ICarWashWorker _carWashWorker;
        private readonly IHydraPosWorker _hydraPosWorker;
        private readonly IRetalixPosWorker _retalixPosWorker;
        private readonly IThirdPartyPosWorker _thirdPartyPosWorker;
        private readonly IMediaChannelWorker _mediaChannelWorker;
        private readonly IJournalWorker _journalWorker;
        private readonly ITelemetryWorker _telemetryWorker;
        private readonly IConfigUpdateWorker _configUpdateWorker;
        private readonly ILocalAccountWorker _localAccountWorker;
        private readonly IHydraDb _hydraDb;
        private readonly IPaymentConfigIntegrator _paymentConfig;
        private readonly IContactlessProperties _contactlessProperties;
        private readonly IRetalixTransactionFile _retalixTransactionFile;
        private readonly IPumpCollection _allPumps;
        private readonly IPump _pumpOne;
        private readonly IPump _pumpTwo;
        private readonly IOptCollection _allOpts;
        private readonly IOpt _optOne;
        private readonly IHtecLogger _logger;
        private readonly IHtecLogManager _logManager;
        private readonly IConfigurationManager _configurationManager;

        private readonly IList<uint> _addPaymentAmounts = new List<uint>();
        private readonly IList<bool> _addPaymentAutoAuths = new List<bool>();
        private readonly IList<IList<byte>> _addPaymentRestrictions = new List<IList<byte>>();
        private readonly IList<JournalTotalSalesItem> _totalSalesItems = new List<JournalTotalSalesItem>();
        private readonly IList<IList<JournalFuelSalesItem>> _fuelSalesItems = new List<IList<JournalFuelSalesItem>>();
        private readonly IList<IList<JournalCarWashSalesItem>> _carWashSalesItems = new List<IList<JournalCarWashSalesItem>>();
        private readonly IList<IList<JournalOtherSalesItem>> _otherSalesItems = new List<IList<JournalOtherSalesItem>>();
        private readonly IList<JournalDiscountItem> _discountItems = new List<JournalDiscountItem>();
        private readonly IList<JournalLocalAccountItem> _localAccountItems = new List<JournalLocalAccountItem>();
        private readonly IList<string> _txnNumbers = new List<string>();
        private readonly IList<string> _saveReceiptCardNumbers = new List<string>();
        private readonly IList<ReceiptTransaction> _saveReceipts = new List<ReceiptTransaction>();
        private readonly IList<string> _saveReceiptOpts = new List<string>();
        private readonly IList<XElement> _toOptElements = new List<XElement>();
        private readonly IList<XElement> _fromOptElements = new List<XElement>();
        private readonly IList<int> _toOptIds = new List<int>();

        private readonly SiteInfo _siteInfo = new SiteInfo(ReceiptLayoutMode, SiteName, VatNumber, true, true, CurrencyCode, false, 99, 99, 0);

        private readonly IMessageTracking<XElement> _heartbeatRequest = Substitute.For<IMessageTracking<XElement>>();
        private readonly IMessageTracking<XElement> _heartbeatResponse;
        private readonly IMessageTracking<XElement> _softwareRequest = Substitute.For<IMessageTracking<XElement>>();
        private readonly XElement _softwareResponse;
        private readonly IMessageTracking<XElement> _configRequest = Substitute.For<IMessageTracking<XElement>>();
        private readonly XElement _configResponse;
        private readonly IMessageTracking<XElement> _whitelistRequest;
        private readonly XElement _whitelistResponse;
        private readonly IMessageTracking<XElement> _pumpsStatesRequest;
        private readonly XElement _pumpsStatesResponse;
        private readonly IMessageTracking<XElement> _carWashRequest;
        private readonly XElement _carWashFailureResponse;
        private readonly XElement _carWashSuccessResponse;
        private readonly IMessageTracking<XElement> _signInRequest;
        private readonly XElement _signInResponse;
        private readonly IMessageTracking<XElement> _signOutRequest;
        private readonly XElement _signOutResponse;
        private readonly IMessageTracking<XElement> _paymentApprovedRequest;
        private readonly IMessageTracking<XElement> _paymentCancelledRequest;
        private readonly IMessageTracking<XElement> _paymentClearedRequest;
        private readonly XElement _paymentResponse;
        private readonly IMessageTracking<XElement> _printerOkRequest;
        private readonly IMessageTracking<XElement> _printerPaperLowRequest;
        private readonly IMessageTracking<XElement> _printerErrorRequest;
        private readonly IMessageTracking<XElement> _deviceReadyRequest;
        private readonly IMessageTracking<XElement> _deviceEsocketSignInFailRequest;
        private readonly IMessageTracking<XElement> _deviceEsocketLinkDownRequest;
        private readonly IMessageTracking<XElement> _devicePumpMissingRequest;
        private readonly IMessageTracking<XElement> _devicePumpClosedRequest;
        private readonly IMessageTracking<XElement> _deviceCpatFileMissingRequest;
        private readonly IMessageTracking<XElement> _deviceSecureFileMissingRequest;
        private readonly IMessageTracking<XElement> _deviceInvalidServiceRequest;
        private readonly IMessageTracking<XElement> _deviceInvalidConfigRequest;
        private readonly IMessageTracking<XElement> _deviceEmvNotInitialisedRequest;
        private readonly IMessageTracking<XElement> _deviceHeartbeatFailedRequest;
        private readonly IMessageTracking<XElement> _deviceOptBlockedRequest;
        private readonly IMessageTracking<XElement> _devicePaymentLinkNotSetRequest;
        private readonly IMessageTracking<XElement> _deviceCtlsDataMissingRequest;
        private readonly IMessageTracking<XElement> _cardInsertedRequest;
        private readonly IMessageTracking<XElement> _kioskUseRequest;
        private readonly XElement _optNotificationResponse;
        private readonly IMessageTracking<XElement> _getReceiptRequest;
        private readonly XElement _getReceiptResponse;
        private readonly IMessageTracking<XElement> _storeReceiptRequest;
        private readonly XElement _storeReceiptResponse;
        private readonly IMessageTracking<XElement> _notificationResponse;
        private readonly IMessageTracking<XElement> _deliveredRequest;
        private readonly XElement _deliveringRequest;
        private readonly IMessageTracking<XElement> _nozzleUpRequest;
        private readonly IMessageTracking<XElement> _nozzleUpRequestUnknowNozzle;
        private readonly XElement _nozzleDownRequest;
        private readonly IMessageTracking<XElement> _takeFuelRequest;
        private readonly IMessageTracking<XElement> _paidAtKioskRequest;
        private readonly IMessageTracking<XElement> _configPendingRequest;
        private readonly IMessageTracking<XElement> _restartRequest;
        private readonly IMessageTracking<XElement> _dayEndRequest;
        private readonly ITimerFactory _timerFactory;
        private readonly IMessageTracking _messageTracking = Substitute.For<IMessageTracking>();

        public OptWorkerIntegrationTests()
        {
            _toOptProxy = Substitute.For<IToOptWorker>();
            _fromOptProxy = Substitute.For<IListenerConnectionThreadXml>();
            _heartbeatProxy = Substitute.For<IOptHeartbeatWorker>();

            _timerFactory = Substitute.For<ITimerFactory>();
            //_toOptProxy.SendXml(Arg.Do<XElement>(x => _toOptElements.Add(x)), Arg.Do<int>(x => _toOptIds.Add(x)));
            //_fromOptProxy.SendXml(Arg.Do<XElement>(x => _fromOptElements.Add(x)), Arg.Do<int>(x => _toOptIds.Add(x)));
            _controllerWorker = Substitute.For<IControllerWorker>();
            _hscWorker = Substitute.For<IPumpWorker>();
            _domsWorker = Substitute.For<IDomsWorker>();
            _secAuthOutWorker = Substitute.For<ISecAuthIntegratorOutTransient<IMessageTracking>>();
            _secAuthInWorker = Substitute.For<ISecAuthIntegratorInTransient<IMessageTracking>>();
            _carWashWorker = Substitute.For<ICarWashWorker>();
            _carWashWorker.IsConnected().Returns(true);
            _hydraPosWorker = Substitute.For<IHydraPosWorker>();
            _retalixPosWorker = Substitute.For<IRetalixPosWorker>();
            _thirdPartyPosWorker = Substitute.For<IThirdPartyPosWorker>();
            _mediaChannelWorker = Substitute.For<IMediaChannelWorker>();
            _retalixTransactionFile = Substitute.For<IRetalixTransactionFile>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _journalWorker = Substitute.For<IJournalWorker>();
            _journalWorker.WriteSalesItems(Arg.Do<JournalTotalSalesItem>(x => _totalSalesItems.Add(x)),
                Arg.Do<IList<JournalFuelSalesItem>>(x => _fuelSalesItems.Add(x)),
                Arg.Do<IList<JournalCarWashSalesItem>>(x => _carWashSalesItems.Add(x)),
                Arg.Do<IList<JournalOtherSalesItem>>(x => _otherSalesItems.Add(x)), Arg.Do<JournalDiscountItem>(x => _discountItems.Add(x)),
                Arg.Do<JournalLocalAccountItem>(x => _localAccountItems.Add(x)), Arg.Do<string>(x => _txnNumbers.Add(x)), out _);
            _telemetryWorker = Substitute.For<ITelemetryWorker>();
            _configUpdateWorker = Substitute.For<IConfigUpdateWorker>();
            _localAccountWorker = Substitute.For<ILocalAccountWorker>();
            _hydraDb = Substitute.For<IHydraDb>();
            _hydraDb.FetchEndPoints("").ReturnsForAnyArgs(new OptEndPoints());
            _hydraDb.FetchTariffMappings().ReturnsForAnyArgs(new List<TariffMapping>
                {new TariffMapping(1, "11", false), new TariffMapping(2, "22", false)});
            _hydraDb.GetReceipt(CardNumber).Returns(new ReceiptInfo(null, ReceiptContent, DateTime.Now, null, 1, 500, DateTime.Now, 1));
            _hydraDb.SaveReceipt(Arg.Do<string>(x => _saveReceiptCardNumbers.Add(x)), Arg.Do<ReceiptTransaction>(x => _saveReceipts.Add(x)),
                Arg.Do<string>(x => _saveReceiptOpts.Add(x)), Arg.Any<long>());
            _hydraDb.GetFileLocations().Returns(new AllFileLocations("", "", WhitelistDirectory, "", SoftwareDirectory, "", "", "", "", "",
                "", "", "", "", "", "", "", "", true, "", "", "", false, false, false, false));
            _hydraDb.GetSiteInfo().Returns(_siteInfo);
            _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Opt).Returns(PaymentTimeoutOptMode);
            _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Pod).Returns(PaymentTimeoutPodMode);
            _hydraDb.FetchPaymentTimeout(PaymentTimeoutType.Mixed).Returns(PaymentTimeoutMixedMode);
            _paymentConfig = Substitute.For<IPaymentConfigIntegrator>();
            _paymentConfig.TermProcCategory.Returns(_termProcCategory);
            _paymentConfig.AllPumpTids.Returns(new List<TermId>
                {new TermId(TidOne, TransactionOne), new TermId(TidTwo, TransactionTwo)});
            _contactlessProperties = Substitute.For<IContactlessProperties>();
            _optOne = Substitute.For<IOpt>();
            _optOne.Id.Returns(1);
            _optOne.IdString.Returns(OptId);
            _optOne.SoftwareToSend.Returns(NewSoftwareVersion);
            _allOpts = Substitute.For<IOptCollection>();
            _allOpts.TryGetOpt(0, out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (int)x[0] == 1 ? _optOne : null;
                return (int)x[0] == 1;
            });
            _allOpts.GetOptIdForIdString("").ReturnsForAnyArgs(x => x[0].Equals(OptId) ? 1 : 0);
            _allOpts.AllOpts.Returns(new List<IOpt> { _optOne });

            _pumpOne = Substitute.For<IPump>();
            _pumpOne.Number.Returns(DeliveryPump);
            _pumpOne.Opt.Returns(_optOne);
            _pumpOne.OptUse.Returns(true);
            _pumpOne.AddPayment(Arg.Do<uint>(x => _addPaymentAmounts.Add(x)), Arg.Do<bool>(x => _addPaymentAutoAuths.Add(x)),
                Arg.Do<IList<byte>>(x => _addPaymentRestrictions.Add(x)));
            _pumpOne.IsAuthorised(out _).ReturnsForAnyArgs(x =>
            {
                x[0] = AuthAmount;
                return true;
            });
            _pumpOne.Tid.Returns(TidOne);
            _pumpOne.AllGrades.Returns(new List<byte> { 1, 2 });
            _pumpTwo = Substitute.For<IPump>();
            _pumpTwo.OptUse.Returns(true);
            _pumpTwo.Opt.Returns(_optOne);
            _pumpTwo.Tid.Returns(TidTwo);
            _allPumps = Substitute.For<IPumpCollection>();
            _allPumps.GetPumpsStates(0).ReturnsForAnyArgs(new List<PumpStateItem> { new PumpStateItem(1, PumpStateType.Closed) });
            _allPumps.TryGetPump(0, out _).ReturnsForAnyArgs(x =>
            {
                x[1] = (byte)x[0] == DeliveryPump ? _pumpOne : (byte)x[0] == NextDeliveryPump ? _pumpTwo : null;
                return (byte)x[0] == DeliveryPump || (byte)x[0] == NextDeliveryPump;
            });
            _allPumps.AllPumps.Returns(new List<IPump> { _pumpOne, _pumpTwo });
            _optOne.AddNotification(Arg.Do<OptRequest>(x => _optOne.GetCurrentNotification().Returns(x)));
            _optOne.PumpList().Returns(new List<byte> { DeliveryPump, NextDeliveryPump });

            _logManager = Substitute.For<IHtecLogManager>();
            _logger = Substitute.For<IHtecLogger>();
            var pumpTransactionValidator = Substitute.For<IPumpTransactionValidator>();
            var cacheHelper = Substitute.For<ICacheHelper>();
            _optWorker = new FromOptWorker(_toOptProxy, _heartbeatProxy, _fromOptProxy, _controllerWorker, _hscWorker, _secAuthInWorker, _secAuthOutWorker,
                _carWashWorker, _hydraPosWorker, _hydraPosWorker, _mediaChannelWorker, _journalWorker,
                _telemetryWorker, _configUpdateWorker, _localAccountWorker, _hydraDb, _paymentConfig, _allPumps,
                _allOpts, _logManager, VersionString, _configurationManager, _timerFactory, Substitute.For<IGradeHelper>(), Substitute.For<IVatCalculator>(), pumpTransactionValidator, cacheHelper);
            _optWorker.SetGradeName(DeliveryGrade, GradeName);
            _optWorker.FetchGenericOptConfig(true);

            _heartbeatRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Heartbeat"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _heartbeatResponse.Response.Returns(new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "Heartbeat"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)))));
            _softwareRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Software"),
                    new XElement("OPT", new XAttribute("Id", OptId), new XAttribute("SoftwareVersion", SoftwareVersion)))));
            _softwareResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "Software"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Software",
                        new XElement("File", new XAttribute("Name", "OPTApp"),
                            Convert.ToBase64String(Encoding.ASCII.GetBytes("Software"))))));
            _configRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "GetConfig"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _configResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetConfig"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Config", _configText)));
            _whitelistRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Whitelist"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _whitelistResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "Whitelist"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Whitelist",
                        new XElement("File", new XAttribute("Name", "whitelist.txt"),
                            Convert.ToBase64String(Encoding.ASCII.GetBytes("Whitelist"))))));
            _pumpsStatesRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "GetPumpsStates"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _pumpsStatesResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetPumpsStates"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Pumps",
                        new XElement("PumpState", new XAttribute("Pump", DeliveryPump), new XAttribute("State", "Closed")))));
            _carWashRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "GetCarWash"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Wash", new XAttribute("ProgramId", "1")))));
            _carWashFailureResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetCarWash"), new XAttribute("Result", "Failure"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId))));
            _carWashSuccessResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetCarWash"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Wash", new XAttribute("TicketNumber", TicketNumber), new XAttribute("Pin", Pin))));
            _signInRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "SignIn"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _signInResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "SignIn"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId), new XAttribute("SoftwareVersion", VersionString)),
                    new XElement("Hydra", new XAttribute("Id", HydraId))));
            _signOutRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "SignOut"), new XElement("OPT", new XAttribute("Id", OptId)))));
            _signOutResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "SignOut"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId))));
            _paymentApprovedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Payment"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Payment", new XAttribute("Result", "Approved"), new XAttribute("Pump", DeliveryPump),
                        new XAttribute("AmountAuthed", AuthAmount), new XElement("CardRestrictions", new XAttribute("Codes", "11,22"))))));
            _paymentCancelledRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Payment"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Payment", new XAttribute("Result", "Cancelled"), new XAttribute("Pump", DeliveryPump),
                        new XAttribute("TxnNumber", TxnNumber), new XAttribute("AmountAuthed", AuthAmount)))));
            _paymentClearedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Payment"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Payment", new XAttribute("Result", "Cleared"), new XAttribute("Pump", 1),
                        new XAttribute("AmountAuthed", DeliveryAmount), new XAttribute("CardNumber", CardNumber),
                        new XAttribute("CardProductName", CardProductName), new XAttribute("TxnNumber", TxnNumber),
                        new XElement("Products",
                            new XElement("Product", new XAttribute("Code", "22"), new XAttribute("Quantity", DeliveryVolume),
                                new XAttribute("Value", DeliveryAmount))),
                        new XElement("Discount", new XAttribute("Name", DiscountName), new XAttribute("Type", DiscountType),
                            new XAttribute("Value", DiscountValue), new XAttribute("CardNumber", DiscountCardNumber)),
                        new XElement("LocalAccount", new XAttribute("Mileage", LocalAccountMileage),
                            new XAttribute("Registration", LocalAccountRegistration))))));
            _paymentResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "Payment"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId))));
            _printerOkRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "PrinterStatus"),
                        new XElement("Printer", new XAttribute("Status", "PrinterOK"))))));
            _printerPaperLowRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "PrinterStatus"),
                        new XElement("Printer", new XAttribute("Status", "PrinterPaperLow"))))));
            _printerErrorRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "PrinterStatus"),
                        new XElement("Printer", new XAttribute("Status", "PrinterError"))))));
            _deviceReadyRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "Ready"))))));
            _deviceEsocketSignInFailRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "EsocketSignInFail"))))));
            _deviceEsocketLinkDownRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "EsocketLinkDown"))))));
            _devicePumpMissingRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "PumpMissing"))))));
            _devicePumpClosedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "PumpClosed"))))));
            _deviceCpatFileMissingRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "CpatFileMissing"))))));
            _deviceSecureFileMissingRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "SecureFileMissing"))))));
            _deviceInvalidServiceRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "InvalidServiceAddress"))))));
            _deviceInvalidConfigRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "InvalidConfig"))))));
            _deviceEmvNotInitialisedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "EmvNotInitialised"))))));
            _deviceHeartbeatFailedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "HeartbeatFailed"))))));
            _deviceOptBlockedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "OptBlocked"))))));
            _devicePaymentLinkNotSetRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "PaymentLinkNotSet"))))));
            _deviceCtlsDataMissingRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "DeviceState"),
                        new XElement("Device", new XAttribute("State", "ContactlessDataMissing"))))));
            _cardInsertedRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "CardInserted"), new XAttribute("Pump", DeliveryPump)))));
            _kioskUseRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "OptNotification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Notification", new XAttribute("Type", "KioskUse"), new XAttribute("Pump", DeliveryPump)))));
            _optNotificationResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "OptNotification"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId))));
            _getReceiptRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "GetReceipt"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Receipt", new XAttribute("CardNumber", CardNumber)))));
            _getReceiptResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetReceipt"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Receipt", ReceiptContent)));
            _storeReceiptRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "StoreReceipt"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Receipt", new XAttribute("CardNumber", CardNumber), ReceiptContent))));
            _storeReceiptResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "StoreReceipt"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId))));
            _notificationResponse.Response.Returns(new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "Notification"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)))));
            _deliveredRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "Delivered"), new XAttribute("Pump", NextDeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", NextDeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", DeliveryVolume), new XAttribute("Amount", DeliveryAmount),
                            new XAttribute("Name", GradeName), new XAttribute("Price", DeliveryPrice),
                            new XAttribute("NetAmount", DeliveryNetAmount), new XAttribute("VatAmount", DeliveryVatAmount),
                            new XAttribute("VatRate", DeliveryVatRate))))));
            _deliveringRequest = new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "Delivering"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", GradeName),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0)))));
            _nozzleUpRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "NozzleUp"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", GradeName),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0))))));
            _nozzleUpRequestUnknowNozzle.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "NozzleUp"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", 0),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", ConfigConstants.Unknown),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0))))));
            _nozzleDownRequest = new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "NozzleDown"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", GradeName),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0)))));
            _takeFuelRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "TakeFuel"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", GradeName),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0))))));
            _paidAtKioskRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "PaidAtKiosk"), new XAttribute("Pump", DeliveryPump),
                        new XElement("DeliveryDetails", new XAttribute("Pump", DeliveryPump), new XAttribute("Grade", DeliveryGrade),
                            new XAttribute("Volume", 0), new XAttribute("Amount", 0), new XAttribute("Name", GradeName),
                            new XAttribute("Price", DeliveryPrice), new XAttribute("NetAmount", 0), new XAttribute("VatAmount", 0),
                            new XAttribute("VatRate", 0))))));
            _configPendingRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "ConfigPending")))));
            _restartRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)), new XElement("Notification", new XAttribute("Type", "Restart")))));
            _dayEndRequest.Request.Returns(new XElement("HydraOPT",
                new XElement("Request", new XAttribute("Type", "Notification"), new XElement("OPT", new XAttribute("Id", OptId)),
                    new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Notification", new XAttribute("Type", "EndOfDay"),
                        new XElement("Totals", new XAttribute("ShiftNumber", ShiftNumber),
                            new XElement("TotalsGroup", new XAttribute("CardProductName", CardAmountCardName),
                                new XAttribute("Amount", CardAmountCardValue)))))));
        }

        [Fact]
        public void test_receive_payment_approved_from_opt()
        {
            // Arrange
            const int id = 1;
            IList<byte> expectedRestrictions = new List<byte> { 1, 2 };
            _pumpOne.CanAddPayment.Returns(true);

            // Act
            _optWorker.SetAutoAuth(false);
            var result = _optWorker.OnMessageReceived(_paymentApprovedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_paymentResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _addPaymentAmounts.Should().NotBeEmpty();
            foreach (uint item in _addPaymentAmounts)
            {
                item.Should().Be(AuthAmount);
            }

            _addPaymentAutoAuths.Should().NotBeEmpty();
            foreach (bool item in _addPaymentAutoAuths)
            {
                item.Should().Be(true);
            }

            _addPaymentRestrictions.Should().NotBeEmpty();
            foreach (IList<byte> item in _addPaymentRestrictions)
            {
                item.Should().BeEquivalentTo(expectedRestrictions);
            }

            _secAuthOutWorker.Received(1).PostAuthRequest(Arg.Any<SecAuthRequest>(), _messageTracking);
            _thirdPartyPosWorker.Received(1).SendAuthRequestToPos(DeliveryPump, AuthAmount);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_payment_cancelled_from_opt()
        {
            // Arrange
            const int id = 1;
            _pumpOne.CanCancelPayment.Returns(true);
            _optWorker.SetAutoAuth(false);

            // Act
            var result = _optWorker.OnMessageReceived(_paymentCancelledRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_paymentResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _pumpOne.Received(1).CancelPayment();
            _thirdPartyPosWorker.Received(1).SendClearRequestToPos(DeliveryPump);
            _txnNumbers.Should().NotBeEmpty();
            _txnNumbers.Should().Contain(TxnNumber);
        }

        [Fact]
        public void test_receive_payment_cleared_from_opt()
        {
            // Arrange
            const int id = 1;
            _pumpOne.CanClearPayment.Returns(true);
            _pumpOne.DeliveredHose.Returns(DeliveryHose);
            // TODO: Moved over to RetalixTransactionFile
            //_retalixPosWorker.WhenForAnyArgs(x => x.SendTransaction(Arg.Any<RetalixTransactionFileItem>(), string.Empty, out _))
            //    .Do(x => x[1] = new List<IPAddress>());

            // Act
            var result = _optWorker.OnMessageReceived(_paymentClearedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_paymentResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _pumpOne.Received(1).ClearPayment(CardNumber, CardProductName, DeliveryAmount);
            _txnNumbers.Should().NotBeEmpty();
            _txnNumbers.Should().Contain(TxnNumber);
            _totalSalesItems.Should().NotBeEmpty();
            foreach (JournalTotalSalesItem item in _totalSalesItems)
            {
                item.Amount.Should().Be(DeliveryAmount);
                item.CardNumber.Should().BeEquivalentTo(CardNumber);
                item.CardProductName.Should().BeEquivalentTo(CardProductName);
                item.Hose.Should().Be(DeliveryHose);
                item.PumpNumber.Should().Be(DeliveryPump);
            }

            _fuelSalesItems.Should().NotBeEmpty();
            foreach (IList<JournalFuelSalesItem> items in _fuelSalesItems)
            {
                items.Should().NotBeEmpty();
                foreach (JournalFuelSalesItem item in items)
                {
                    item.Should().NotBeNull();
                    item.Amount.Should().Be(DeliveryAmount);
                    item.Grade.Should().Be(DeliveryGrade);
                    item.GradeName.Should().BeEquivalentTo(GradeName);
                    item.Quantity.Should().Be(DeliveryVolume);
                }
            }

            _carWashSalesItems.Should().NotBeEmpty();
            foreach (IList<JournalCarWashSalesItem> items in _carWashSalesItems)
            {
                items.Should().BeEmpty();
            }

            _otherSalesItems.Should().NotBeEmpty();
            foreach (IList<JournalOtherSalesItem> items in _otherSalesItems)
            {
                items.Should().BeEmpty();
            }

            _discountItems.Should().NotBeEmpty();
            foreach (JournalDiscountItem item in _discountItems)
            {
                item.Should().NotBeNull();
                item.Name.Should().BeEquivalentTo(DiscountName);
                item.Type.Should().BeEquivalentTo(DiscountType);
                item.Value.Should().Be(DiscountValue);
                item.CardNumber.Should().BeEquivalentTo(DiscountCardNumber);
            }

            _localAccountItems.Should().NotBeEmpty();
            foreach (JournalLocalAccountItem item in _localAccountItems)
            {
                item.Should().NotBeNull();
                item.Mileage.Should().Be(LocalAccountMileage);
                item.Registration.Should().BeEquivalentTo(LocalAccountRegistration);
            }
        }

        [Fact]
        public void test_receive_printer_ok_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_printerOkRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetPrinterStatus();
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_printer_paper_low_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_printerPaperLowRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetPrinterStatus(false, true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_printer_error_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_printerErrorRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetPrinterStatus(true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number  );
        }

        [Fact]
        public void test_receive_device_ready_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceReadyRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.Ready);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_esocket_signin_fail_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceEsocketSignInFailRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.EsocketSignInFail);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_esocket_link_down_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceEsocketLinkDownRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_pump_missing_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_devicePumpMissingRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.PumpMissing);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_pump_closed_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_devicePumpClosedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.PumpClosed);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_cpat_missing_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceCpatFileMissingRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.CpatFileMissing);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_secure_missing_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceSecureFileMissingRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.SecureFileMissing);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_invalid_service_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceInvalidServiceRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.InvalidServiceAddress);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_invalid_config_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceInvalidConfigRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.InvalidConfig);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_emv_not_initialised_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceEmvNotInitialisedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.EmvNotInitialised);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_heartbeat_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceHeartbeatFailedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.HeartbeatFailed);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_opt_blocked_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceOptBlockedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.OptBlocked);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_payment_link_invalid_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_devicePaymentLinkNotSetRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.PaymentLinkNotSet);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_device_contactless_data_missing_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_deviceCtlsDataMissingRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _optOne.Received(1).SetDeviceState(DeviceState.ContactlessDataMissing);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
            _hydraPosWorker.Received(1).StatusResponse(_pumpTwo.Number);
        }

        [Fact]
        public void test_receive_card_inserted_from_opt()
        {
            // Arrange
            const int id = 1;
            _pumpOne.CanInsertCard.Returns(true);

            // Act
            var result = _optWorker.OnMessageReceived(_cardInsertedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _pumpOne.Received().CardInserted();
            _secAuthOutWorker.Received(1).PostAuthRequest(Arg.Any<SecAuthRequest>(), _messageTracking);
        }

        [Fact]
        public void test_receive_card_inserted_from_opt_out_of_sequence()
        {
            // Arrange
            const int id = 1;
            bool first = true;
            _pumpOne.CanInsertCard.Returns(x =>
            {
                if (first)
                {
                    first = false;
                    return false;
                }

                return true;
            });

            // Act
            var result = _optWorker.OnMessageReceived(_cardInsertedRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _pumpOne.Received().CardInserted();
            _pumpOne.Received().ResetPump();
            _secAuthOutWorker.Received(1).PostAuthRequest(Arg.Any<SecAuthRequest>(), _messageTracking);
            _hydraPosWorker.Received(1).StatusResponse(_pumpOne.Number);
        }

        [Fact]
        public void test_receive_kiosk_use_from_opt()
        {
            // Arrange
            const int id = 1;
            _pumpOne.CanSetKioskUse.Returns(true);

            // Act
            var result = _optWorker.OnMessageReceived(_kioskUseRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_optNotificationResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _pumpOne.Received().SetKioskUse();
        }

        [Fact]
        public void test_receive_get_receipt_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_getReceiptRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_getReceiptResponse);
            _optOne.Received(1).SetSignInRequired(true);
        }

        [Fact]
        public void test_receive_store_receipt_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_storeReceiptRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_storeReceiptResponse);
            _optOne.Received(1).SetSignInRequired(true);
            _journalWorker.Received(1).ReceiptJournal(ReceiptContent, null);
            _saveReceiptCardNumbers.Should().NotBeEmpty();
            foreach (string item in _saveReceiptCardNumbers)
            {
                item.Should().BeEquivalentTo(CardNumber);
            }

            _saveReceipts.Should().NotBeEmpty();
            foreach (var item in _saveReceipts)
            {
                item.Should().BeEquivalentTo(new ReceiptTransaction{Details = ReceiptContent});
            }

            _saveReceiptOpts.Should().NotBeEmpty();
            foreach (string item in _saveReceiptOpts)
            {
                item.Should().BeEquivalentTo(OptId);
            }
        }

        [Fact]
        public void test_receive_notification_response_from_opt_and_send_delivered()
        {
            // Arrange
            const int id = 1;
            bool first = true;
            _optOne.GetCurrentNotification().Returns(x =>
                first
                    ? new DeliveredNotificationRequest(DeliveryPump, DeliveryGrade, DeliveryVolume, DeliveryAmount, GradeName, DeliveryPrice,
                        DeliveryNetAmount, DeliveryVatAmount, DeliveryVatRate)
                    : new DeliveredNotificationRequest(NextDeliveryPump, DeliveryGrade, DeliveryVolume, DeliveryAmount, GradeName, DeliveryPrice,
                        DeliveryNetAmount, DeliveryVatAmount, DeliveryVatRate));
            _optOne.When(x => x.NotificationResponseReceived()).Do(x => first = false);

            // Act
            var result = _optWorker.OnMessageReceived(_notificationResponse, id);

            // Assert
            result.Should().BeNull();
            _optOne.Received(1).NotificationResponseReceived();
            _hydraDb.Received(1).ClearDelivered(DeliveryPump);
            _toOptIds.Should().NotBeEmpty();
            foreach (int item in _toOptIds)
            {
                item.Should().Be(id);
            }

            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_deliveredRequest.Request);
            }
        }

        [Fact]
        public void test_receive_request_pump_state()
        {
            // Arrange
            List<byte> grades = new List<byte>();
            _pumpOne.Requesting(Arg.Do<IList<byte>>(x => grades.AddRange(x)));

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Request, DeliveryPump, 0, DeliveryGrade, 0, 0, DeliveryPrice, true, new List<byte>());

            // Assert
            grades.Should().BeEquivalentTo(new List<byte> { DeliveryGrade });
            _pumpOne.Received(1).IsAuthorised(out _);
            _hscWorker.Received(1).PaymentApproved(Arg.Is<byte>(x => x == DeliveryPump), AuthAmount);
            _hydraDb.Received(1).SetOptPayment(DeliveryPump);
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_nozzleUpRequest.Request);
            }
        }

        [Fact]
        public void test_receive_request_pump_state_unknown_nozzle()
        {
            // Arrange
            List<byte> grades = new List<byte>();
            _pumpOne.Requesting(Arg.Do<IList<byte>>(x => grades.AddRange(x)));

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Request, DeliveryPump, 0, 0, 0, 0, DeliveryPrice, true, new List<byte> { 1, 2 });

            // Assert
            grades.Should().BeEquivalentTo(new List<byte> { 1, 2 });
            _pumpOne.Received(1).IsAuthorised(out _);
            _hscWorker.Received(1).PaymentApproved(Arg.Is<byte>(x => x == DeliveryPump), AuthAmount);
            _hydraDb.Received(1).SetOptPayment(DeliveryPump);
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_nozzleUpRequestUnknowNozzle.Request);
            }
        }

        [Fact]
        public void test_receive_idle_pump_state_with_opt_payment()
        {
            // Arrange
            _pumpTwo.HasOptPayment.Returns(true);

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Idle, NextDeliveryPump, DeliveryHose, DeliveryGrade, DeliveryVolume, DeliveryAmount,
                DeliveryPrice, true, new List<byte>());

            // Assert
            _hscWorker.Received(1).PaymentCleared(NextDeliveryPump, DeliveryAmount);
            _hydraDb.Received(1).SetDelivered(_messageTracking, NextDeliveryPump, DeliveryGrade, DeliveryVolume, DeliveryAmount, GradeName, DeliveryPrice,
                DeliveryNetAmount, DeliveryVatAmount, DeliveryVatRate, 0, 0);
            _hydraDb.Received(1).ClearOptPayment(NextDeliveryPump);
            _pumpTwo.Received().SetDeliveredHose(DeliveryHose, DeliveryPrice);
            _pumpTwo.Received(1).Idle();
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_deliveredRequest.Request);
            }
        }

        [Fact]
        public void test_receive_idle_pump_state_without_opt_payment()
        {
            // Arrange
            _pumpOne.IsNozzleUp.Returns(true);
            _pumpOne.HasPayment.Returns(true);

            // Act
            _optWorker.SetPaymentTimeout(PaymentTimeoutType.NozzleDown, PaymentTimeout);
            _optWorker.OnPumpState(_messageTracking, PumpState.Idle, DeliveryPump, DeliveryHose, DeliveryGrade, DeliveryVolume, DeliveryAmount, DeliveryPrice,
                true, new List<byte>());
            int result = _optWorker.GetPaymentTimeout(PaymentTimeoutType.NozzleDown);

            // Assert
            _pumpOne.Received(1).ResetPaymentTimeout(PaymentTimeout);
            _pumpOne.Received(1).Idle();
            _hydraDb.Received(1).ClearOptPayment(DeliveryPump);
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_nozzleDownRequest);
            }

            result.Should().Be(PaymentTimeout);
        }

        [Fact]
        public void test_receive_idle_pump_state_with_kiosk_payment_paid()
        {
            // Arrange
            _pumpOne.IsPaid.Returns(false);
            _pumpOne.HasKioskPayment.Returns(true);

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Idle, DeliveryPump, DeliveryHose, DeliveryGrade, DeliveryVolume, DeliveryAmount, DeliveryPrice,
                true, new List<byte>());

            // Assert
            _pumpOne.Received(1).Idle();
            _pumpOne.Received(1).SetPaid(true);
            _hydraDb.Received(1).ClearOptPayment(DeliveryPump);
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_paidAtKioskRequest.Request);
            }
        }

        [Fact]
        public void test_reload_listeners()
        {
            // Arrange
            _hydraDb.FetchEndPoints("").ReturnsForAnyArgs(new OptEndPoints(fromOptPort: 8262, toOptPort: 8263, heartbeatPort: 8264));

            // Act
            _optWorker.Restart();

            // Assert
            _controllerWorker.Received(1).ReloadListener();
            _hydraPosWorker.Received(1).Restart();
            _retalixPosWorker.Received(1).Restart();
            _thirdPartyPosWorker.Received(1).Restart();
        }

        [Fact]
        public void test_car_wash_no_ticket()
        {
            // Arrange

            // Act
            _optWorker.CarWashNoTicket(_optOne);

            // Assert
            _fromOptElements.Should().NotBeEmpty();
            foreach (XElement item in _fromOptElements)
            {
                item.Should().BeEquivalentTo(_carWashFailureResponse);
            }
        }

        [Fact]
        public void test_car_wash_with_ticket()
        {
            // Arrange

            // Act
            _optWorker.CarWashTicket(_optOne, TicketNumber, "", Pin);

            // Assert
            _fromOptElements.Should().NotBeEmpty();
            foreach (XElement item in _fromOptElements)
            {
                item.Should().BeEquivalentTo(_carWashSuccessResponse);
            }
        }

        [Fact]
        public void test_send_day_end_notification()
        {
            // Arrange
            IList<CardAmountItem> cardAmounts = new List<CardAmountItem> { new CardAmountItem(1, CardAmountCardName, CardAmountCardValue) };

            // Act
            _optWorker.SendDayEndNotification(cardAmounts.Select(x => (CardAmountSaleItem)x), ShiftNumber);

            // Assert
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_dayEndRequest.Request);
            }
        }

        [Fact]
        public void test_get_all_ip_addresses()
        {
            // Arrange
            _allOpts.AllOptsAllConnected.Returns(new List<IOpt> { _optOne });

            // Act
            IList<IPAddress> result = _optWorker.GetAllIpAddresses().ToList();

            // Assert
            result.Should().HaveCount(1);
            foreach (IPAddress address in result)
            {
                address.Should().Be(IPAddress.Loopback);
            }
        }

        [Fact]
        public void test_receive_delivering_pump_state()
        {
            // Arrange

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Delivering, DeliveryPump, 0, DeliveryGrade, 0, 0, DeliveryPrice, true, new List<byte>());

            // Assert
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_deliveringRequest);
            }

            _pumpOne.Received(1).Delivering();
        }

        [Fact]
        public void test_receive_authorise_pump_state()
        {
            // Arrange

            // Act
            _optWorker.OnPumpState(_messageTracking, PumpState.Authorise, DeliveryPump, 0, DeliveryGrade, 0, 0, DeliveryPrice, true, new List<byte>());

            // Assert
            _pumpOne.Received(1).Authorised();
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_takeFuelRequest.Request);
            }
        }

        [Fact]
        public void test_redirect()
        {
            // Arrange

            // Act
            _optWorker.Redirect("Hydra 2");

            // Assert
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_configPendingRequest.Request);
            }
        }

        [Fact]
        public void test_restart_all()
        {
            // Arrange

            // Act
            _optWorker.RestartAll();

            // Assert
            _toOptElements.Should().NotBeEmpty();
            foreach (XElement item in _toOptElements)
            {
                item.Should().BeEquivalentTo(_restartRequest.Request);
            }
        }

        [Fact]
        public void test_register_disconnect()
        {
            // Arrange
            const SocketType socketType = SocketType.FromOpt;
            _optOne.FromOptConnected.Returns(true);

            _fromOptProxy.IsConnected()
                .Returns(true);

            // Act
            _optWorker.Disconnect(1);

            // Assert
            _optOne.Received(1).SignOut();
            _optOne.Received(1).Disconnected(socketType);
        }

        [Fact]
        public void test_receive_get_config_from_opt()
        {
            // Arrange
            const int id = 1;

            // Act
            var result = _optWorker.OnMessageReceived(_configRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(_configResponse);
        }

        [Fact]
        public void test_receive_get_config_from_opt_more_details()
        {
            // Arrange
            const int id = 1;
            const string receiptHeader = "A Receipt Header";
            const string esocketIpAddress = "127.0.0.1";
            const int esocketPort = 10000;
            const string aidAid = "An AID";
            const string appVerTerm = "App Ver Term";
            const string tacDefault = "TAC Default";
            const string tacDenial = "TAC Denial";
            const string tacOnline = "TAC Online";
            const string partialMatch = "Partial Match";
            const string tdol = "A TDOL";
            const string ddol = "A DDOL";
            const string floorLimit = "Floor Limit";
            const string emvTarget = "EMV Target";
            const string emvMaxTarget = "EMV Max Target";
            const string emvThreshold = "EMV Threshold";
            const string transLimit = "Trans Limit";
            const string cvmLimit = "CVM Limit";
            const string odcvmLimit = "ODCVM Limit";
            const string termAddCapabilities = "Term Add Capabilities";
            const string termCapabilitiesCvm = "Term Capabilities CVM";
            const string termCapabilitiesNoCvm = "Term Capabilities No CVM";
            const string termRiskData = "Term Risk Data";
            const string udol = "A UDOL";
            const string programId = "Program ID";
            const string ridRid = "A RID";
            const string index = "An Index";
            const string modulus = "A Modulus";
            const string exponent = "An Exponent";
            const string checksum = "A Checksum";
            const string expiryDate = "A Date";
            const string iinStart = "IIN Start";
            const string iinEnd = "IIN End";
            const string onlinePin = "Online PIN";
            const string siteId = "Site ID";
            const string terminalId = "Terminal ID";
            const string footer1 = "Footer One";
            const string footer2 = "Footer Two";
            const int timeout = 1000;
            const string apiKey = "API Key";
            const string httpHeader = "HTTP Header";
            const string loyaltyIpAddress = "*********";
            const int loyaltyPort = 1010;
            const string loyaltyHostname = "Host";
            const string iinLow = "Low IIN";
            const string iinHigh = "High IIN";
            const string productCode = "Product Code";
            const string loyaltyCode = "Loyalty Code";
            const int washProgramId = 45;
            const string washDescription = "A Description";
            const string washPrice = "100";
            const string washVatRate = "20.0";
            const int predefinedAmount1 = 2000;
            const int predefinedAmount2 = 3000;
            const string discountIin = "IIN One";
            const string discountName = "A Discount";
            const string discountType = "P1";
            const double discountValue = 12.3;
            const byte discountGrade = 15;
            const string pan1 = "PAN 1";
            const string pan2 = "PAN 2";
            const string pan3 = "PAN 3";
            const string pan4 = "PAN 4";
            const string localAccountRef = "A Reference";
            const string localAccountName = "A Local Account";
            const int localAccountTransLimit = 50;
            const string description3 = "Description 3";
            const string description4 = "Description 4";
            const double discount3 = 100;
            const double discount4 = 200;

            _optOne.IsInPodMode.Returns(true);
            _optOne.ReceiptHeader.Returns(receiptHeader);
            _optOne.HasContactless.Returns(true);
            _hydraDb.FetchESocketEndPoints().Returns(new List<connGenericEndPoint> { new ESocketEndPoint(esocketIpAddress, esocketPort) });
            _paymentConfig.CardAids.Returns(new List<CardAid>
            {
                new CardAid(aidAid, appVerTerm, tacDefault, tacDenial, tacOnline, partialMatch, tdol, ddol, floorLimit, emvTarget,
                    emvMaxTarget, emvThreshold)
            });
            _contactlessProperties.Cards.Returns(new List<CardClessAid>
            {
                new CardClessAid(aidAid, appVerTerm, transLimit, floorLimit, cvmLimit, odcvmLimit, termAddCapabilities, termCapabilitiesCvm,
                    termCapabilitiesNoCvm, termRiskData, udol, tacDefault, tacDenial, tacOnline, "", "", "", "", "", "", "", "", "", "", "", "")
            });
            _contactlessProperties.Drls.Returns(new List<CardClessDrl>
                {new CardClessDrl(aidAid, programId, transLimit, cvmLimit, floorLimit)});
            _paymentConfig.CardCapks.Returns(new List<CardCapk> { new CardCapk(ridRid, index, modulus, exponent, checksum, expiryDate) });
            _paymentConfig.FuelCards.Returns(new List<FuelCard> { new FuelCard(iinStart, iinEnd, onlinePin) });
            _hydraDb.FetchGenericLoyalty(GenericOptConfig.MorrisonsName).Returns(new GenericLoyalty(
                new LoyaltyTerminal(siteId, terminalId, footer1, footer2, timeout, apiKey, httpHeader),
                new List<GenericEndPoint> { new GenericEndPoint(loyaltyIpAddress, loyaltyPort) }, new List<string> { loyaltyHostname },
                new List<LoyaltyIin> { new LoyaltyIin(iinLow, iinHigh) },
                new List<LoyaltyMapping> { new LoyaltyMapping(productCode, loyaltyCode) }, true));
            _hydraDb.FetchWashes().Returns(new List<Wash>
                {new Wash(washProgramId, productCode, washDescription, washPrice, washVatRate, 0, 0)});
            _hydraDb.FetchPredefinedAmounts().Returns(new List<int> { predefinedAmount1, predefinedAmount2 });
            DiscountCard discountCard = new DiscountCard(discountIin, discountName, discountType, discountValue, discountGrade);
            discountCard.SetWhitelist(new List<string> { pan1, pan2 });
            _hydraDb.FetchDiscountCards().Returns(new List<DiscountCard> { discountCard });
            LocalAccountCustomer localAccountCustomer = new LocalAccountCustomer(localAccountRef, localAccountName, true,
                localAccountTransLimit, true, true, true, true, true, true, true, false, false, 100, true);
            localAccountCustomer.SetCards(new List<LocalAccountCard>
            {
                new LocalAccountCard(pan3, description3, discount3, true, false, false, false, false, false, false, false, false, false,
                    false, false, false, false, false, false),
                new LocalAccountCard(pan4, description4, discount4, false, true, true, true, false, false, false, false, false, false,
                    false, false, false, false, true, false)
            });
            _localAccountWorker.Customers.Returns(new List<LocalAccountCustomer> { localAccountCustomer });
            string configText = "<config xmlns=\"http://www.htec.co.uk/OPT2\">" +
                                $"<site vatNumber=\"{VatNumber}\" name=\"{SiteName}\" currencyCode=\"{CurrencyCode}\" type=\"Retail\" ignoreMessageId=\"false\" />" +
                                $"<opt mode=\"2\" receiptLayoutMode=\"1\" receiptHeader=\"{receiptHeader}\" mixedModeKioskTriggerMode=\"1\">" +
                                $"<predefinedAmounts><amount value=\"{predefinedAmount1}\" />" +
                                $"<amount value=\"{predefinedAmount2}\" /></predefinedAmounts>" +
                                $"</opt>" +
                                $"<pumps>" +
                                $"<pump number=\"1\" tid=\"{TidOne}\" transactionNumber=\"{TransactionOne}\" />" +
                                $"<pump number=\"2\" tid=\"{TidTwo} \" transactionNumber=\"{TransactionTwo}\" />" +
                                $"</pumps>" +
                                "<hydraOpt id=\"1234\">" + "<inbound ip=\"127.0.0.1\" port=\"1263\" />" +
                                "<outbound ip=\"127.0.0.1\" port=\"1262\" />" + "<heartbeat ip=\"127.0.0.1\" port=\"1264\" />" +
                                "<media ip=\"127.0.0.1\" port=\"1266\" />" + "</hydraOpt>" + "<esocket><host ip=\"" + esocketIpAddress +
                                "\" port=\"" + esocketPort + "\" /></esocket>" + "<cards>" + "<aids>" + "<aid aid=\"" + aidAid +
                                "\" appVerTerm=\"" + appVerTerm + "\" tacDefault=\"" + tacDefault + "\" tacDenial=\"" + tacDenial +
                                "\" tacOnline=\"" + tacOnline + "\" partialMatch=\"" + partialMatch + "\" tdol=\"" + tdol + "\" ddol=\"" +
                                ddol + "\" floorLimit=\"" + floorLimit + "\" emvTarget=\"" + emvTarget + "\" emvMaxTarget=\"" +
                                emvMaxTarget + "\" emvThreshold=\"" + emvThreshold + "\" /></aids>" + "<cless_aids>" + "<cless_aid aid=\"" +
                                aidAid + "\" appVerTerm=\"" + appVerTerm + "\" transLimit=\"" + transLimit + "\" floorLimit=\"" +
                                floorLimit + "\" cvmLimit=\"" + cvmLimit + "\" odcvmLimit=\"" + odcvmLimit + "\" termAddCapabilities=\"" +
                                termAddCapabilities + "\" termCapabilitiesCvm=\"" + termCapabilitiesCvm + "\" termCapabilitiesNoCvm=\"" +
                                termCapabilitiesNoCvm + "\" termRiskData=\"" + termRiskData + "\" udol=\"" + udol + "\" tacDefault=\"" +
                                tacDefault + "\" tacDenial=\"" + tacDenial + "\" tacOnline=\"" + tacOnline + "\"><drl programId=\"" +
                                programId + "\" transLimit=\"" + transLimit + "\" floorLimit=\"" + floorLimit + "\" cvmLimit=\"" +
                                cvmLimit + "\" /></cless_aid>" + "</cless_aids>" + "<capks>" + "<capk rid=\"" + ridRid + "\" index=\"" +
                                index + "\" modulus=\"" + modulus + "\" exponent=\"" + exponent + "\" checksum=\"" + checksum +
                                "\" expiryDate=\"" + expiryDate + "\" />" + "</capks>" + "<fuelcards>" + "<fuelcard iinStart=\"" +
                                iinStart + "\" iinEnd=\"" + iinEnd + "\" onlinePin=\"" + onlinePin + "\" />" + "</fuelcards>" +
                                "<tariffMapping>" + "<mapping grade=\"1\" productCode=\"11\" />" +
                                "<mapping grade=\"2\" productCode=\"22\" />" + "</tariffMapping>" + "<discountCards>" +
                                "<discountRange iin=\"" + discountIin + "\" name=\"" + discountName + "\" type=\"" + discountType +
                                "\" value=\"" + discountValue.ToString("F2") + "\" grade=\"" + discountGrade + "\">" + "<whitelist>" +
                                "<card pan=\"" + pan1 + "\" />" + "<card pan=\"" + pan2 + "\" />" + "</whitelist>" + "</discountRange>" +
                                "</discountCards>" + "<localAccounts>" + "<customer name=\"" + localAccountName +
                                "\" pin=\"1\" printValue=\"1\" allowLoyalty=\"1\" fuelOnly=\"1\" getMiles=\"1\" getReg=\"1\" isPrePay=\"1\"" +
                                " transLimit=\"" + localAccountTransLimit + "\">" + "<cards>" + "<card pan=\"" + pan3 +
                                "\" description=\"" + description3 + "\" discount=\"" + discount3 +
                                "\" restrictions1=\"0\" restrictions2=\"0\" />" + "<card pan=\"" + pan4 + "\" description=\"" +
                                description4 + "\" discount=\"" + discount4 + "\" restrictions1=\"7\" restrictions2=\"64\" />" +
                                "</cards>" + "</customer>" + "</localAccounts>" + "</cards>" + "<loyalty>" + "<morrisons>" +
                                "<terminal siteId=\"" + siteId + "\" terminalId=\"" + terminalId + "\" footer1=\"" + footer1 +
                                "\" footer2=\"" + footer2 + "\" timeout=\"" + timeout + "\" apiKey=\"" + apiKey + "\" httpHeader=\"" +
                                httpHeader + "\" />" + "<hosts>" + "<host ip=\"" + loyaltyIpAddress + "\" port=\"" + loyaltyPort + "\" />" +
                                "</hosts>" + "<hostnames>" + "<hostname name=\"" + loyaltyHostname + "\" />" + "</hostnames>" + "<iins>" +
                                "<iin low=\"" + iinLow + "\" high=\"" + iinHigh + "\" />" + "</iins>" + "<tariffMapping>" +
                                "<mapping productCode=\"" + productCode + "\" loyaltyCode=\"" + loyaltyCode + "\" />" + "</tariffMapping>" +
                                "</morrisons>" + "</loyalty>" + "<washlink>" + "<wash programId=\"" + washProgramId + "\" productCode=\"" +
                                productCode + "\" description=\"" + washDescription + "\" price=\"" + washPrice + "\" vatRate=\"" +
                                washVatRate + "\" />" + "</washlink>" + "</config>";
            XElement configResponse = new XElement("HydraOPT",
                new XElement("Response", new XAttribute("Type", "GetConfig"), new XAttribute("Result", "Success"),
                    new XElement("OPT", new XAttribute("Id", OptId)), new XElement("Hydra", new XAttribute("Id", HydraId)),
                    new XElement("Config", configText)));

            // Act
            var result = _optWorker.OnMessageReceived(_configRequest, id);

            // Assert
            result.Should().NotBeNull();
            result.Value.Should().NotBeNull();
            result.Value.Should().BeEquivalentTo(configResponse);
        }
    }
}
