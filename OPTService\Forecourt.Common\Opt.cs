﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Workers;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Hydra.Messages.Opt.Messages;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Hydra.Messages.Opt.RequestResponse;
using Htec.Hydra.Messages.Opt.RequestResponse.Notifications;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using static Forecourt.Core.Opt.Enums.OptEnums;
using optMessages = Htec.Hydra.Messages.Opt;
using PumpModeType = Forecourt.Core.Pump.Enums.PumpModeType;

namespace OPT.Common
{

    public abstract class OptCore : Loggable, IOptCore
    {
        private const int MinutesToWaitForTxn = 10;

        public string HydraId { get; private set; }

        public string IdString { get; private set; }

        public int Id { get; private set; }

        public virtual string FullIdentifier(string header = ConfigConstants.NameOptUpper, string separator = "") =>
            $"{header}{separator}{(string.IsNullOrWhiteSpace(header + separator) ? string.Empty : " ")}{IdString} ({Id})";

        public bool SignedIn { get; private set; } = false;
        public bool SignInRequired { get; private set; } = false;

        public bool Connected => DoConnected();

        public bool Offline => DoOffline();

        protected IDictionary<byte, IPump> Pumps { get; } = new ConcurrentDictionary<byte, IPump>();

        public OptModeType Mode
        {
            get
            {
                if (Pumps.Count == 0)
                {
                    return OptModeType.OptModeNotSet;
                }

                return Pumps.Values.All(x => x.OutsideOnly) ? OptModeType.OptModeOpt
                    : Pumps.Values.All(x => x.IsKioskMode) ? OptModeType.OptModeKioskOnly
                    : !Pumps.Values.Any(x => x.Mixed) && Pumps.Values.Any(x => x.OutsideOnly || x.PumpMode == PumpModeType.KioskOnly) ? OptModeType.OptModeOpt
                    : Pumps.Values.Any(x => x.Mixed || x.OutsideOnly) ? OptModeType.OptModeMixed
                    : OptModeType.OptModeNotSet;
            }
        }

        protected OptCore(IHtecLogger logger, string idString, int id, string hydraId) : base(logger)
        {
            DoCtor(idString, id, hydraId);
        }

        private void DoCtor(string idString, int id, string hydraId)
        {
            IdString = idString;
            Id = id;
            HydraId = hydraId;
            TxnExpiryTime = DateTime.MaxValue;
        }

        protected OptCore(IHtecLogManager logMan, string idString, int id, string hydraId, IConfigurationManager configurationManager) : base(logMan, Opt.LoggerName, configurationManager)
        {
            DoCtor(idString, id, hydraId);
        }

        protected abstract bool DoConnected();

        protected abstract bool DoOffline();

        public void SetSignInRequired(bool signInRequired)
        {
            SignInRequired = signInRequired;
        }

        public void SignIn()
        {
            SignedIn = true;
        }

        public void SignOut()
        {
            SignedIn = false;
        }

        protected IDictionary<string, long> TransactionNumbers { get; } = new ConcurrentDictionary<string, long>();
        protected IDictionary<string, ReceiptTransaction> TransactionReceipts { get; } = new ConcurrentDictionary<string, ReceiptTransaction>();

        public void StoreTxnTransaction(string txnNumber, long transactionNumber)
        {
            CheckTxnExpiry();
            TransactionNumbers[txnNumber] = transactionNumber;
        }

        public long FetchTxnTransaction(string txnNumber)
        {
            long value = 0;
            if (TransactionNumbers.ContainsKey(txnNumber))
            {
                value = TransactionNumbers[txnNumber];
                TransactionNumbers.Remove(txnNumber);
            }
            return value;
        }

        public void StoreTxnReceipt(string txnNumber, ReceiptTransaction receipt)
        {
            CheckTxnExpiry();
            TransactionReceipts[txnNumber] = receipt;
        }

        public ReceiptTransaction FetchTxnReceipt(string txnNumber)
        {
            ReceiptTransaction value = null;
            if (TransactionReceipts.ContainsKey(txnNumber))
            {
                value = TransactionReceipts[txnNumber];
                TransactionReceipts.Remove(txnNumber);
            }
            return value;
        }

        private DateTime TxnExpiryTime { get; set; }

        private void CheckTxnExpiry()
        {
            if (TxnExpiryTime < DateTime.Now)
            {
                TransactionNumbers.Clear();
                TransactionReceipts.Clear();
            }
            TxnExpiryTime = DateTime.Now.AddMinutes(MinutesToWaitForTxn);
        }

        protected virtual string DoFormatReceiptForLogging(string receipt)
        {
            return receipt;
        }

        public string FormatReceiptForLogging(string receipt)
        {
            return DoFormatReceiptForLogging(receipt);
        }

        protected virtual string DoFormatReceiptForJournal(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null)
        {
            return receipt;
        }

        public string FormatReceiptForJournal(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null)
        {
            return DoFormatReceiptForJournal(receipt);
        }

        protected virtual Result<string> DoGetRawReceiptText(ReceiptInfo info, bool centreAlign)
        {
            return Result.Success(info.ReceiptContent);
        }

        public Result<string> GetRawReceiptText(ReceiptInfo info, bool centreAlign = false)
        {
            return DoGetRawReceiptText(info, centreAlign);
        }

        public IList<byte> PumpList()
        {
            return Pumps.Keys.ToList();
        }
    }

    ///
    ///  Name: OptId
    ///  Description: State of an OPT.
    ///
    [HasConfiguration()]
    public class Opt : OptCore, IOpt
    {
        public const string LoggerName = ConfigConstants.LoggerPrefixOptService + nameof(Opt);

        private const int MaxAgeOfHeartbeatInSeconds = 10;
        private const int MaxAgeOfResponseInSeconds = 2;
        public const int DefaultPaymentTimeoutInSeconds = 30;
        public const int DefaultPayAtKioskPressedTimeoutInSeconds = ConfigConstants.DefaultPaymentTimeoutInSeconds;

        public optMessages.Xsd.configType SentConfig { get; private set; }
        public WhitelistResponse SentWhitelist { get; private set; }
        public LayoutResponse SentLayout { get; private set; }
        public IList<string> SentMediaFilesList { get; } = new List<string>();
        public string SoftwareToSend { get; private set; }
        public bool IsSecureAssetsToSend => _secureAssetsToSend != null;
        public string AssetToSend => _secureAssetsToSend ?? _cpatAssetsToSend;
        public string ReceiptHeader { get; private set; }
        public string ReceiptFooter { get; private set; }
        public string PlaylistFileName { get; private set; }
        private DateTime? _lastLogTime;
        private int _logInterval = 86400;

        private string _secureAssetsToSend = null;
        private string _cpatAssetsToSend = null;

        public int ToOptReconnectionCount { get; set; } = 1;
        public bool ToOptConnected { get; private set; } = false;
        public bool FromOptConnected { get; private set; } = false;
        public bool HeartbeatConnected { get; private set; } = false;
        private PrinterStatus _printerStatus = PrinterStatus.PrinterOK;
        private DeviceState _deviceState = DeviceState.Ready;
        private ConfigStatus _softwareStatus = ConfigStatus.ConfigSent;
        private ConfigStatus _assetStatus = ConfigStatus.ConfigSent;
        private ConfigStatus _configStatus = ConfigStatus.NotificationPending;
        private ConfigStatus _whitelistStatus = ConfigStatus.NotificationPending;
        private ConfigStatus _layoutStatus = ConfigStatus.NotificationPending;
        private ConfigStatus _mediaUpdateStatus = ConfigStatus.NotificationPending;
        public bool PrinterError => _printerStatus == PrinterStatus.PrinterError;
        public bool PaperLow => _printerStatus == PrinterStatus.PrinterPaperLow;
        protected override bool DoConnected() => ToOptConnected || FromOptConnected || HeartbeatConnected;
        public bool AllConnected => ToOptConnected && FromOptConnected && HeartbeatConnected;
        public uint CashLimit { get; } = 10000;
        protected override bool DoOffline() => !AllConnected;

        private bool PendingIsInUse(bool checkSignedIn = true, bool checkInUse = true) => Connected && (!checkInUse || !InUse) && (!checkSignedIn || SignedIn);

        public bool SoftwareNotificationPending =>
            _softwareStatus == ConfigStatus.NotificationPending && PendingIsInUse();

        public bool ConfigNotificationPending =>
            _configStatus == ConfigStatus.NotificationPending && PendingIsInUse() &&
            _softwareStatus == ConfigStatus.ConfigSent;

        public bool WhitelistNotificationPending =>
            _whitelistStatus == ConfigStatus.NotificationPending && PendingIsInUse() &&
            _softwareStatus == ConfigStatus.ConfigSent && _configStatus == ConfigStatus.ConfigSent;

        public bool LayoutNotificationPending =>
            _layoutStatus == ConfigStatus.NotificationPending && PendingIsInUse() &&
            _mediaUpdateStatus == ConfigStatus.ConfigSent && _whitelistStatus == ConfigStatus.ConfigSent &&
            _softwareStatus == ConfigStatus.ConfigSent && _configStatus == ConfigStatus.ConfigSent;

        public bool MediaUpdateNotificationPending =>
            _mediaUpdateStatus == ConfigStatus.NotificationPending && PendingIsInUse() &&
            _whitelistStatus == ConfigStatus.ConfigSent && _softwareStatus == ConfigStatus.ConfigSent &&
            _configStatus == ConfigStatus.ConfigSent;

        public bool LogFileRequestSent { get; set; } = false;

        public bool LogFileRequestPending =>
            (_lastLogTime == null || _lastLogTime.Value.AddSeconds(_logInterval) < DateTime.Now) && PendingIsInUse() &&
            _mediaUpdateStatus == ConfigStatus.ConfigSent && _whitelistStatus == ConfigStatus.ConfigSent &&
            _softwareStatus == ConfigStatus.ConfigSent && _configStatus == ConfigStatus.ConfigSent;

        public bool AssetNotificationPending =>
            _assetStatus == ConfigStatus.NotificationPending && PendingIsInUse() &&
            _whitelistStatus == ConfigStatus.ConfigSent && _softwareStatus == ConfigStatus.ConfigSent &&
            _configStatus == ConfigStatus.ConfigSent && _mediaUpdateStatus == ConfigStatus.ConfigSent && _layoutStatus == ConfigStatus.ConfigSent;


        public bool ConfigCheckPending => _configStatus == ConfigStatus.CheckPending && PendingIsInUse(false);
        public bool WhitelistCheckPending => _whitelistStatus == ConfigStatus.CheckPending && PendingIsInUse(false);
        public bool LayoutCheckPending => _layoutStatus == ConfigStatus.CheckPending && PendingIsInUse(false);
        public bool MediaUpdateCheckPending => _mediaUpdateStatus == ConfigStatus.CheckPending && PendingIsInUse(false);

        public bool ModeChangePending
        {
            get
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{IdString}", () => new[]
                {
                    $"{nameof(_configStatus)}: {_configStatus}; {nameof(PendingIsInUse)}(f, f): {PendingIsInUse(false, false)}",
                    $"{nameof(ModeChanged)}()/Current/Sent: {ModeChanged()}/{Mode}/{(OptModeType)(SentConfig?.opt?.mode ?? 0)}; Pumps.ModePending: {Pumps.Any(x => x.Value.ModePending)}",
                });

                return _configStatus == ConfigStatus.ConfigSent && PendingIsInUse(false, false) && (ModeChanged() || Pumps.Any(x => x.Value.ModePending));
            }
        }

        public bool ConfigChangePending => _configStatus != ConfigStatus.ConfigSent && Connected;

        public bool InUse => Pumps.Values.Any(x => x.InUseByOpt);
        public bool InKioskUse => Pumps.Values.All(x => x.KioskUse);
        public bool HasContactless { get; private set; } = false;
        public bool RequestSentToOpt { get; private set; } = false;
        private DateTime RequestExpiryTime { get; set; }
        private DateTime HeartbeatExpiryTime { get; set; }

        private readonly Queue<OptRequest> _queue = new Queue<OptRequest>();

        private readonly object _currentNotificationSyncObject = new object();

        private OptRequest CurrentNotification { get; set; } = null;

        public OptRequest GetCurrentNotification()
        {
            lock (_currentNotificationSyncObject)
            {
                return CurrentNotification;
            }
        }

        public int PaymentTimeoutInSeconds { get; set; } = DefaultPaymentTimeoutInSeconds;
        public int PayAtKioskPressedTimeoutInSeconds { get; set; } = DefaultPayAtKioskPressedTimeoutInSeconds;
        public string SoftwareVersion { get; private set; } = null;
        public string SecureAssetsVersion { get; private set; } = null;
        public string MultimediaAssetsVersion { get; private set; } = null;
        public string CpatAssetsVersion { get; private set; } = null;
        public string OptFirmwareVersion { get; private set; } = null;
        public string EmvKernelVersion { get; private set; } = null;
        public string PluginType { get; private set; } = null;
        public string PluginVersion { get; private set; } = null;
        public string Subnet { get; private set; } = null;
        public string Gateway { get; private set; } = null;
        public string Dns1 { get; private set; } = null;
        public string Dns2 { get; private set; } = null;

        /// <inheritdoc />
        public MessageBuilder MessageBuilder { get; private set; }

        private readonly IPrinterHelper<IMessageTracking> _printerHelper;
        private readonly IReceiptHelper _receiptHelper;

        [Obsolete("Pending Unit Test re-factoring!")]
        public Opt(IHtecLogger logger, string idString, int id, string hydraId, OptMode optMode = null) : base(logger, idString, id, hydraId)
        {
            DoCtor(optMode);
        }

        public Opt(IHtecLogManager logMan, string idString, int id, string hydraId, IConfigurationManager configurationManager, OptMode optMode = null, 
            IPrinterHelper<IMessageTracking> printerHelper = null, IReceiptHelper receiptHelper = null) : base(logMan, idString, id, hydraId, configurationManager)
        {
            DoCtor(optMode);

            _printerHelper = printerHelper;
            _receiptHelper = receiptHelper;
        }

        private void DoCtor(OptMode optMode)
        {
            HeartbeatExpiryTime = DateTime.Now.AddSeconds(MaxAgeOfHeartbeatInSeconds);

            MessageBuilder = new MessageBuilder(HydraId, IdString);

            if (optMode != null)
            {
                SetReceiptHeader(optMode.ReceiptHeader);
                SetReceiptFooter(optMode.ReceiptFooter);
                SetPlaylistFileName(optMode.PlaylistFileName);
                SetContactless(optMode.Contactless);
                SetLastLogTime(optMode.LastLogTime);
            }
        }

        public bool IsNozzleUp(byte pump)
        {
            return Pumps.TryGetValue(pump, out var thePump) && thePump.IsNozzleUp;
        }

        public bool AddNotification(OptRequest notification)
        {
            var result = false;
            lock (_currentNotificationSyncObject)
            {
                if (CurrentNotification == null)
                {
                    CurrentNotification = notification;
                    result = true;
                }
                else if (!(notification is EndOfDayNotificationRequest) || !(CurrentNotification is EndOfDayNotificationRequest) && _queue.All(x => !(x is EndOfDayNotificationRequest)))
                {
                    _queue.Enqueue(notification);
                }
            }

            return result;
        }

        public OptRequest NotificationResponseReceived()
        {
            OptRequest result;
            lock (_currentNotificationSyncObject)
            {
                result = CurrentNotification;
                if (_queue.Any())
                {
                    CurrentNotification = _queue.Dequeue();
                }
                else
                {
                    CurrentNotification = null;
                }
            }

            return result;
        }

        public void Received(SocketType socket)
        {
            // ReSharper disable once ConvertIfStatementToSwitchStatement
            if (socket == SocketType.ToOpt)
            {
                ToOptConnected = true;
                HeartbeatExpiryTime = DateTime.Now.AddSeconds(MaxAgeOfHeartbeatInSeconds);
                RequestSentToOpt = false;
            }
            else if (socket == SocketType.FromOpt)
            {
                FromOptConnected = true;
                HeartbeatExpiryTime = DateTime.Now.AddSeconds(MaxAgeOfHeartbeatInSeconds);
            }

            else if (socket == SocketType.Heartbeat)
            {
                HeartbeatConnected = true;
                HeartbeatExpiryTime = DateTime.Now.AddSeconds(MaxAgeOfHeartbeatInSeconds);
            }
        }

        public void Disconnected(SocketType socket)
        {
            // ReSharper disable once ConvertIfStatementToSwitchStatement
            if (socket == SocketType.ToOpt)
            {
                ToOptConnected = false;
                RequestSentToOpt = false;
            }
            else if (socket == SocketType.FromOpt)
            {
                FromOptConnected = false;
            }

            else if (socket == SocketType.Heartbeat)
            {
                HeartbeatConnected = false;
            }

            if (_configStatus == ConfigStatus.NotificationSent)
            {
                _configStatus = ConfigStatus.NotificationPending;
            }

            if (_whitelistStatus == ConfigStatus.NotificationSent)
            {
                _whitelistStatus = ConfigStatus.NotificationPending;
            }

            if (_layoutStatus == ConfigStatus.NotificationSent)
            {
                _layoutStatus = ConfigStatus.NotificationPending;
            }

            if (_mediaUpdateStatus == ConfigStatus.NotificationSent)
            {
                _mediaUpdateStatus = ConfigStatus.NotificationPending;
            }
        }


        public void SendingRequestToOpt(DateTime? expiresAt = null)
        {
            RequestExpiryTime = (expiresAt == null) || expiresAt.Equals(DateTime.MinValue) ? DateTime.Now.AddSeconds(MaxAgeOfResponseInSeconds) : expiresAt.Value.ToLocalTime();

            RequestSentToOpt = true;
        }

        public bool HasTimeoutExpired()
        {
            var now = DateTime.Now;
            var result = HeartbeatExpiryTime < now || RequestSentToOpt && RequestExpiryTime < now;
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "ConnectedStatus.OPT", () => new[]
            {
                $"{IdString} ({Id})",
                $"Now: {now:HH:mm:ss.fff}; Heartbeat: {HeartbeatExpiryTime:HH:mm:ss.fff}; RequestSentToOpt: {RequestSentToOpt}; RequestExpiryTime: {RequestExpiryTime:HH:mm:ss.fff}"
            });
            return result;
        }

        public void ClearPumps()
        {
            Pumps.Clear();
        }

        public void AddPump(IPump pump)
        {
            if (!Pumps.ContainsKey(pump.Number))
            {
                Pumps.Add(pump.Number, pump);
            }
        }

        public void RemovePump(IPump pump)
        {
            Pumps.Remove(pump.Number);
        }


        public void SetPaymentTimeoutInSeconds(int timeout)
        {
            PaymentTimeoutInSeconds = timeout;
        }

        public void SetContactless(bool value)
        {
            HasContactless = value;
        }      

        public void SetPrinterStatus(bool error = false, bool paperLow = false)
        {
            if (error)
            {
                _printerStatus = PrinterStatus.PrinterError;
            }
            else if (paperLow)
            {
                _printerStatus = PrinterStatus.PrinterPaperLow;
            }
            else
            {
                _printerStatus = PrinterStatus.PrinterOK;
            }
        }

        public void SetDeviceState(DeviceState state)
        {
            _deviceState = state;
        }

        public void ConfigSent(optMessages.Xsd.configType config)
        {
            SentConfig = config;
            _configStatus = ConfigStatus.ConfigSent;
        }

        public void WhitelistSent(WhitelistResponse whitelist)
        {
            SentWhitelist = whitelist;
            _whitelistStatus = ConfigStatus.ConfigSent;
        }

        public void LayoutSent(LayoutResponse layout)
        {
            SentLayout = layout;
            _layoutStatus = ConfigStatus.ConfigSent;
        }

        public void MediaFilesListSent(IList<string> files)
        {
            SentMediaFilesList.Clear();
            foreach (string file in files)
            {
                SentMediaFilesList.Add(file);
            }

            _mediaUpdateStatus = ConfigStatus.ConfigSent;
        }

        public void ConfigPendingSent()
        {
            _configStatus = ConfigStatus.NotificationSent;
        }

        public void WhitelistPendingSent()
        {
            _whitelistStatus = ConfigStatus.NotificationSent;
        }

        public void LayoutPendingSent()
        {
            _layoutStatus = ConfigStatus.NotificationSent;
        }

        public void MediaUpdatePendingSent()
        {
            _mediaUpdateStatus = ConfigStatus.NotificationSent;
        }

        public void ConfigCheckRequired()
        {
            if (_configStatus == ConfigStatus.ConfigSent)
            {
                _configStatus = ConfigStatus.CheckPending;
            }
        }

        public void WhitelistCheckRequired()
        {
            if (_whitelistStatus == ConfigStatus.ConfigSent)
            {
                _whitelistStatus = ConfigStatus.CheckPending;
            }
        }

        public void LayoutCheckRequired()
        {
            if (_layoutStatus == ConfigStatus.ConfigSent)
            {
                _layoutStatus = ConfigStatus.CheckPending;
            }
        }

        public void MediaUpdateCheckRequired()
        {
            if (_mediaUpdateStatus == ConfigStatus.ConfigSent)
            {
                _mediaUpdateStatus = ConfigStatus.CheckPending;
            }
        }

        public void ConfigCheckCleared()
        {
            if (_configStatus == ConfigStatus.CheckPending)
            {
                _configStatus = ConfigStatus.ConfigSent;
            }
        }

        public void WhitelistCheckCleared()
        {
            if (_whitelistStatus == ConfigStatus.CheckPending)
            {
                _whitelistStatus = ConfigStatus.ConfigSent;
            }
        }

        public void LayoutCheckCleared()
        {
            if (_layoutStatus == ConfigStatus.CheckPending)
            {
                _layoutStatus = ConfigStatus.ConfigSent;
            }
        }

        public void MediaUpdateCheckCleared()
        {
            if (_mediaUpdateStatus == ConfigStatus.CheckPending)
            {
                _mediaUpdateStatus = ConfigStatus.ConfigSent;
            }
        }

        public void ConfigNeeded()
        {
            if (_configStatus != ConfigStatus.NotificationSent)
            {
                _configStatus = ConfigStatus.NotificationPending;
            }
        }

        public void WhitelistNeeded()
        {
            if (_whitelistStatus != ConfigStatus.NotificationSent)
            {
                _whitelistStatus = ConfigStatus.NotificationPending;
            }
        }

        public void LayoutNeeded()
        {
            if (_layoutStatus != ConfigStatus.NotificationSent)
            {
                _layoutStatus = ConfigStatus.NotificationPending;
            }
        }

        public void MediaUpdateNeeded()
        {
            if (_mediaUpdateStatus != ConfigStatus.NotificationSent)
            {
                _mediaUpdateStatus = ConfigStatus.NotificationPending;
            }
        }

        public void ModeChangeSent()
        {
            if (SentConfig?.opt != null)
            {
                SentConfig.opt.mode = (int) Mode;
            }
        }

        public void SetSoftwareVersion(string softwareVersion)
        {
            SoftwareVersion = softwareVersion;
            CheckSoftwareVersion();
        }

        public void SetSecureAssetsVersion(string secureAssetsVersion)
        {
            SecureAssetsVersion = secureAssetsVersion;
        }

        public void SetMultimediaAssetsVersion(string multimediaAssetsVersion)
        {
            MultimediaAssetsVersion = multimediaAssetsVersion;
        }

        public void SetCpatAssetsVersion(string cpatAssetsVersion)
        {
            CpatAssetsVersion = cpatAssetsVersion;
        }

        public void SetOptFirmwareVersion(string optFirmwareVersion)
        {
            OptFirmwareVersion = optFirmwareVersion;
        }

        public void SetEmvKernelVersion(string emvKernelVersion)
        {
            EmvKernelVersion = emvKernelVersion;
        }

        public void SetPluginType(string plugInType)
        {
            PluginType = plugInType;
        }

        public void SetPluginVersion(string plugInVersion)
        {
            PluginVersion = plugInVersion;
        }

        public void SoftwarePendingSent()
        {
            _softwareStatus = ConfigStatus.NotificationSent;
        }

        public void AssetPendingSent()
        {
            _assetStatus = ConfigStatus.NotificationSent;
        }

        public void SetSubnet(string subnet)
        {
            Subnet = subnet;
        }

        public void SetGateway(string gateway)
        {
            Gateway = gateway;
        }

        public void SetDns1(string dns1)
        {
            Dns1 = dns1;
        }

        public void SetDns2(string dns2)
        {
            Dns2 = dns2;
        }

        public void SoftwareSent()
        {
            _softwareStatus = ConfigStatus.ConfigSent;
            SoftwareToSend = null;
        }

        public void AssetSent()
        {
            if (IsSecureAssetsToSend)
            {
                _secureAssetsToSend = null;
            }
            else
            {
                _cpatAssetsToSend = null;
            }

            if (_secureAssetsToSend == null && _cpatAssetsToSend == null)
            {
                _assetStatus = ConfigStatus.ConfigSent;
            }
            else
            {
                _assetStatus = ConfigStatus.NotificationPending;
            }
        }

        public void SetReceiptHeader(string receiptHeader)
        {
            ReceiptHeader = receiptHeader;
        }

        public void SetReceiptFooter(string receiptFooter)
        {
            ReceiptFooter = receiptFooter;
        }

        public void SetPlaylistFileName(string playlistFileName)
        {
            PlaylistFileName = playlistFileName;
        }

        public void SetLastLogTime(DateTime? logTime)
        {
            _lastLogTime = logTime;
        }

        public void SetLogInterval(int interval)
        {
            _logInterval = interval;
        }

        private void CheckSoftwareVersion()
        {
            if (SoftwareToSend?.Equals(SoftwareVersion) == false)
            {
                if (_softwareStatus == ConfigStatus.ConfigSent)
                {
                    _softwareStatus = ConfigStatus.NotificationPending;
                }
            }
            else
            {
                SoftwareToSend = null;
            }
        }

        private bool ModeChanged()
        {
            return SentConfig?.opt == null || Mode != (OptModeType)SentConfig.opt.mode;
        }

        public override string ToString()
        {
            return IdString;
        }

        /// <inheritdoc cref="IOpt.IsInPodMode"/>
        public bool IsInPodMode => Pumps.Values.Distinct().Count() > 1;

        /// <inheritdoc />
        public DeviceState DeviceStatus => _deviceState;

        /// <inheritdoc cref="IOpt.GetModeStatusDescription"/>

        public string GetModeStatusDescription()
        {
            var mode = Mode;

            return IsInPodMode
                ? "Pod"
                : mode == OptModeType.OptModeOpt
                    ? "OPT"
                    : mode == OptModeType.OptModeMixed
                        ? "Mixed"
                        : mode == OptModeType.OptModeKioskOnly
                            ? "Kiosk Only"
                            : null;
        }

        protected override string DoFormatReceiptForLogging(string receipt)
        {
            var xml = XElement.Parse(receipt);
            var results = xml.Elements().Where(x =>
                    !string.IsNullOrWhiteSpace(x.Attribute("Text")?.Value) || !string.IsNullOrWhiteSpace(x.Attribute("Left")?.Value) || !string.IsNullOrWhiteSpace(x.Attribute("right")?.Value))
                .Select(x => new { Text = $"{x.Attribute("Text")?.Value}{x.Attribute("Left")?.Value}{x.Attribute("right")?.Value}" });
            return string.Join(Environment.NewLine, results.Select(x => x.Text.Trim()));
        }

        protected override string DoFormatReceiptForJournal(string receipt, bool isPrint = false, bool useControlChars = true, IMessageTracking message = null)
        {
            return _printerHelper?.FormatReceipt(receipt, isPrint, useControlChars, message) ?? base.DoFormatReceiptForJournal(receipt, isPrint, useControlChars, message);
        }

        protected override Result<string> DoGetRawReceiptText(ReceiptInfo info, bool centreAlign)
        {
            var result = _receiptHelper?.GetRawReceiptText(info, centreAlign) ?? Result.Failure<IEnumerable<string>>("No IReceiptHelper");

            return !result.IsSuccess ? base.GetRawReceiptText(info, centreAlign) : string.Join(Environment.NewLine, result.Value);
        }
    }

    public class MobileOpt: OptCore
    {
        public MobileOpt(IHtecLogManager logManager, string optid, string hydraId, IConfigurationManager configurationManager) : base(logManager, optid, BaseHydraMobileWorker.HydraMobileOptId, hydraId, configurationManager)
        {
        }

        protected override bool DoConnected()
        {
            return true;
        }

        protected override bool DoOffline()
        {
            return !DoConnected();
        }

        protected override Result<string> DoGetRawReceiptText(ReceiptInfo info, bool centreAlign)
        {
            return Result.Success(info.ReceiptContent.Replace($"{OPT.Common.ConfigConstants.CustomerCopy}{Environment.NewLine}", string.Empty));
        }

    }
}