using CSharpFunctionalExtensions;
using Forecourt.BackOffice.Orchestrations.EvoBook;
using Forecourt.BackOffice.Orchestrations.Interfaces;
using Forecourt.BackOffice.Workers;
using Forecourt.BackOffice.Workers.Interfaces;
using Forecourt.Bos.Factories;
using Forecourt.Bos.Factories.Interfaces;
using Forecourt.Bos.Hubs;
using Forecourt.Bos.Hubs.Interfaces;
using Forecourt.Bos.Orchestrations.Interfaces;
using Forecourt.Bos.TransactionFiles;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Forecourt.Bos.Workers;
using Forecourt.Bos.Workers.Interfaces;
using Forecourt.Common.Factories;
using Forecourt.Common.Factories.Interfaces;
using Forecourt.Common.Helpers;
using Forecourt.Common.Helpers.Interfaces;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Common.Workers;
using Forecourt.Common.Workers.Interfaces;
using Forecourt.Core.Configuration;
using Forecourt.Core.Configuration.Interfaces;
using Forecourt.Core.Enums;
using Forecourt.Core.Helpers;
using Forecourt.Core.Helpers.Interfaces;
using Forecourt.Core.HydraDb.Models;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.PaymentConfiguration.ESocketDbClasses;
using Forecourt.PaymentConfiguration.Factories;
using Forecourt.PaymentConfiguration.Factories.Interfaces;
using Forecourt.PaymentConfiguration.Workers.Interfaces;
using Forecourt.Pos.Factories;
using Forecourt.Pos.Factories.Interfaces;
using Forecourt.Pos.Helpers;
using Forecourt.Pos.Helpers.Interfaces;
using Forecourt.Pos.Hubs;
using Forecourt.Pos.Hubs.Interfaces;
using Forecourt.Pos.Workers;
using Forecourt.Pos.Workers.Interfaces;
using Forecourt.Pump;
using Forecourt.Pump.Controllers;
using Forecourt.Pump.Controllers.Interfaces;
using Forecourt.Pump.Factories;
using Forecourt.Pump.Factories.Interfaces;
using Forecourt.Pump.Workers;
using Forecourt.Pump.Workers.Interfaces;
using Forecourt.SecondaryAuth.Factories;
using Forecourt.SecondaryAuth.Factories.Interfaces;
using Forecourt.SecondaryAuth.Hubs;
using Forecourt.SecondaryAuth.Hubs.Interfaces;
using Forecourt.SecondaryAuth.Workers;
using Forecourt.SecondaryAuth.Workers.Interfaces;
using HSC;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Common.Abstractions.Net.Sockets;
using Htec.Common.Abstractions.Net.Sockets.Interfaces;
using Htec.Common.Abstractions.System.Diagnostics;
using Htec.Common.Abstractions.System.Diagnostics.Interfaces;
using Htec.Common.Abstractions.Timers;
using Htec.Common.Abstractions.Timers.Interfaces;
using Htec.Common.Extensions;
using Htec.DapperWrapper;
using Htec.DapperWrapper.Interfaces;
using Htec.Foundation.Connections.Common;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Connections.Workers.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Interfaces;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData;
using Htec.Hydra.Core.PaymentConfiguration.Models.ESocket.CustomData.Interfaces;
using Htec.Hydra.Core.Pos.Interfaces;
using Htec.Hydra.Core.Pump.Interfaces;
using Htec.Hydra.Core.SecondaryAuth.Interfaces;
using Htec.Logger.Interfaces;
using Htec.Logger.Interfaces.Tracing;
using Htec.Logger.log4net;
using Htec.Logger.log4net.Helpers;
using Htec.Logger.log4net.Helpers.Interfaces;
using Htec.Logger.log4net.Tracing;
using Microsoft.AspNet.SignalR;
using OPT.Common;
using OPT.Common.Configuration;
using OPT.Common.ConnectionThreads;
using OPT.Common.Constants;
using OPT.Common.Helpers;
using OPT.Common.Helpers.Interfaces;
using OPT.Common.Repositories;
using OPT.Common.Repositories.Interfaces;
using OPT.Common.Workers;
using OPT.Common.Workers.Interfaces;
using OPT.HydraDb.Migrations;
using OPT.TransactionValidator;
using OPT.TransactionValidator.Interfaces;
using OPT.TransactionValidator.Models;
using PSS_Forecourt_Lib;
using PSS_TcpIp_Lib;
using System;
using System.IO.Abstractions;
using System.Reflection;
using System.Web.Http;
using Unity;
using Unity.Lifetime;
using Unity.WebApi;
using coreConfigConstants = Forecourt.Core.Configuration.Constants;
using IFileSystem = System.IO.Abstractions.IFileSystem;
using IJournalWorkerReceipt = OPT.Common.Workers.Interfaces.IJournalWorkerReceipt;
using ITankGaugeIntegratorInJournal = Htec.Hydra.Core.Pump.Interfaces.ITankGaugeIntegratorInJournal;
using ITelemetryWorker = Forecourt.Common.Workers.Interfaces.ITelemetryWorker;
using slibAbstractions = Htec.Common.Abstractions.Net.Http;

namespace OPTService
{
    /// <summary>
    /// Defines the Unity DI Container
    /// </summary>
    public static class UnityConfig
    {
        private const string InitialCatalog = "Initial Catalog=";
        private const string AttachDbFileName = "AttachDbFileName=";
        
        /// <summary>
        /// Name of the MessageBroker version, for all of the IxxxIntegrator implementations
        /// </summary>
        public const string MessageBrokerName = nameof(MessageBroker);

        /// <summary>
        /// The unity container.
        /// </summary>
        private static UnityContainer _container;

        /// <summary>f
        /// web logger dependency name
        /// </summary>
        public const string DependencyWebLogger = LoggingConstants.UnityDependencyWebLogger;

        /// <summary>
        /// The main Unity configuration registration method
        /// </summary>
        public static IUnityContainer RegisterComponents()
        {
            var tyForcePssDllInclusion = typeof(IDomsPos);
            tyForcePssDllInclusion = typeof(IPSSTcpIpInterface);

            _container = new UnityContainer();

            RegisterTypes(_container);
            RegisterInstances(_container);

            RegisterMessageBrokerWorkers(_container);

            return _container;
        }

        /// <summary>
        /// Registers the <see cref="IUnityContainer"/> with the HTTP stack
        /// </summary>
        /// <param name="httpConfiguration"></param>
        public static void RegisterDependencyResolver(HttpConfiguration httpConfiguration)
        {
            httpConfiguration.DependencyResolver = new UnityDependencyResolver(_container);

            GlobalHost.DependencyResolver.Register(typeof(BosHub), () => _container.Resolve<IBosHub>());
            GlobalHost.DependencyResolver.Register(typeof(PosHub), () => _container.Resolve<IPosHub>());
            GlobalHost.DependencyResolver.Register(typeof(SecAuthHub), () => _container.Resolve<ISecAuthHub>());
        }

        private static Result<bool, Exception> RegisterHubs(IUnityContainer container, IHtecLogManager logManager, IConfigurationManager configManager, IHydraDb hydraDb)
        {
            container.RegisterType<IPosHubbable, PosHubbable>(new ContainerControlledLifetimeManager());
            container.RegisterFactory<IPosHubbableIn>(x => x.Resolve<IPosHubbable>());

            container.RegisterType<IBosHubbable, BosHubbable>(new ContainerControlledLifetimeManager());
            container.RegisterFactory<IBosHubbableIn>(x => x.Resolve<IBosHubbable>());

            container.RegisterType<ISecAuthHubbable, SecAuthHubbable>(new ContainerControlledLifetimeManager());
            container.RegisterFactory<ISecAuthHubbableIn>(x => x.Resolve<ISecAuthHubbable>());

            container.RegisterFactory<IBosHub>(x => new BosHub(container.Resolve<IBosHubbableIn>(), hydraDb), new ContainerControlledLifetimeManager());
            container.RegisterFactory<ISecAuthHub>(x => new SecAuthHub(container.Resolve<ISecAuthHubbableIn>()), new ContainerControlledLifetimeManager());
            container.RegisterFactory<IPosHub>(x => new PosHub(container.Resolve<IPosHubbableIn>()), new ContainerControlledLifetimeManager());

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection types.
        /// </summary>
        /// <param name="container">The unity container.</param>
        private static void RegisterTypes(IUnityContainer container)
        {
            container.RegisterFactory<ILogFormatter>(x => new LogFormatter());
            container.RegisterFactory<IDbExecutorFactory>(x => new SqlExecutorFactory(container.Resolve<IConfigurationManager>().ConnectionString(ConfigConstants.HydraDbName)));

            container.RegisterType<IConfigurationRepository, ConfigurationRepository>();
            container.RegisterType<IFileSystem, FileSystem>();
            container.RegisterType<IFileVersionInfoFactory, FileVersionInfoFactory>();
            container.RegisterType<IFileVersionInfoHelper, FileVersionInfoHelper>();
            container.RegisterType<ISocketWrapperFactory, SocketWrapperFactory>();
            container.RegisterType<ITimerFactory, TimerFactory>();
            container.RegisterType<IFileSavingHelper, FileSavingHelper>();
            container.RegisterType<IServiceFilesHelper, ServiceFilesHelper>();
            container.RegisterType<IHttpClientFactory, slibAbstractions.HttpClientFactory>();

            container.RegisterType<IIntegratorFactories, IntegratorFactories>(new ContainerControlledLifetimeManager());
            container.RegisterType<IMessageBroker, MessageBroker>(new ContainerControlledLifetimeManager());
            container.RegisterType<IPrinterHelper<IMessageTracking>, PrinterHelper>(new ContainerControlledLifetimeManager());
            container.RegisterType<IShiftDayEndConfig, ShiftDayEndConfig>(new ContainerControlledLifetimeManager());
            container.RegisterType<IInfoMessagesConfig, InfoMessagesConfig>(new ContainerControlledLifetimeManager());
            container.RegisterType<IVatCalculator, VatCalculator>(new ContainerControlledLifetimeManager());
        }

        /// <summary>
        /// Registers the injection instances.
        /// NOTE: These create single instances of classes that are injected.
        /// </summary>
        /// <param name="container">The unity container.</param>
        private static Result<bool, Exception> RegisterInstances(IUnityContainer container)
        {
            var fileSystem = container.Resolve<IFileSystem>();
            var configPath = fileSystem.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"{ConfigConstants.ServiceName.ToLower()}.log4net.xml");
            container.RegisterInstance("ConfigPath", configPath);
            var logManager = HtecLogManager.GetLogManager(fileSystem, configPath, true, container.Resolve<ILogFormatter>());
            container.RegisterInstance(logManager);

            var logger = logManager.GetLogger(ConfigConstants.ServiceName, false);
            var loggerWeb = logManager.GetLogger($"{ConfigConstants.LoggerPrefixOptService}Web", false);
            var loggerConfig = logManager.GetLogger(Loggable.LoggerNameIConfigurationManager, false);

            container.RegisterInstance(logger); // Catch-all
            container.RegisterInstance(ConfigConstants.ServiceName, logger);
            container.RegisterInstance(DependencyWebLogger, loggerWeb);
            container.RegisterInstance(Loggable.LoggerNameIConfigurationManager, loggerConfig);

            var helper = container.Resolve<IFileVersionInfoHelper>();
            var assembly = Assembly.GetExecutingAssembly();
            var versionInfo = helper.ExtractFileVersionInfo(assembly);
            container.RegisterInstance("ServiceVersionInfo", versionInfo.IsSuccess ? versionInfo.Value : new FileVersionInfo(assembly.FullName, string.Empty, string.Empty, DateTime.MinValue));

            container.RegisterInstance("Telemetry", logManager.GetLogger("Telemetry", configPath, false));
            container.RegisterInstance("TelemetryWorker", logManager.GetLogger($"{ConfigConstants.LoggerPrefixWorker}Telemetry", false));

            var configManager = new DbCachedConfigurationManager(loggerConfig);
            container.RegisterInstance<IConfigurationManager>(configManager);

            var result = PerformDbMigrations(configManager, $@"C:\Logs\{DateTime.UtcNow:yyyy-MM-dd}\HydraOPTService\DbMigrations.log", fileSystem, container.Resolve<IDbExecutorFactory>());
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { $"{nameof(PerformDbMigrations)}.Failure" }, result.Error);
                return result;
            }

            var workerTelemetry = new TelemetryWorker(Core.GetDeviceName(), container.Resolve<FileVersionInfo>("ServiceVersionInfo").Version, container.Resolve<IHtecLogger>("Telemetry"),
                container.Resolve<IHtecLogger>("TelemetryWorker"), configManager);
            container.RegisterInstance<ITelemetryWorker>(workerTelemetry);

            var cacheHelper = new CacheHelper(loggerConfig, configManager);
            container.RegisterInstance<ICacheHelper>(cacheHelper);

            var loggerCore = logManager.GetLogger($"{ConfigConstants.LoggerPrefixOptService}Core");
            container.RegisterInstance("Core", loggerCore);
            container.RegisterInstance<ILoggingHelper>(new LoggingHelper(fileSystem, loggerCore, configManager, _container.Resolve<IFileSavingHelper>()));

            var configRepository = container.Resolve<IConfigurationRepository>();
            configManager.Register(cacheHelper, configRepository);

            var resultDb = RegisterDatabases(container, (logManager, configManager), workerTelemetry);
            if (!resultDb.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterDatabases) }, result.Error);
                return result;
            }

            var hydraDb = container.Resolve<IHydraDb>();
            var timerFactory = container.Resolve<ITimerFactory>();
            var socketWrapperFactory = container.Resolve<ISocketWrapperFactory>();

            container.RegisterInstance<IReceiptHelper>(new ReceiptHelper(logManager, hydraDb, null, configManager), new ContainerControlledLifetimeManager());

            var allOpts = new OptCollection(logManager, Opt.LoggerName, hydraDb, configManager, container.Resolve<IPrinterHelper<IMessageTracking>>(), container.Resolve<IReceiptHelper>());
            var allPumps = new PumpCollection(logManager, Pump.LoggerName, hydraDb, allOpts, configManager);
            container.RegisterInstance<IOptCollection>(allOpts);
            container.RegisterInstance<IPumpCollection>(allPumps);
            container.RegisterInstance<IGradeHelper>(new GradeHelper(logger, hydraDb));

            result = Register_WorkersAndIntegrators(container, logger, logManager, configManager, hydraDb, workerTelemetry, timerFactory, fileSystem, socketWrapperFactory, 
                cacheHelper, resultDb.Value, container.Resolve<IFileVersionInfoHelper>(), container.Resolve<IGradeHelper>(), container.Resolve<IReceiptHelper>());
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(Register_WorkersAndIntegrators) }, result.Error);
                return result;
            }

            result = RegisterHubs(container, logManager, configManager, hydraDb);

            var core = new Core(
                logManager, 
                configManager,
                fileSystem, 
                container.Resolve<IFileVersionInfoFactory>(),
                container.Resolve<IPaymentConfigIntegrator>(), 
                hydraDb, 
                container.Resolve<IControllerWorker>(),
                container.Resolve<IConfigUpdateWorker>(),
                container.Resolve<IUpdateWorker>(), 
                container.Resolve<IJournalWorker>(), 
                container.Resolve<ILocalAccountWorker>(), 
                container.Resolve<ICarWashWorker>(),
                container.Resolve<ISecAuthIntegratorInTransient<IMessageTracking>>(),
                container.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(),
                container.Resolve<IToOptWorker>(),
                container.Resolve<IOptHeartbeatWorker>(), 
                container.Resolve<IFromOptWorker>(),
                container.Resolve<IMediaChannelWorker>(), 
                container.Resolve<IPumpIntegratorConfiguration>(), 
                container.Resolve<ITankGaugeWorker>(),
                container.Resolve<IPosIntegratorIn<IMessageTracking, IPump>>($"{IntegrationType.MobilePos}"), 
                container.Resolve<IPumpCollection>(),
                container.Resolve<IOptCollection>(),
                container.Resolve<IHydraTransactionFile>(),
                container.Resolve<IRetalixTransactionFile>(),
                (type) => container.Resolve(type) as IWorkerable,
                container.Resolve<IIntegratorFactories>(),
                container.Resolve<IMessageBroker>(),
                container.Resolve<IServiceFilesHelper>(),
                container.Resolve<ICacheHelper>());
            container.RegisterInstance<ICore>(core);

            container.RegisterInstance<IWeb>(new Web(core, loggerWeb, configManager, container.Resolve<IIntegratorFactories>(),
                container.Resolve<IFileVersionInfoHelper>(), container.Resolve<IFileSystem>(), container.Resolve<IServiceFilesHelper>(), 
                hydraDb, container.Resolve<IControllerWorker>(), container.Resolve<IToOptWorker>()));

            return Result.Success<bool, Exception>(true);
        }

        private static Result<bool, Exception> Register_WorkersAndIntegrators(IUnityContainer container, IHtecLogger logger, IHtecLogManager logManager, IConfigurationManager configManager, IHydraDb hydraDb, ITelemetryWorker workerTelemetry, 
            ITimerFactory timerFactory, IFileSystem fileSystem, ISocketWrapperFactory socketWrapperFactory, ICacheHelper cacheHelper, string dbName, IFileVersionInfoHelper fileVersionInfoHelper, IGradeHelper gradeHelper, IReceiptHelper receiptHelper)
        {
            var result = RegisterWorkers(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, fileSystem, socketWrapperFactory), dbName);
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterWorkers) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_Bos(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, fileSystem, gradeHelper, receiptHelper));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_Bos) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_Pos(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, fileSystem, socketWrapperFactory, receiptHelper));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_Pos) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_TankGauge(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, cacheHelper, socketWrapperFactory, fileVersionInfoHelper));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_TankGauge) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_Pump(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, cacheHelper, socketWrapperFactory, fileVersionInfoHelper));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_Pump) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_Payment(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, fileSystem, socketWrapperFactory, gradeHelper));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_Payment) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_SecAuth(container, (logManager, configManager), hydraDb, workerTelemetry, (timerFactory, fileSystem, socketWrapperFactory));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_SecAuth) }, result.Error);
                return result;
            }

            result = RegisterIntegrators_PaymentConfig(container, (logManager, configManager), (hydraDb, container.Resolve<IEsocketDb>()), workerTelemetry, (timerFactory, fileSystem));
            if (!result.IsSuccess)
            {
                logger.Error("Service.Startup", () => new[] { nameof(RegisterIntegrators_SecAuth) }, result.Error);
                return result;
            }

            return Result.Success<bool, Exception>(true);
        }

        private static Result<bool, Exception> PerformDbMigrations(IConfigurationManager configManager, string logFileName, IFileSystem fileSystem, IDbExecutorFactory dbExecutorFactory)
        {
            var key = $"{ConfigConstants.HydraDbName}{OPT.HydraDb.Common.Constants.MigrationTypeBootStrap}";
            var connectionString = configManager.ConnectionString(key);
            if (string.IsNullOrEmpty(connectionString))
            {
                return Result.Failure<bool, Exception>(new ArgumentNullException($"{nameof(IConfigurationManager)}.ConnectionString[\"{key}\"]"));
            }

            var result = MigrationHelper.RunMigrations(connectionString, OPT.HydraDb.Common.Constants.MigrationTypeBootStrap, logFileName);
            if (!result.IsSuccess)
            {
                return result;
            }

            connectionString = configManager.ConnectionString(ConfigConstants.HydraDbName);

            // MUST be System.Configuration.ConfigurationManager as it needs to read from the app.config
            var direction = System.Configuration.ConfigurationManager.AppSettings.GetAppSettingOrDefault(OPT.HydraDb.Common.Constants.ConfigKeyHydraDbMigrationType, OPT.HydraDb.Common.Constants.DefaultValueHydraDbMigrationTypeUpgrade, null);
            var down = direction.Equals(OPT.HydraDb.Common.Constants.DefaultValueHydraDbMigrationTypeRollback, StringComparison.InvariantCultureIgnoreCase);

            var types = down ?
                new[] { OPT.HydraDb.Common.Constants.MigrationTypeData, OPT.HydraDb.Common.Constants.MigrationTypeProgrammatic, OPT.HydraDb.Common.Constants.MigrationTypeSchema } :
                new[] { OPT.HydraDb.Common.Constants.MigrationTypeSchema, OPT.HydraDb.Common.Constants.MigrationTypeProgrammatic, OPT.HydraDb.Common.Constants.MigrationTypeData };

            foreach (var type in types)
            {
                result = MigrationHelper.RunMigrations(connectionString, type, logFileName, down, fileSystem, dbExecutorFactory);
                if (!result.IsSuccess)
                {
                    return result;
                }
            }

            return result;
        }

        /// <summary>
        /// Registers the injection instances, specific to HydraDb and eSocketDb
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <returns>Result status</returns>
        private static Result<string, Exception> RegisterDatabases(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, ITelemetryWorker telemetryWorker)
        {
            var logger = managers.Item1.GetLogger(ConfigConstants.ServiceName, false);
            var loggerCore = container.Resolve<IHtecLogger>("Core");
            var fileSystem = container.Resolve<IFileSystem>();

            var customDataOverride = new CustomDataFields(loggerCore, managers.Item2);
            container.RegisterInstance<ICustomDataFields>(customDataOverride);

            var eSocketDb = new EsocketDb(managers.Item1, telemetryWorker, fileSystem, container.Resolve<ICustomDataFields>());
            container.RegisterInstance<IEsocketDb>(eSocketDb);

            var databaseConnectionString = managers.Item2.ConnectionStrings[ConfigConstants.HydraDbName].ConnectionString;
            var databaseFileName = ConfigConstants.HydraDbName;
            if (string.IsNullOrWhiteSpace(databaseConnectionString))
            {
                logger.Error("Empty database connection string");
            }
            else if (databaseConnectionString.Contains(InitialCatalog))
            {
                var catalog = databaseConnectionString.Substring(databaseConnectionString.IndexOf(InitialCatalog) + InitialCatalog.Length);
                if (catalog.Contains(";"))
                {
                    databaseFileName = catalog.Substring(0, catalog.IndexOf(";"));
                }
                else
                {
                    databaseFileName = catalog;
                }
            }
            else if (databaseConnectionString.Contains(AttachDbFileName))
            {
                var attach = databaseConnectionString.Substring(databaseConnectionString.IndexOf(AttachDbFileName) + AttachDbFileName.Length);
                if (attach.Contains(";"))
                {
                    attach = attach.Substring(0, attach.IndexOf(";"));
                }

                if (attach.Contains($"{fileSystem.Path.DirectorySeparatorChar}"))
                {
                    attach = attach.Substring(attach.LastIndexOf($"{fileSystem.Path.DirectorySeparatorChar}") + 1);
                }

                if (attach.Contains(".mdf"))
                {
                    attach = attach.Substring(0, attach.IndexOf(".mdf"));
                }

                databaseFileName = attach;
            }
            else
            {
                logger.Error("Invalid database connection string");
            }

            logger.Info($"Database Connection String is {databaseConnectionString}");
            logger.Info($"Database File Name is {databaseFileName}");

            IHydraDb hydraDb;
            try
            {
                var dbExecutorFactory = new SqlExecutorFactory(databaseConnectionString);

                hydraDb = new HydraDb(dbExecutorFactory, managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixDatabase}HydraDB", false), telemetryWorker, managers.Item2, 
                    container.Resolve<IConfigurationRepository>(), container.Resolve<ICacheHelper>());
                container.RegisterInstance(hydraDb);
                container.RegisterInstance<Forecourt.Bos.HydraDb.Interfaces.IHydraDb>(hydraDb);
            }
            catch (Exception ex)
            {
                return Result.Failure<string, Exception>(ex);
            }

            var result = hydraDb.GetVersionInfo();
            if (!result.IsSuccess)
            {
                return Result.Failure<string, Exception>(new Exception(result.Error));
            }

            var fileLocations = hydraDb.GetFileLocations();
            if (fileLocations == null)
            {
                return Result.Failure<string, Exception>(new Exception("Error fetching file locations, probably invalid database connection string"));
            }
            
            return Result.Success<string, Exception>(databaseFileName);
        }

        /// <summary>
        /// Registers the injection instances, specific to Workers/ConnectionThreads
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>, <see cref="ISocketWrapperFactory"/>) instance</param>
        /// <param name="databaseFileName">Db filename</param>
        /// <returns>Result status</returns>        
        private static Result<bool, Exception> RegisterWorkers(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, (ITimerFactory, IFileSystem, ISocketWrapperFactory) helpers, string databaseFileName)
        {
            var loggerCore = container.Resolve<IHtecLogger>("Core");

            AdvancedConfig siteInfo;
            BosConfig bosConfig;

            try
            {
                siteInfo = hydraDb.AdvancedConfig;
                loggerCore.SetLoggerProperty("SiteName", siteInfo.SiteName);
            }
            catch (Exception ex)
            {
                return Result.Failure<bool, Exception>(ex);
            }

            EndPointsConfig endPointConfig;
            try
            {
                endPointConfig = hydraDb.EndPointsConfig;
            }
            catch (Exception ex)
            {
                return Result.Failure<bool, Exception>(ex);
            }

            try
            {
                bosConfig = hydraDb.BosConfig;
            }
            catch (Exception ex)
            {
                return Result.Failure<bool, Exception>(ex);
            }

            var allPumps = container.Resolve<IPumpCollection>();

            container.RegisterFactory<CarWashConnectionThread>(x => new CarWashConnectionThread(managers.Item1, helpers.Item3, managers.Item2));
            container.RegisterFactory<ICarWashWorker>(x => new CarWashWorker(
                telemetryWorker, 
                hydraDb,
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}CarWash", false),
                x.Resolve<CarWashConnectionThread>(), 
                managers.Item2,
                helpers.Item1), new ContainerControlledLifetimeManager());
            
            container.RegisterFactory<MediaChannelConnectionThread>(x => new MediaChannelConnectionThread(managers.Item1, helpers.Item3, helpers.Item2.FileInfo, managers.Item2, telemetryWorker));
            container.RegisterFactory<IMediaChannelWorker>(x => new MediaChannelWorker(
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}MediaChannel", false), 
                hydraDb, telemetryWorker,
                x.Resolve<MediaChannelConnectionThread>(),
                managers.Item2, 
                helpers.Item2,
                container.Resolve<IOptCollection>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ILocalAccountWorker>(x => new LocalAccountWorker(
                hydraDb, 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}LocalAccount", false)), 
                new ContainerControlledLifetimeManager());

            container.RegisterFactory<IUpdateWorker>(x => new UpdateWorker(
                hydraDb, 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}Update", false),
                x.Resolve<IJournalWorker>(),
                x.Resolve<IHydraTransactionFile>(), 
                x.Resolve<ILocalAccountWorker>(),
                managers.Item2,
                helpers.Item1), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IConfigUpdateWorker>(x => new ConfigUpdateWorker(
                hydraDb, 
                x.Resolve<IPaymentConfigIntegrator>(), 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}ConfigUpdate", false),
                managers.Item2,
                helpers.Item1, 
                helpers.Item2), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IControllerWorker>(x => new ControllerWorker(
                (x.Resolve<IPosIntegratorOut<IMessageTracking>>(IntegrationType.Pos.ToString()), 
                x.Resolve<IPosIntegratorOut<IMessageTracking>>(IntegrationType.MobilePos.ToString())),
                x.Resolve<IMediaChannelWorker>(),
                container.Resolve<ISecAuthIntegratorInTransient<IMessageTracking>>(),
                container.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(),
                x.Resolve<ICarWashWorker>(), 
                x.Resolve<ITankGaugeIntegratorInJournal>(),
                x.Resolve<IPosIntegratorInMode<IMessageTracking>>(IntegrationType.Pos.ToString()),
                x.Resolve<IBosIntegratorInJournal<IMessageTracking>>(IntegrationType.MobilePos.ToString()),
                x.Resolve<IPumpIntegratorInJournal>(),
                x.Resolve<IPumpIntegratorInSetup>(),
                x.Resolve<IJournalWorker>(),
                x.Resolve<IUpdateWorker>(),
                x.Resolve<IConfigUpdateWorker>(),
                x.Resolve<ILocalAccountWorker>(),
                x.Resolve<IOptCollection>(), 
                x.Resolve<IPumpCollection>(), 
                hydraDb,
                x.Resolve<IPaymentConfigIntegrator>(),
                x.Resolve<IHydraTransactionFile>(),
                x.Resolve<IRetalixTransactionFile>(), 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}Controller", false), 
                databaseFileName,
                x.Resolve<ILoggingHelper>(), 
                managers.Item2, 
                telemetryWorker,
                helpers.Item1,
                x.Resolve<IInfoMessagesConfig>(),
                container.Resolve<IIntegratorFactories>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<Htec.Foundation.Connections.Workers.Interfaces.INotificationWorker<string>>(x => x.Resolve<IControllerWorker>());
            container.RegisterFactory<INotificationWorker<EventType>>(x => x.Resolve<IControllerWorker>());

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the Tank Gauge Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="ICacheHelper"/>, <see cref="ISocketWrapperFactory"/>, <see cref="IFileVersionInfoHelper"/>) instance</param>
        /// <returns>Result status</returns>
        private static Result<bool, Exception> RegisterIntegrators_TankGauge(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, (ITimerFactory, ICacheHelper, ISocketWrapperFactory, IFileVersionInfoHelper) helpers)
        {
            container.RegisterFactory<TankGaugeConnectionThread>(x => new TankGaugeConnectionThread(
                managers.Item1,
                helpers.Item3,
                managers.Item2));

            container.RegisterFactory<ITankGaugeWorker>(x => new TankGaugeWorker(
                x.Resolve<ITankGaugeControllerFactory>(),
                telemetryWorker,
                x.Resolve<ITankGaugeIntegratorOutJournal>(MessageBrokerName),
                hydraDb,
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}TankGauge", false),
                managers.Item2,
                helpers.Item1), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IHydraTankGaugeController>(x => new HydraTankGaugeController(
                telemetryWorker,
                hydraDb,
                x.Resolve<TankGaugeConnectionThread>(),
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}TankGauge", false),
                managers.Item2,
                helpers.Item1,
                x.Resolve<IShiftDayEndConfig>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ITankGaugeControllerFactory>(x => new TankGaugeControllerFactory(
              managers.Item1,
              managers.Item2,
              (name) => x.Resolve<ITankGaugeController>(name)));

            container.RegisterFactory<ITankGaugeController>(x => x.Resolve<ITankGaugeControllerFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.PumpType));
            container.RegisterFactory<ITankGaugeController>(coreConfigConstants.Integrator.PumpTypeHsc, x => x.Resolve<IHydraTankGaugeController>());
            container.RegisterFactory<ITankGaugeController>(coreConfigConstants.Integrator.PumpTypeDoms, x => x.Resolve<IDomsController>());

            container.RegisterFactory<ITankGaugeIntegratorInFactory>(x => new TankGaugeIntegratorInFactory(
               managers.Item1,
               managers.Item2,
               (name) => x.Resolve<ITankGaugeIntegratorInJournal>(name)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ITankGaugeIntegratorInJournal>(x => x.Resolve<ITankGaugeWorker>());
            container.RegisterFactory<ITankGaugeIntegratorInJournal>(coreConfigConstants.Integrator.PumpTypeHsc, x => x.Resolve<ITankGaugeWorker>());
            container.RegisterFactory<ITankGaugeIntegratorInJournal>(coreConfigConstants.Integrator.PumpTypeDoms, x => x.Resolve<ITankGaugeWorker>());

            container.RegisterFactory<ITankGaugeIntegratorInJournal>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<ITankGaugeIntegratorOutJournal>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the Pump Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="ICacheHelper"/>, <see cref="ISocketWrapperFactory"/>, <see cref="IFileVersionInfoHelper"/>) instance</param>
        /// <returns>Result status</returns>
        private static Result<bool, Exception> RegisterIntegrators_Pump(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, (ITimerFactory, ICacheHelper, ISocketWrapperFactory, IFileVersionInfoHelper) helpers)
        {
            container.RegisterFactory<ISiteController>(x => new SiteController(managers.Item1.GetLogger(LoggingConstants.LoggerHsc, false)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPumpWorker>(x => new PumpWorker(
                x.Resolve<IPumpControllerFactory>(), 
                x.Resolve<IPumpIntegratorOutJournal<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IPumpIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                hydraDb, 
                managers.Item1, 
                managers.Item2,
                helpers.Item1,
                x.Resolve<IPumpCollection>(),
                helpers.Item4, 
                helpers.Item2,
                x.Resolve<IPosIntegratorInMode<IMessageTracking>>(MessageBrokerName)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPumpIntegratorInFactory>(x => new PumpIntegratorInFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IPumpIntegratorIn<IMessageTracking>>(name)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPumpIntegratorIn<IMessageTracking>>(coreConfigConstants.Integrator.PumpTypeHsc, x => x.Resolve<IPumpWorker>());
            container.RegisterFactory<IPumpIntegratorIn<IMessageTracking>>(coreConfigConstants.Integrator.PumpTypeDoms, x => x.Resolve<IPumpWorker>());

            container.RegisterFactory<IPumpIntegratorIn<IMessageTracking>>(x => x.Resolve<IPumpWorker>());
            container.RegisterFactory<IPumpIntegratorInTransient<IMessageTracking>>(x => x.Resolve<IPumpWorker>());
            container.RegisterFactory<IPumpIntegratorInJournal>(x => x.Resolve<IPumpWorker>());
            container.RegisterFactory<IPumpIntegratorInSetup>(x => x.Resolve<IPumpWorker>());
            container.RegisterFactory<IPumpIntegratorConfiguration>(x => x.Resolve<IPumpWorker>());

            container.RegisterFactory<IPumpIntegratorInTransient<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPumpIntegratorInJournal>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPumpIntegratorInSetup>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPumpIntegratorOutJournal<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPumpIntegratorOutTransient<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            container.RegisterFactory<IHydraPumpController>(x => new HydraPumpController(
               managers.Item1,
               managers.Item2,
               hydraDb,
               x.Resolve<ISiteController>(),
               x.Resolve<IFileSystem>(),
               helpers.Item1,
               helpers.Item4), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IDomsController>(x => new DomsController(
               managers.Item1,
               managers.Item2,
               hydraDb,
               helpers.Item4,
               helpers.Item2,
               container.Resolve<IPumpCollection>(),
               hydraDb.GetPosClaim().PosNumber,
               container.Resolve<ITimerFactory>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPumpControllerFactory>(x => new PumpControllerFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IPumpController>(name)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPumpController>(x => x.Resolve<IPumpControllerFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.PumpType));
            container.RegisterFactory<IPumpController>(coreConfigConstants.Integrator.PumpTypeHsc, x => x.Resolve<IHydraPumpController>());
            container.RegisterFactory<IPumpController>(coreConfigConstants.Integrator.PumpTypeDoms, x => x.Resolve<IDomsController>());

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the Pos Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>, <see cref="ISocketWrapperFactory"/>) instance</param>
        /// <returns>Result status</returns>
        private static Result<bool, Exception> RegisterIntegrators_Pos(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, 
            (ITimerFactory, IFileSystem, ISocketWrapperFactory, IReceiptHelper) helpers)
        {
            container.RegisterFactory<INonePosXxxWorker>(x => new NonePosXxxWorker(
                managers.Item1,
                hydraDb,
                x.Resolve<IPumpCollection>(),
                managers.Item2), new ContainerControlledLifetimeManager());


            container.RegisterFactory<IMobilePosIntegratorInFactory>(x => new MobilePosIntegratorInFactory(
               managers.Item1,
               managers.Item2,
               (name) => x.Resolve<IPosIntegratorIn<IMessageTracking, IPump, Result>>($"{IntegrationType.MobilePos}:{name}")));

            #region Mobile

            container.RegisterFactory<HydraMobileConnectionThread>(x => new HydraMobileConnectionThread(managers.Item1, helpers.Item3, managers.Item2));

            container.RegisterFactory<IHydraMobileFullWorker>(x => new HydraMobileFullWorker(
            managers.Item1,
                hydraDb,
                managers.Item2,
                telemetryWorker,
                x.Resolve<HydraMobileConnectionThread>(),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IJournalWorkerReceipt>(),
                x.Resolve<IBosIntegratorOut<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IOptCollection>(),
                x.Resolve<IPumpCollection>(),
                x.Resolve<IGradeHelper>(),
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(MessageBrokerName), 
                x.Resolve<ITimerFactory>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IHydraMobileStatusWorker>(x => new HydraMobileStatusWorker(
                managers.Item1,
                hydraDb,
                managers.Item2,
                telemetryWorker,
                x.Resolve<HydraMobileConnectionThread>(),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IJournalWorkerReceipt>(),
                x.Resolve<IBosIntegratorOut<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IOptCollection>(),
                x.Resolve<IPumpCollection>(),
                x.Resolve<IGradeHelper>(),
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<ITimerFactory>(),
                x.Resolve<IPumpIntegratorOutTransient<IMessageTracking>>(MessageBrokerName)), new ContainerControlledLifetimeManager());
            

            container.RegisterFactory<IPosIntegratorIn<IMessageTracking, IPump, Result>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobile}", x => x.Resolve<IHydraMobileFullWorker>());
            container.RegisterFactory<IPosIntegratorIn<IMessageTracking, IPump, Result>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobileStatus}", x => x.Resolve<IHydraMobileStatusWorker>());

            container.RegisterFactory<IPosIntegratorIn<IMessageTracking, IPump>>($"{IntegrationType.MobilePos}", x => x.Resolve<IMobilePosIntegratorInFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.MobilePosType));
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.MobilePos}", x => x.Resolve<IMobilePosIntegratorInFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.MobilePosType));

            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobile}", x => x.Resolve<IHydraMobileFullWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobileStatus}", x => x.Resolve<IHydraMobileStatusWorker>());

            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.MobilePos}", x => x.Resolve<IMobilePosIntegratorInFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.MobilePosType));
            container.RegisterFactory<IPosIntegratorOutTransient<IMessageTracking>>($"{IntegrationType.MobilePos}", x => x.Resolve<IMobilePosIntegratorInFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.MobilePosType));

            #endregion

            container.RegisterFactory<IPosIntegratorInFactory>(x => new PosIntegratorInFactory(
              managers.Item1,
              managers.Item2,
              (name) => x.Resolve<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{name}")));

            container.RegisterFactory<IPosIntegratorOutFactory>(x => new PosIntegratorOutFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{name}")));

            #region HydraPos, Retalix

            container.RegisterFactory<HydraPosConnectionThread>(x => new HydraPosConnectionThread(managers.Item1, helpers.Item3, managers.Item2, MessageLengthFlowType.Both));
            container.RegisterFactory<IHydraPosWorker>(x => new HydraPosWorker(
                x.Resolve<IBosIntegratorInJournal<IMessageTracking>>(MessageBrokerName), 
                telemetryWorker, 
                x.Resolve<HydraPosConnectionThread>(),
                hydraDb, 
                x.Resolve<IPumpCollection>(), 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}HydraPOS", false), 
                managers.Item2,
                x.Resolve<IPrinterHelper<IMessageTracking>>(),
                x.Resolve<IPosIntegratorOut<IMessageTracking>>(MessageBrokerName), 
                x.Resolve<IPosIntegratorInMode<IMessageTracking>>(MessageBrokerName)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<RetalixConnectionThread>(x => new RetalixConnectionThread(managers.Item1, helpers.Item3, managers.Item2));
            container.RegisterFactory<IRetalixPosWorker>(x => new RetalixPosWorker(
                hydraDb, 
                managers.Item1,
                telemetryWorker,
                x.Resolve<RetalixConnectionThread>(), 
                managers.Item2,
                x.Resolve<IHydraPosWorker>(),
                x.Resolve<IBosIntegratorOutOfflineTransient>(MessageBrokerName)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ThirdPartyPosConnectionThread>(x => new ThirdPartyPosConnectionThread(managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixConnection}ThirdPartyPOS", false), helpers.Item3, managers.Item2));
            container.RegisterFactory<IThirdPartyPosWorker>(x => new ThirdPartyPosWorker(
                hydraDb, 
                x.Resolve<IPumpIntegratorInTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IPumpCollection>(), 
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}ThirdPartyPOS", false),
                telemetryWorker, 
                x.Resolve<ThirdPartyPosConnectionThread>()), new ContainerControlledLifetimeManager());

            #endregion

            #region SignalR

            container.RegisterFactory<IWebApiPosInTransientWorker>(x => new WebApiPosInTransientWorker(    
                managers.Item1,
                managers.Item2,
                container.Resolve<IPumpCollection>(),
                hydraDb,
                helpers.Item4,
                container.Resolve<IOptCollection>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ISignalRPosOutWorker>(x => new SignalRPosOutWorker(
                managers.Item1,
                managers.Item2,
                hydraDb,
                x.Resolve<IPumpCollection>(),
                x.Resolve<IPrinterHelper<IMessageTracking>>(),
                helpers.Item4,
                x.Resolve<IPosHubbable>()), new ContainerControlledLifetimeManager());

            #endregion

            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{PosType.None}", x => x.Resolve<INonePosXxxWorker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{PosType.HydraPos}", x => x.Resolve<IHydraPosWorker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{PosType.Retalix}", x => x.Resolve<IRetalixPosWorker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{PosType.GenericSignalRApi}", x => x.Resolve<IWebApiPosInTransientWorker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}:{PosType.MadicApiSignalR}", x => x.Resolve<IWebApiPosInTransientWorker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>($"{IntegrationType.Pos}", x => x.Resolve<IPosIntegratorInFactory>().GetInstance(null, $"{x.Resolve<IHydraDb>().AdvancedConfig.PosType}"));

            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.None}", x => x.Resolve<INonePosXxxWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.HydraPos}", x => x.Resolve<IHydraPosWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.Retalix}", x => x.Resolve<IRetalixPosWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.GenericSignalRApi}", x => x.Resolve<ISignalRPosOutWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.MadicApiSignalR}", x => x.Resolve<ISignalRPosOutWorker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>($"{IntegrationType.Pos}", x => x.Resolve<IPosIntegratorOutFactory>().GetInstance(null, $"{x.Resolve<IHydraDb>().AdvancedConfig.PosType}"));
            container.RegisterFactory<IPosIntegratorOutTransient<IMessageTracking>>($"{IntegrationType.Pos}", x => x.Resolve<IPosIntegratorOutFactory>().GetInstance(null, $"{x.Resolve<IHydraDb>().AdvancedConfig.PosType}"));

            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPosIntegratorOut<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPosIntegratorOutMode<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IPosIntegratorInTransient<IMessageTracking, IPump, Result<StatusCodeResult>>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            container.RegisterFactory<IPosInModeFactory>(x => new PosInModeFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{name}")));

            #region InModeWorkers

            container.RegisterFactory<IHydraPosInModeWorker>(x=> new HydraPosInModeWorker(
                managers.Item1, 
                hydraDb,
                container.Resolve<IPumpCollection>(), 
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                managers.Item2
                ), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IRetalixPosInModeWorker>(x => new RetalixPosInModeWorker(
              managers.Item1,
              hydraDb,
              container.Resolve<IPumpCollection>(),
              x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
              managers.Item2
              ), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IWebApiPosInModeWorker>(x => new WebApiPosInModeWorker(
              managers.Item1,
              hydraDb,
              container.Resolve<IPumpCollection>(),
              x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
              managers.Item2
              ), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IMadicSignalRInModeWorker>(x => new MadicSignalRPosInModeWorker(
                managers.Item1,
                hydraDb,
                container.Resolve<IPumpCollection>(),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                managers.Item2
                ), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.None}", x => x.Resolve<INonePosXxxWorker>());
            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.HydraPos}", x => x.Resolve<IHydraPosInModeWorker>());
            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.Retalix}", x => x.Resolve<IRetalixPosInModeWorker>());
            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.GenericSignalRApi}", x => x.Resolve<IWebApiPosInModeWorker>());
            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}:{PosType.MadicApiSignalR}", x => x.Resolve<IMadicSignalRInModeWorker>());
            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>($"{IntegrationType.Pos}", x => x.Resolve<IPosInModeFactory>().GetInstance(null, $"{x.Resolve<IHydraDb>().AdvancedConfig.PosType}"));

            container.RegisterFactory<IPosIntegratorInMode<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            #endregion

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the BosType Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>) instance</param>
        /// <returns>Result status</returns>        
        private static Result<bool, Exception> RegisterIntegrators_Bos(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker,
            (ITimerFactory, IFileSystem, IGradeHelper, IReceiptHelper) helpers)
        {
            container.RegisterFactory<IHydraTransactionFile>(x => new HydraTransactionFile(
             hydraDb,
             managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixOptService}HydraTransactionFile", false),
             helpers.Item2,
             managers.Item2), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IRetalixTransactionFile>(x => new RetalixTransactionFile(
                hydraDb,
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixOptService}RetalixTransactionFile", false),
                helpers.Item2,
                x.Resolve<IRetalixPosWorker>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IHydraBosOutWorker>(x => new HydraBosOutWorker(
                managers.Item1,
                null,
                managers.Item2,
                x.Resolve<IHydraTransactionFile>(),
                hydraDb), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IRetalixBosOutWorker>(x => new RetalixBosOutWorker(
                managers.Item1,
                null,
                managers.Item2,
                x.Resolve<IHydraTransactionFile>(),
                x.Resolve<IRetalixTransactionFile>(),
                hydraDb), new ContainerControlledLifetimeManager());

            #region SignalR

            container.RegisterFactory<ISignalRBosOutWorker>(x => new SignalRBosOutWorker(
                managers.Item1,
                managers.Item2,                
                hydraDb,
                x.Resolve<IBosHubbable>()), new ContainerControlledLifetimeManager());

            #endregion

            #region WebAPI

            container.RegisterFactory<IBookTransactionOrchestration>(x => new BookTransactionOrchestration(
                managers.Item1,
                nameof(BookTransactionOrchestration),
                managers.Item2,
                hydraDb,
               x.Resolve<IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>>(),
               x.Resolve<IHttpClientFactory>(),
               x.Resolve<IControllerWorker>(),
               x.Resolve<IVatCalculator>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IMadicRestApiBosOutWorker>(x => new MadicRestApiBosOutWorker(
               managers.Item1,
               hydraDb,
               x.Resolve<IBookTransactionOrchestration>(),
               x.Resolve<ICacheHelper>(),
               x.Resolve<IMapConfigurationOrchestration<ILogTracking>>(),
               managers.Item2),
               new ContainerControlledLifetimeManager());      

            #endregion

            container.RegisterFactory<IBosIntegratorOutFactory>(x => new BosIntegratorOutFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IBosIntegratorOut<IMessageTracking>>(name)));

            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>($"{PosType.HydraPos}", x => x.Resolve<IHydraBosOutWorker>());
            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>($"{PosType.Retalix}", x => x.Resolve<IRetalixBosOutWorker>());
            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>($"{PosType.GenericSignalRApi}", x => x.Resolve<ISignalRBosOutWorker>());
            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>($"{PosType.MadicApiSignalR}", x => x.Resolve<IMadicRestApiBosOutWorker>());

            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>(x => x.Resolve<IBosIntegratorOutFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.BosType));
            container.RegisterFactory<IBosIntegratorOutTransient<IMessageTracking>>(x => x.Resolve<IBosIntegratorOutFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.BosType));
            container.RegisterFactory<IBosIntegratorOutOfflineTransient>(x => x.Resolve<IBosIntegratorOutFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.BosType));

            container.RegisterFactory<IBosIntegratorOut<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IBosIntegratorOutOfflineTransient>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            container.RegisterFactory<IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>>(x => x.Resolve<IJournalWorker>(), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IJournalWorker>(x => new JournalWorker(
                x.Resolve<IBosIntegratorOut<IMessageTracking>>(MessageBrokerName), 
                hydraDb, 
                telemetryWorker, 
                managers.Item1.GetLogger("Journal", false),
                managers.Item1, 
                x.Resolve<ILoggingHelper>(),
                managers.Item2,
                helpers.Item1,
                x.Resolve<IPumpIntegratorInJournal>(MessageBrokerName),
                x.Resolve<IPumpIntegratorInTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<ITankGaugeIntegratorInJournal>(MessageBrokerName),
                x.Resolve<IPrinterHelper<IMessageTracking>>(),
                x.Resolve<ILocalAccountWorker>(),
                x.Resolve<IOptCollection>(),
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName), 
                helpers.Item3,
                helpers.Item4,
                x.Resolve<IPumpCollection>(),
                x.Resolve<IShiftDayEndConfig>(),
                x.Resolve<IPumpIntegratorConfiguration>()), new ContainerControlledLifetimeManager());
            container.RegisterFactory<IJournalWorkerReceipt>(x => x.Resolve<IJournalWorker>(), new ContainerControlledLifetimeManager());

            #region Mobile

            container.RegisterFactory<IMobileBosIntegratorInFactory>(x => new MobileBosIntegratorInFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IBosIntegratorInJournal<IMessageTracking>>($"{IntegrationType.MobilePos}:{name}")), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IBosIntegratorInJournal<IMessageTracking>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobile}", x => x.Resolve<IHydraMobileFullWorker>());
            container.RegisterFactory<IBosIntegratorInJournal<IMessageTracking>>($"{IntegrationType.MobilePos}:{coreConfigConstants.Integrator.PosMobileTypeHydraMobileStatus}", x => x.Resolve<IHydraMobileStatusWorker>());
            container.RegisterFactory<IBosIntegratorInJournal<IMessageTracking>>($"{IntegrationType.MobilePos}", x => x.Resolve<IMobileBosIntegratorInFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.MobilePosType));

            #endregion

            container.RegisterFactory<IBosIntegratorInJournal<IMessageTracking, Result<StatusCodeResult>>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IBosIntegratorInJournal<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IBosIntegratorInTransient<IMessageTracking, Result<StatusCodeResult>>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<IBosIntegratorIn<IMessageTracking, Result<StatusCodeResult>>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            #region Configuration Data Map

            container.RegisterFactory<IMapEvoBookConfigDataOrchestration>(x => new Forecourt.Bos.Orchestrations.EvoBook.MapConfigurationDataOrchestration(
                managers.Item1,
                managers.Item2,
                hydraDb,
                helpers.Item3,
                x.Resolve<IHttpClientFactory>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IMapConfigurationFactory>(x => new MapConfigurationFactory(
                managers.Item1,
                managers.Item2,
                (name) => x.Resolve<IMapConfigurationOrchestration<ILogTracking>>(name)));

            container.RegisterFactory<IMapConfigurationOrchestration<ILogTracking>>($"{PosType.MadicApiSignalR}", x => x.Resolve<IMapEvoBookConfigDataOrchestration>());
            container.RegisterFactory<IMapConfigurationOrchestration<ILogTracking>>(x => x.Resolve<IMapConfigurationFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.BosType));

            #endregion

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the Payment Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>, <see cref="ISocketWrapperFactory"/>) instance</param>
        /// <returns>Result status</returns>        
        private static Result<bool, Exception> RegisterIntegrators_Payment(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, (ITimerFactory, IFileSystem, ISocketWrapperFactory, IGradeHelper) helpers)
        {
            var allPumps = container.Resolve<IPumpCollection>();
            var allOpts = container.Resolve<IOptCollection>();

            #region OPT

            container.RegisterFactory<ToOptConnectionThread>(x => new ToOptConnectionThread(managers.Item1, helpers.Item3, managers.Item2));
            container.RegisterFactory<IToOptWorker>(x => new ToOptWorker(
                managers.Item1, 
                telemetryWorker, 
                x.Resolve<ToOptConnectionThread>(),
                hydraDb,
                managers.Item2, 
                allPumps,
                allOpts, 
                x.Resolve<IControllerWorker>(),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<OptHeartbeatConnectionThread>(x => new OptHeartbeatConnectionThread(managers.Item1, helpers.Item3, managers.Item2, logRxTx: false));
            container.RegisterFactory<IOptHeartbeatWorker>(x => new OptHeartbeatWorker(
                managers.Item1, 
                telemetryWorker,
                x.Resolve<OptHeartbeatConnectionThread>(), 
                hydraDb, 
                managers.Item2, 
                allPumps, 
                allOpts, 
                x.Resolve<IControllerWorker>(),
                x.Resolve<IPosIntegratorOutTransient<IMessageTracking>>(MessageBrokerName)), new ContainerControlledLifetimeManager());

            container.RegisterFactory<FromOptConnectionThread>(x => new FromOptConnectionThread(managers.Item1, helpers.Item3, managers.Item2));
            container.RegisterFactory<IPumpTransactionValidator>(x => new PumpTransactionValidator(
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixOptService}PumpTransactionValidator", false),
                new ValidationConfiguration(managers.Item1, managers.Item2)));

            container.RegisterFactory<IFromOptWorker>(x => new FromOptWorker(
                x.Resolve<IToOptWorker>(), 
                x.Resolve<IOptHeartbeatWorker>(), 
                x.Resolve<FromOptConnectionThread>(), 
                x.Resolve<IControllerWorker>(),
                x.Resolve<IPumpIntegratorInTransient<IMessageTracking>>(MessageBrokerName), 
                x.Resolve<ISecAuthIntegratorInTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>(MessageBrokerName),
                x.Resolve<ICarWashWorker>(), 
                x.Resolve<IPosIntegratorOut<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IPosIntegratorOutMode<IMessageTracking>>(MessageBrokerName),
                x.Resolve<IMediaChannelWorker>(), 
                x.Resolve<IJournalWorker>(), 
                telemetryWorker, 
                x.Resolve<IConfigUpdateWorker>(),
                x.Resolve<ILocalAccountWorker>(), // TODO: IPaymentProviderIntegratorOutJournal ADO #477703/477695
                hydraDb, 
                x.Resolve<IPaymentConfigIntegrator>(),
                allPumps, allOpts, 
                managers.Item1, 
                x.Resolve<FileVersionInfo>("ServiceVersionInfo").Version, 
                managers.Item2, helpers.Item1, helpers.Item4,
                x.Resolve<IVatCalculator>(),
                x.Resolve<IPumpTransactionValidator>(),
                x.Resolve<ICacheHelper>()), new ContainerControlledLifetimeManager());

            #endregion

            return Result.Success<bool, Exception>(true);
        }

        /// <summary>
        /// Registers the injection instances, specific to the Secondary Authorisation (SecAuth) Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="hydraDb"><see cref="IHydraDb"/> instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>, <see cref="ISocketWrapperFactory"/>) instance</param>
        /// <returns>Result status</returns>        
        private static Result<bool, Exception> RegisterIntegrators_SecAuth(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, IHydraDb hydraDb, ITelemetryWorker telemetryWorker, (ITimerFactory, IFileSystem, ISocketWrapperFactory) helpers)
        {
            var allPumps = container.Resolve<IPumpCollection>();
            var allOpts = container.Resolve<IOptCollection>();

            container.RegisterFactory<INoneSecAuthOutWorker>(x => new NoneSecAuthOutWorker(
                managers.Item1,
                managers.Item2,
                allPumps, telemetryWorker), new ContainerControlledLifetimeManager());

            container.RegisterFactory<AnprConnectionThread>(x => new AnprConnectionThread(managers.Item1, helpers.Item3, managers.Item2));
            container.RegisterFactory<IAnprWorker>(x => new AnprWorker(telemetryWorker,
                hydraDb,
                x.Resolve<ISecAuthIntegratorInTransient<IMessageTracking>>(MessageBrokerName),
                allPumps,
                managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixWorker}ANPR", false),
                x.Resolve<AnprConnectionThread>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IAnprSecAuthOutWorker>(x => new AnprSecAuthOutWorker(
              managers.Item1,
              managers.Item2,
              allPumps, telemetryWorker,
              x.Resolve<IAnprWorker>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ISignalRPreSecAuthOutWorker>(x => new SignalRPreSecAuthOutWorker(
              managers.Item1,
              managers.Item2,
              allPumps, telemetryWorker,
              x.Resolve<ISecAuthHubbable>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ISignalRPostSecAuthOutWorker>(x => new SignalRPostSecAuthOutWorker(
              managers.Item1,
              managers.Item2,
              allPumps, telemetryWorker,
              x.Resolve<ISecAuthHubbable>()), new ContainerControlledLifetimeManager());

            container.RegisterFactory<ISecAuthIntegratorOutFactory>(x => new SecAuthIntegratorOutFactory(
              managers.Item1,
              managers.Item2,
              (name) => x.Resolve<ISecAuthIntegratorOut<IMessageTracking>>(name.ToUpper())));

            container.RegisterFactory<ISecAuthIntegratorOut<IMessageTracking>>(ConfigConstants.NoneUpper, x => x.Resolve<INoneSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(ConfigConstants.NoneUpper, x => x.Resolve<INoneSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOut<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeHydra, x => x.Resolve<IAnprSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeHydra, x => x.Resolve<IAnprWorker>());
            container.RegisterFactory<ISecAuthIntegratorOut<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeSignalRApiPre, x => x.Resolve<ISignalRPreSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeSignalRApiPre, x => x.Resolve<ISignalRPreSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOut<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeSignalRApiPost, x => x.Resolve<ISignalRPostSecAuthOutWorker>());
            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(coreConfigConstants.Integrator.SecAuthTypeSignalRApiPost, x => x.Resolve<ISignalRPostSecAuthOutWorker>());

            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            container.RegisterFactory<ISecAuthIntegratorOutTransient<IMessageTracking>>(x => x.Resolve<ISecAuthIntegratorOutFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.SecAuthType));

            container.RegisterFactory<ISecAuthIntegratorInTransient<IMessageTracking>>(x => new SecAuthInWorker(
                managers.Item1,
                managers.Item2,
                allPumps,
                x.Resolve<IPumpIntegratorInTransient<IMessageTracking>>(),
                telemetryWorker,
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>()), new ContainerControlledLifetimeManager());
            container.RegisterFactory<ISecAuthIntegratorInTransient<IMessageTracking>>(MessageBrokerName, x => x.Resolve<IMessageBroker>());
            
            return Result.Success<bool, Exception>(true);
        }

        /// Registers the injection instances, specific to the Payment Configuration (PaymentConfig) Integrators
        /// </summary>
        /// <param name="container"><see cref="IUnityContainer"/> instance</param>
        /// <param name="managers">(<see cref="IHtecLogManager"/>, <see cref="IConfigurationManager"/>) tuple instance</param>
        /// <param name="dbs">(<see cref="IHydraDb"/>, <see cref="IEsocketDb"/>) tuple instance</param>
        /// <param name="telemetryWorker"><see cref="ITelemetryWorker"/> instance</param>
        /// <param name="helpers">(<see cref="ITimerFactory"/>, <see cref="IFileSystem"/>, <see cref="ISocketWrapperFactory"/>) instance</param>
        /// <returns>Result status</returns>        
        private static Result<bool, Exception> RegisterIntegrators_PaymentConfig(IUnityContainer container, (IHtecLogManager, IConfigurationManager) managers, (IHydraDb, IEsocketDb) dbs, ITelemetryWorker telemetryWorker, (ITimerFactory, IFileSystem) helpers)
        {
            container.RegisterFactory<INoneWorker>(x => new Forecourt.PaymentConfiguration.Workers.NoneWorker(
              managers.Item1,
              dbs.Item1,
              helpers.Item2,
              managers.Item2), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IESocketSqlServerWorker>(x => new Forecourt.PaymentConfiguration.Workers.ESocketSqlServerWorker(
              managers.Item1,
              dbs.Item1,
              helpers.Item2,
              dbs.Item2,
              container.Resolve<IContactlessProperties>(),
              managers.Item2), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IESocketHSqlDbWorker>(x => new Forecourt.PaymentConfiguration.Workers.ESocketHSqlDbWorker(
              managers.Item1,
              dbs.Item1,
              helpers.Item2,
              dbs.Item2,
              container.Resolve<IContactlessProperties>(),
              managers.Item2), new ContainerControlledLifetimeManager());

            container.RegisterFactory<IPaymentConfigIntegratorFactory>(x => new PaymentConfigIntegratorFactory(
             managers.Item1,
             managers.Item2,
             (name) => x.Resolve<IPaymentConfigIntegrator>(name.ToUpper())));

            container.RegisterFactory<IPaymentConfigIntegrator>(ConfigConstants.NoneUpper, x => x.Resolve<INoneWorker>());
            container.RegisterFactory<IPaymentConfigIntegrator>(Forecourt.Core.Configuration.Constants.Integrator.PaymenConfigeSocketSqlServer, x => x.Resolve<IESocketSqlServerWorker>());
            container.RegisterFactory<IPaymentConfigIntegrator>(Forecourt.Core.Configuration.Constants.Integrator.PaymenConfigeSocketHSqlDb, x => x.Resolve<IESocketHSqlDbWorker>());

            container.RegisterFactory<IPaymentConfigIntegrator>(MessageBrokerName, x => x.Resolve<IMessageBroker>());

            container.RegisterFactory<IPaymentConfigIntegrator>(x => x.Resolve<IPaymentConfigIntegratorFactory>().GetInstance(null, x.Resolve<IHydraDb>().AdvancedConfig.PaymentConfigType));
            
            container.RegisterFactory<IContactlessProperties>(x => new ContactlessProperties(managers.Item1.GetLogger($"{ConfigConstants.LoggerPrefixOptService}ContactlessProperties", false)));

            return Result.Success<bool, Exception>(true);
        }

        private static void RegisterMessageBrokerWorkers(IUnityContainer x)
        {
            var broker = _container.Resolve<IMessageBroker>();
            broker.RegisterWorkers(
                x.Resolve<IJournalWorker>(),
                x.Resolve<IFromOptWorker>(),
                x.Resolve<IControllerWorker>(),
                x.Resolve<IPumpIntegratorInJournal>(),
                x.Resolve<ITankGaugeIntegratorInJournal>(),
                x.Resolve<IBosIntegratorInJournal<IMessageTracking>>(IntegrationType.MobilePos.ToString()));
            broker.RegisterWorkers(
                x.Resolve<IPosIntegratorInMode<IMessageTracking>>(IntegrationType.Pos.ToString()),
                x.Resolve<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>(IntegrationType.Pos.ToString()),
                x.Resolve<IPosIntegratorInTransient<IMessageTracking, IPump, Result>>(IntegrationType.MobilePos.ToString())
                );
            broker.RegisterWorkers(
                x.Resolve<IBosIntegratorOut<IMessageTracking>>(),
                x.Resolve<ISecAuthIntegratorInTransient<IMessageTracking>>(),
                x.Resolve<ISecAuthIntegratorOutTransient<IMessageTracking>>());
            x.Resolve<IControllerWorker>()?.RegisterWorker(broker);
        }
    }
}
