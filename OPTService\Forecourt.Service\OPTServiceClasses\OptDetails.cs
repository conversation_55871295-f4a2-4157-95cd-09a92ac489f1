﻿using System.Collections.Generic;

namespace OPTService.OPTServiceClasses
{
    public class OptDetails
    {
        public string StringId { get; set; }
        public string Status { get; set; }
        public List<PumpDetails> Pumps { get; set; }
        public bool Connected { get; set; }
        public bool SignedIn { get; set; }
        public bool PrinterError { get; set; }
        public bool PaperLow { get; set; }
        public bool HasContactless { get; set; }
        public bool InUse { get; set; }
        public string SoftwareVersion { get; set; }
        public List<string> AvailableSoftware { get; set; }
        public List<string> AvailableSecureAssets { get; set; }
        public List<string> AvailableCpatAssets { get; set; }
        public string IpAddress { get; set; }
        public string Subnet { get; set; }
        public string Gateway { get; set; }
        public string Dns1 { get; set; }
        public string Dns2 { get; set; }
        public string[] ReceiptHeaders { get; set; }
        public string[] ReceiptFooters { get; set; }
        public string PlaylistFileName { get; set; }
        public bool MediaChannel { get; set; }
        public string SecureAssetsVersion { get; set; }
        public string MultimediaAssetsVersion { get; set; }
        public string CpatAssetsVersion { get; set; }
        public string OptFirmwareVersion { get; set; }
        public string EmvKernelVersion { get; set; }
        public string PluginType { get; set; }
        public string PluginVersion { get; set; }
        public bool ModeChangePending { get; set; }
        public bool ConfigChangePending { get; set; }
        public bool LogFileRequestSent { get; set; }

        public string DeviceStatus { get; set; }
        public string DeviceStatusCode { get; set; }
    }
}