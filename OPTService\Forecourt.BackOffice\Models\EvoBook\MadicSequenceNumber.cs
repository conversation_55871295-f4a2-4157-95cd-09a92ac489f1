﻿using Newtonsoft.Json;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class MadicSequenceNumber
    {
        [JsonProperty("companyId")]
        public int CompanyId { get; set; }
        [JsonProperty("storeId")]
        public int StoreId { get; set; }
        [JsonProperty("workstationId")]
        public int WorkstationId { get; set; }
        [JsonProperty("nextSequenceNumber")]
        public int NextSequenceNumber { get; set; }
        [JsonProperty("shiftId")]
        public int ShiftId { get; set; }
        [JsonProperty("periodId")]
        public int PeriodId { get; set; }
        [JsonProperty("currentBusinessDay")]
        public string CurrentBusinessDay { get; set; }
    }
}
