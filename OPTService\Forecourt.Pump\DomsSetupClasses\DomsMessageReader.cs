using CSharpFunctionalExtensions;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.ConnectionThreads.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Pump.Common;
using Htec.Hydra.Core.Pump.Interfaces.Doms;
using Htec.Hydra.Core.Pump.Messages;
using Htec.Hydra.Core.Pump.Messages.Doms.Setup;
using Htec.Hydra.Core.Pump.Messages.Extensions;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;

namespace Forecourt.Pump.DomsSetupClasses
{
    [HasConfiguration]
    public class DomsMessageReader : DomsMessage, IDomsMessageReader
    {
        private readonly Random _random = new();
        private readonly IDomsSetup _setup;
        private readonly bool _updateSetup;

        private readonly ISet<byte> _receivedApcs = new HashSet<byte>();
        private readonly ISet<byte> _rejectedApcs = new HashSet<byte>();
        private readonly IDictionary<byte, DateTime> _apcHeartbeats = new ConcurrentDictionary<byte, DateTime>();
        public IDictionary<byte, DateTime> ApcHeartbeats => new Dictionary<byte, DateTime>(_apcHeartbeats);

        public bool MessageReject { get; private set; } = false;

        private readonly IDictionary<byte, IDictionary<DomsMessageCode, byte[]>> _lastPumpMessage = new ConcurrentDictionary<byte, IDictionary<DomsMessageCode, byte[]>>();

        /// <inheritdoc cref="IConnectionThread"/>
        public ConfigurableBool LogRxTx { get; private set; }

        private IDictionary<(DomsMessageCode, byte), bool> _logRxTxMessageCode;

        public bool LoggingEnabled(DomsMessageCode msgCode, byte subC) => (!_logRxTxMessageCode.TryGetValue((msgCode, subC), out var lm) || lm) && LogRxTx.GetValue();

        public DomsMessageReader(IDomsSetup setup, IHtecLogManager logManager, bool updateSetup, IConfigurationManager configurationManager) : base(logManager, nameof(DomsMessageReader), configurationManager)
        {
            _setup = setup;
            _updateSetup = updateSetup;

            LogRxTx = new ConfigurableBool(this, DomsSetup.ConfigKeyLogRxTx, DomsSetup.DefaultValueLogRxTx);

            _logRxTxMessageCode = new ConcurrentDictionary<(DomsMessageCode, byte), bool>()
            {
                [(DomsMessageCode.FpStatus, 0)] = false
            };
        }

        public void ClearReceived(byte apc)
        {
            _receivedApcs.Remove(apc);
            _rejectedApcs.Remove(apc);
        }

        public bool HasReceived(byte apc)
        {
            return _receivedApcs.Contains(apc);
        }

        public bool HasRejected(byte apc)
        {
            return _rejectedApcs.Contains(apc);
        }

        private void ReceivedApc(byte apc)
        {
            _receivedApcs.Add(apc);
            _apcHeartbeats[apc] = DateTime.UtcNow;
        }

        private void RejectedApc(byte apc)
        {
            _rejectedApcs.Add(apc);
        }

        private static byte UnBcd1(byte number) => (byte)UnBcd(new byte[] { number });

        private static uint UnBcd(IEnumerable<byte> bytes)
        {
            long result = 0;
            foreach (byte b in bytes)
            {
                result = (result * 100) + ((b / 16) * 10) + (b % 16);
            }

            return (uint)result;
        }

        // ReSharper disable once ParameterTypeCanBeEnumerable.Local
        private static string AsciiString(byte[] bytes)
        {
            return Encoding.ASCII.GetString(bytes.TakeWhile(x => x != 0).ToArray());
        }

        private static int FromTwoByte(byte[] bytes)
        {
            return bytes.Length == 2 ? bytes[0] + bytes[1] * 256 : 0;
        }

        private DomsSetupPump Pump(byte fpId) => _setup.Pump(fpId);

        private void ExtractFcPriceSet(byte subc, byte[] data)
        {
            if (subc != 0x02 || data.Length < 3)
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcPriceSet";
            byte fcPriceSetId = UnBcd1(data[0]);
            if (_updateSetup)
            {
                _setup.SetPriceSetId(fcPriceSetId);
            }

            int noFcPriceGroups = data[1];
            int noFcGrades = data[2];
            if (data.Length != 3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 7)
            {
                LogInvalidMessageFormat();
                return;
            }

            byte[] fcPriceGroupId = new byte[noFcPriceGroups];
            byte[] fcGradeId = new byte[noFcGrades];

            for (int i = 0; i < noFcPriceGroups; i++)
            {
                fcPriceGroupId[i] = UnBcd1(data[3 + i]);
            }

            for (int i = 0; i < noFcGrades; i++)
            {
                fcGradeId[i] = UnBcd1(data[3 + noFcPriceGroups + i]);
            }

            var logMessages = new List<string>() { "[GroupId/GradeId/Price]: [" };
            for (int i = 0; i < noFcPriceGroups; i++)
            {
                for (int j = 0; j < noFcGrades; j++)
                {
                    uint price = UnBcd(data.Skip(3 + noFcPriceGroups + noFcGrades + 3 * (i * noFcGrades + j)).Take(3));
                    logMessages.Add($" {fcPriceGroupId[i]}/{fcGradeId[j]}/{price};");
                    if (_updateSetup)
                    {
                        _setup.SetGradePrice(fcGradeId[j], fcPriceGroupId[i], price);
                    }
                }
            }
            logMessages.Add(" ]");

            int timestampYear = (int)UnBcd(data.Skip(3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades).Take(2));
            int timestampMonth = UnBcd1(data[3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 2]);
            int timestampDay = UnBcd1(data[3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 3]);
            int timestampHour = UnBcd1(data[3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 4]);
            int timestampMinute = UnBcd1(data[3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 5]);
            int timestampSecond = UnBcd1(data[3 + noFcPriceGroups + noFcGrades + 3 * noFcPriceGroups * noFcGrades + 6]);
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "PriceSetId", () => new[] { $"{fcPriceSetId}; Timestamp: {FormatDateTime(new DateTime(timestampYear, timestampMonth, timestampDay, timestampHour, timestampMinute, timestampSecond))}; {string.Join(string.Empty, logMessages)}" }, methodName: methodName);
            if (_updateSetup)
            {
                _setup.SetPriceSetTimestamp(new DateTime(timestampYear, timestampMonth, timestampDay, timestampHour, timestampMinute, timestampSecond));
            }
        }

        private void ExtractFcParameterSet(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length < 3)
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcParameterSet";
            byte parGroupId = data[0];
            byte parSetId = data[1];
            uint noFcPars = data[2];
            var logMessages = new List<string>();
            if (parGroupId == 0x04 && parSetId == 0x01)
            {
                if (data.Length != 3 + 13 * noFcPars)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                for (int i = 0; i < noFcPars; i++)
                {
                    byte gradeId = UnBcd1(data[3 + 13 * i]);
                    string gradeText = AsciiString(data.Skip(3 + 13 * i + 1).Take(12).ToArray());
                    logMessages.Add($" {gradeId}/{gradeText.Trim()};");
                    if (_updateSetup)
                    {
                        _setup.SetGradeName(gradeId, gradeText);
                    }
                }
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "GroupId/SetId", () => new[] { $"{parGroupId}/{parSetId}; [GradeId/GradeText]: [ {string.Join(string.Empty, logMessages)} ]" }, methodName: methodName);
            }
            else if (parGroupId == 0x04 && parSetId == 0x04)
            {
                if (data.Length != 3 + 7 * noFcPars)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                for (int i = 0; i < noFcPars; i++)
                {
                    byte gradeId = UnBcd1(data[3 + 7 * i]);
                    string gradeColour = AsciiString(data.Skip(3 + 7 * i + 1).Take(6).ToArray());
                    logMessages.Add($" {gradeId}/{gradeColour};");
                    if (_updateSetup)
                    {
                        _setup.SetGradeColour(gradeId, gradeColour);
                    }
                }
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "GroupId/SetId", () => new[] { $"{parGroupId}/{parSetId}; [GradeId/GradeColour]: [ {string.Join(string.Empty, logMessages)} ]" }, methodName: methodName);
            }
            else
            {
                LogInvalidMessageFormat();
            }
        }

        private void ExtractFcInstallStatus(byte subc, byte[] data)
        {
            if (!(subc == 0x00 && data.Length > 0 || subc == 0x01 && data.Length > 0))
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcInstallStatus";
            byte noInstalledFcDeviceGroups = data[0];
            byte[] nextBytes = data.Skip(1).ToArray();
            for (int i = 0; i < noInstalledFcDeviceGroups; i++)
            {
                var logMessages = new List<string>();

                if (nextBytes.Length < 2 || (nextBytes[0] == 0x00 || nextBytes[0] == 0xFF) && nextBytes.Length < 4)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                byte installMsgCode = (byte)(nextBytes[0] == 0x00 ? FromTwoByte(nextBytes.Skip(1).Take(2).ToArray()) - 0x8000 :
                    nextBytes[0] == 0xFF ? FromTwoByte(nextBytes.Skip(1).Take(2).ToArray()) :
                    nextBytes[0] < 0x80 ? nextBytes[0] : nextBytes[0] - 0x80);
                int extend = nextBytes[0] == 0x00 | nextBytes[0] == 0xFF ? 2 : 0;

                byte noInstalledFcDevices = nextBytes[extend + 1];
                if (nextBytes.Length < noInstalledFcDevices + extend + 2)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                IList<byte> devices = new List<byte>();
                if (noInstalledFcDevices > 0)
                {
                    logMessages.Add("[Device/Id]: [");
                    for (int j = 0; j < noInstalledFcDevices; j++)
                    {
                        byte deviceId = UnBcd1(nextBytes[extend + 2 + j]);
                        logMessages.Add($" {j}/{deviceId};");
                        devices.Add(deviceId);
                    }
                    logMessages.Add(" ]");
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"{FormatHeader("Group", noInstalledFcDeviceGroups, i)}.MessageCode", () => new[] {
                    (installMsgCode == 0x10
                        ?
                        "Fuelling Points"
                        : installMsgCode == 0x50
                            ? "Electronic Payment Terminals"
                            : installMsgCode == 0x40
                                ? "Tank Gauges"
                                : installMsgCode == 0x37
                                    ? "Price Poles"
                                    : $"0x{installMsgCode:X2}") + $"; extend: {extend}; Count: {noInstalledFcDevices}; {string.Join(string.Empty, logMessages)}"}, methodName: methodName);

                if (installMsgCode == 0x10)
                {
                    _setup.SetInstalledPumps(devices);
                }
                else if (installMsgCode == 0x50)
                {
                    _setup.SetInstalledEpts(devices);
                }
                else if (installMsgCode == 0x40)
                {
                    _setup.SetInstalledTankGauges(devices);
                }
                else if (installMsgCode == 0x37)
                {
                    _setup.SetInstalledPricePoles(devices);
                }

                nextBytes = nextBytes.Skip(noInstalledFcDevices + 2 + extend).ToArray();
            }
        }

        private void ExtractFcPriceSetStatus(byte subc, byte[] data)
        {
            if (subc != 0 || data.Length != 8)
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcPriceSetStatus";
            byte priceSetId = UnBcd1(data[0]);
            int timestampYear = (int)UnBcd(data.Skip(1).Take(2));
            int timestampMonth = UnBcd1(data[3]);
            int timestampDay = UnBcd1(data[4]);
            int timestampHour = UnBcd1(data[5]);
            int timestampMinute = UnBcd1(data[6]);
            int timestampSecond = UnBcd1(data[7]);
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "PriceSetId", () => new[] { $"{priceSetId}; Timestamp: {FormatDateTime(new DateTime(timestampYear, timestampMonth, timestampDay, timestampHour, timestampMinute, timestampSecond))}" }, methodName: methodName);
            if (_updateSetup)
            {
                _setup.SetPriceSetTimestamp(new DateTime(timestampYear, timestampMonth, timestampDay, timestampHour, timestampMinute,
                    timestampSecond));
            }
        }

        private void ExtractOperationModeStatus(byte subc, byte[] data)
        {
            if (subc != 0 || data.Length != 8)
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "OperationModeStatus";
            byte fcOperationMode = data[0];
            int timestampYear = (int)UnBcd(data.Skip(1).Take(2));
            int timestampMonth = UnBcd1(data[3]);
            int timestampDay = UnBcd1(data[4]);
            int timestampHour = UnBcd1(data[5]);
            int timestampMinute = UnBcd1(data[6]);
            int timestampSecond = UnBcd1(data[7]);
            DoDeferredLogging(LogLevel.Info, "Mode", () => new[] { $"{fcOperationMode}; Timestamp: {FormatDateTime(new DateTime(timestampYear, timestampMonth, timestampDay, timestampHour, timestampMinute, timestampSecond))}" }, methodName: methodName);
        }     

        private void ExtractFpInstallData(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.FpInstallData, subc, data, (subc, data) => subc != 0x00 || data.Length < 2, out result, (fpId) =>
            {
                var methodName = "FpInstallData";
                byte noFpInstallPars = data[1];
                byte[] nextBytes = data.Skip(2).ToArray();
                for (int i = 0; i < noFpInstallPars; i++)
                {
                    if (nextBytes.Length < 3)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    byte fpInstallParId = UnBcd1(nextBytes[0]);
                    int fpInstallParLength = FromTwoByte(nextBytes.Skip(1).Take(2).ToArray());
                    if (nextBytes.Length < 3 + fpInstallParLength)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    byte[] fpInstallPar = nextBytes.Skip(3).Take(fpInstallParLength).ToArray();
                    var paramType = fpInstallParId == 0x02 ? "Pss Channel No" :
                        fpInstallParId == 0x03 ? "Device Communication Address" :
                        fpInstallParId == 0x05 ? "FP Grade Options" :
                        fpInstallParId == 0x06 ? "Pump Interface Type General" :
                        fpInstallParId == 0x07 ? "Pump Interface Type Protocol" :
                        fpInstallParId == 0x08 ? "Decimal Position in Money" : "Install parameter";
                    if (_updateSetup)
                    {
                        if (fpInstallParId == 0x02 && fpInstallParLength == 1)
                        {
                            Pump(fpId).SetPssChannelNumber(fpInstallPar[0]);
                        }
                        else if (fpInstallParId == 0x03 && fpInstallParLength > 2)
                        {
                            int pssExtProtocolId = FromTwoByte(fpInstallPar.Take(2).ToArray());
                            Pump(fpId).SetPssExtProtocolId(pssExtProtocolId);

                            byte deviceAddressType = fpInstallPar[2];
                            if (deviceAddressType == 0x01 && fpInstallParLength == 5 && fpInstallPar[3] == Pump(fpId).PhysicalAddress &&
                                fpInstallPar[4] == 0x00)
                            {
                                Pump(fpId).SetPhysicalAddress();

                                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"FpId", () => new[] {
                                $"{fpId}; Type: {paramType}; PSSExtProtocolId: {pssExtProtocolId}; PhysicalAddress: {Pump(fpId).PhysicalAddress}"
                            }, methodName: methodName);
                            }
                            else if (deviceAddressType == 0x02 && fpInstallParLength == 10)
                            {
                                string ipAddress = $"{fpInstallPar[6]}.{fpInstallPar[5]}.{fpInstallPar[4]}.{fpInstallPar[3]}";
                                ushort port = (ushort)FromTwoByte(fpInstallPar.Skip(7).Take(2).ToArray());
                                byte subaddress = fpInstallPar[9];
                                Pump(fpId).SetIpAddressAndPort(ipAddress, port);

                                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"FpId", () => new[] {
                                $"{fpId}; Type: {paramType}; PSSExtProtocolId: {pssExtProtocolId}; {ipAddress}:{port}"
                            }, methodName: methodName);
                            }
                            else
                            {
                                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"FpId", () => new[] { $"{fpId}; InvalidType: {deviceAddressType}" }, methodName: methodName);
                            }
                        }
                        else if (fpInstallParId == 0x05 && fpInstallParLength >= 1)
                        {
                            byte noFpGradeOptions = fpInstallPar[0];
                            var info = $"{fpId}; Type: {paramType}; Count: {noFpGradeOptions}; [ ";
                            byte[] gradeOptionBytes = fpInstallPar.Skip(1).ToArray();
                            for (int j = 0; j < noFpGradeOptions; j++)
                            {
                                if (gradeOptionBytes.Length < 3)
                                {
                                    LogInvalidMessageFormat();
                                    return;
                                }

                                byte gradeOptionNo = gradeOptionBytes[0];
                                byte gradeId = UnBcd1(gradeOptionBytes[1]);
                                byte noGradeOptionPars = gradeOptionBytes[2];
                                uint nozzleId = 0;
                                byte tankId = 0;
                                byte tankParts = 0;
                                var infoOption = $"[Number/Grade: {gradeOptionNo}/{gradeId}; Params.Count: {noGradeOptionPars}";

                                gradeOptionBytes = gradeOptionBytes.Skip(3).ToArray();
                                for (int k = 0; k < noGradeOptionPars; k++)
                                {
                                    if (gradeOptionBytes.Length < 3)
                                    {
                                        LogInvalidMessageFormat();
                                        return;
                                    }

                                    byte gradeOptionParId = UnBcd1(gradeOptionBytes[0]);
                                    int gradeOptionParLength = FromTwoByte(gradeOptionBytes.Skip(1).Take(2).ToArray());
                                    if (gradeOptionBytes.Length < 3 + gradeOptionParLength)
                                    {
                                        LogInvalidMessageFormat();
                                        return;
                                    }

                                    var gradeOptionPar = gradeOptionBytes.Skip(3).Take(gradeOptionParLength).ToArray();

                                    infoOption += $"; Param: {gradeOptionParId}/";
                                    if (gradeOptionParId == 0x01 && gradeOptionParLength >= 1)
                                    {
                                        var noTanks = gradeOptionPar[0];
                                        infoOption += $"TankCount: {noTanks}; Id/Part: [ ";
                                        for (var t = 0; t < noTanks; t++)
                                        {
                                            tankId = UnBcd1(gradeOptionPar[1 + t * 2]);
                                            tankParts = UnBcd1(gradeOptionPar[2 + t * 2]);
                                            infoOption += $"{tankId}/{tankParts}; ";
                                        }
                                        infoOption += " ]";
                                    }
                                    else if (gradeOptionParId == 0x02 && gradeOptionParLength >= 1)
                                    {
                                        byte nozzleIdLength = gradeOptionPar[0];
                                        nozzleId = gradeOptionParLength == 1 ? Convert.ToUInt16(UnBcd1(gradeOptionPar[0])) : Convert.ToUInt16(AsciiString(new[] { gradeOptionPar[1] }));
                                        infoOption += $"NozzleId: {nozzleId}";
                                    }

                                    gradeOptionBytes = gradeOptionBytes.Skip(3 + gradeOptionParLength).ToArray();
                                }

                                info += $"{infoOption}]; ";
                                _setup.SetGradeOption(fpId, gradeOptionNo, gradeId, (byte)nozzleId, tankId, tankParts);
                            }

                            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"FpId", () => new[] { $"{info} ]" });
                        }
                        else if (fpInstallParId == 0x06 && fpInstallParLength == 2)
                        {
                            Pump(fpId).SetPumpInterfaceTypeGeneral(FromTwoByte(fpInstallPar.Take(2).ToArray()));
                        }
                        else if (fpInstallParId == 0x07 && fpInstallParLength == 2)
                        {
                            Pump(fpId).SetPumpInterfaceTypeProtocol(FromTwoByte(fpInstallPar.Take(2).ToArray()));
                        }
                        else if (fpInstallParId == 0x08 && fpInstallParLength == 1)
                        {
                            Pump(fpId).SetDecimalPositionInMoney(fpInstallPar[0]);
                        }
                    }

                    nextBytes = nextBytes.Skip(3 + fpInstallParLength).ToArray();
                }
            });
        }

        private void ExtractChangeFcPriceSet(byte subc, byte[] data)
        {
            if (subc != 0x02 || data.Length != 1)
            {
                LogInvalidMessageFormat();
                return;
            }

            byte fcPriceSetId = UnBcd1(data[0]);
            GetLogger().Info($"DOMS - Load Fc Price Set Request Response, Price Set Id is {fcPriceSetId}");
        }

        private void ExtractChangeFcParameters(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length != 0)
            {
                LogInvalidMessageFormat();
                return;
            }

            GetLogger().Info("DOMS - Parameter Set Request Response OK");
        }

        private void ExtractClearInstallData(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length != 0)
            {
                LogInvalidMessageFormat();
                return;
            }

            GetLogger().Info("DOMS - Fp Clear Request Response OK");
        }

        private void ExtractInstallFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.InstallFp, subc, data, (subc, data) => subc != 0x03 || data.Length != 1, out result);
        }

        private void ExtractOpenFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.OpenFp, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ExtractCloseFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.CloseFp, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ExtractFpStatus(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpActionWithChecks(DomsMessageCode.FpStatus, subc, data, (subc, data) => !(subc == 0x00 && data.Length == 5 || subc == 0x03 && data.Length > 6), out result, (fpId) =>
            {
                var methodName = "FpStatus";
                byte activeSmId = UnBcd1(data[1]);
                byte fpMainState = data[2];
                byte fpSubStates = data[3];
                var errorState = (fpSubStates & 0x020) == 0x020;
                byte fpLockId = UnBcd1(data[4]);
                var info = $"{fpId}; Active SMId: {activeSmId}; Main/Sub State: {(DomsSetupMainState)fpMainState}/{fpSubStates}; LockId: {fpLockId}";

                var pump = Pump(fpId);
                if (subc == 0x00)
                {
                    if (_updateSetup)
                    {
                        pump.ReceivedHeartbeat();
                    }
                    return false;
                }
                else
                if(subc == 0x03)
                {
                    byte fcGradeId = UnBcd1(data[5]);
                    // Keep previous value
                    byte fcGradeOption = pump.GradeOption;
                    uint fpNozzleId = pump.HoseId;
                    byte noSupplStatusPars = data[6];

                    byte[] nextBytes = data.Skip(7).ToArray();
                    for (int i = 0; i < noSupplStatusPars; i++)
                    {
                        if (nextBytes.Length < 2)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        byte parId = UnBcd1(nextBytes[0]);
                        byte parLength = nextBytes[1];
                        if (nextBytes.Length < 2 + parLength)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        var parContent = nextBytes.Skip(2).Take(parLength).ToArray();

                        if (parId == 0x04 && parLength == 1)
                        {
                            fcGradeOption = UnBcd1(parContent[0]);
                        }
                        else                        
                        if (parId == 0x09)
                        {
                            fpNozzleId = parLength == 1 ? Convert.ToUInt16(UnBcd1(parContent[0])) : Convert.ToUInt16(AsciiString(new[] { parContent[1] }));
                        }

                        nextBytes = nextBytes.Skip(2 + parLength).ToArray();
                    }
                   
                    if (errorState)
                    {
                        _setup.FetchFromDoms(FetchFcRequestType.None, FetchFpRequestType.Error, fpId);
                    }

                    DoDeferredLogging(LoggingEnabled(DomsMessageCode.FpStatus, subc) ? LogLevel.Info : LogLevel.None, "FpId", () => new[] { $"{info}; GradeId: {fcGradeId}; Param.Count: {noSupplStatusPars}; GradeOption: {fcGradeOption}; NozzleId: {fpNozzleId}" }, methodName: methodName);

                    if (_updateSetup)
                    {
                        pump.SetFcGradeInfo(fcGradeId, (byte)fpNozzleId, fcGradeOption);
                    }
                }

                if (_updateSetup)
                {
                    pump.SetActiveSmId(activeSmId);
                    pump.SetMainState(fpMainState);
                    pump.SetSubStates(fpSubStates);
                    pump.SetLockId(fpLockId);
                }

                return _updateSetup;
            }, checkLength: subc == 0x00 ? -1 : 6);
        }

        private void ExtractFcLogon(byte apc, byte subc, byte[] data)
        {
            if (subc != 0x01 || data.Length < 19)
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcLogon";
            uint countryCode = UnBcd(data.Take(2));
            int hwType = FromTwoByte(data.Skip(2).Take(2).ToArray());
            uint hwVersionNumber = UnBcd(data.Skip(4).Take(4));
            int swType = FromTwoByte(data.Skip(8).Take(2).ToArray());
            uint swVersionNumber = UnBcd(data.Skip(10).Take(4));
            string swVersionString =
                $"{swVersionNumber / 100000:D3}-{swVersionNumber / 1000 % 100:D2}-{swVersionNumber / 100 % 10}.{swVersionNumber % 100:D2}";
            int swYear = (int) UnBcd(data.Skip(14).Take(2));
            int swMonth = UnBcd1(data[16]);
            int swDay = UnBcd1(data[17]);
            var swDate = new DateTime(swYear, swMonth, swDay);
            byte noSwBlocks = data[18];
            byte[] nextBytes = data.Skip(19).ToArray();
            for (int i = 0; i < noSwBlocks; i++)
            {
                if (nextBytes.Length < 6)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                byte swMainBlockId = UnBcd1(nextBytes[0]);
                byte swSubBlockId = UnBcd1(nextBytes[1]);
                uint swBlockReleaseNumber = UnBcd(nextBytes.Skip(2).Take(2));
                int swBlockCheckCode = FromTwoByte(nextBytes.Skip(4).Take(2).ToArray());
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"APC.{apc}.{FormatHeader("SoftwareBlock", noSwBlocks, i)}", () => new[] { 
                    $"Main/Sub/ReleaseNumber/CheckCode: {swMainBlockId}/{swSubBlockId}/{swBlockReleaseNumber}/0x{swBlockCheckCode:X4}" }, methodName: methodName);
                nextBytes = nextBytes.Skip(6).ToArray();
            }

            if (nextBytes.Length < 1)
            {
                LogInvalidMessageFormat();
                return;
            }

            byte noUnsolMessages = nextBytes[0];
            nextBytes = nextBytes.Skip(1).ToArray();
            for (int i = 0; i < noUnsolMessages; i++)
            {
                if (nextBytes.Length < 3)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                int extc = FromTwoByte(nextBytes.Take(2).ToArray());
                DomsMessageCode msgCode = (DomsMessageCode) extc;
                byte mySubc = nextBytes[2];
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"APC.{apc}.{FormatHeader("Msg", noUnsolMessages, i)}", () => new[] { $"EXTC/SUBC: 0x{extc:X4}/0x{mySubc:X2} ({msgCode})" }, methodName: methodName);
                nextBytes = nextBytes.Skip(3).ToArray();
            }

            _setup.SetLoggedOn(apc);

            var sdDtm = $"{swDate:dd/MM/yyyy}";
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"APC.{apc}.Misc", () => new[] {
                    $"CountryCode: {countryCode}; HW Type/Version:{hwType}/{hwVersionNumber}; SW Type/Version/Date: {swType}/{swVersionString}/{sdDtm}; " +
                    $"Number of SoftwareBlocks/Unsolicited Messages: {noSwBlocks}/{noUnsolMessages}" }, methodName: methodName);
            
            _setup.SetSoftwareVersion(swVersionString, sdDtm);
        }

        private void ExtractFpFuellingData(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpActionWithChecks(DomsMessageCode.FpFuellingData, subc, data, (subc, data) => subc != 0x00 || data.Length != 7, out result, (fpId) => 
            {
                var methodName = "FpFuellingData"; // "FpPumpGradeTotals";

                var pump = Pump(fpId);
                var vol = UnBcd(data.Skip(1).Take(3));
                var money = UnBcd(data.Skip(4).Take(3));

                _setup.SetFuellingData(fpId, vol, money);

                return pump.MainState == DomsSetupMainState.Fuelling;
            });
        }

        private void ExtractFpError(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.FpError, subc, data, (subc, data) => subc != 0x00 || data.Length != 16, out result, (fpId) =>
            {
                var methodName = "FpError"; 

                var pump = Pump(fpId);
                var code = UnBcd1(data[1]);
                int yy = (int)UnBcd(data.Skip(2).Take(2));
                int mm = UnBcd1(data[4]);
                int dd = UnBcd1(data[5]);
                int hh = UnBcd1(data[6]);
                int mi = UnBcd1(data[7]);
                int ss = UnBcd1(data[8]);
                var date = new DateTime(yy, mm, dd, hh, mi, ss);

                _setup.SetErrorInfo(fpId, code, date);

                DoDeferredLogging(LogLevel.Info, "FpId", () => new[] { $"{fpId}; Code: {code}; Date: {date:dd/MM/yyyy HH:mm:ss}" }, methodName: methodName);
            });
        }

        private void ExtractClearFpError(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.ClearFpError, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result, (fpId) =>
            {
                var methodName = "ClearFpError";

                var pump = Pump(fpId);

                _setup.SetErrorInfo(fpId);

                DoDeferredLogging(LogLevel.Info, "FpId", () => new[] { $"{fpId};" }, methodName: methodName);
            });
        }

        private void ExtractReserveFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.ReserveFp, subc, data, (subc, data) => subc != 0x01 || data.Length != 1, out result, (fpId) => _setup.SetReserved(fpId));
        }

        private void ExtractAuthoriseFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.AuthoriseFp, subc, data, (subc, data) => subc != 0x02 || data.Length != 1, out result, (fpId) => _setup.SetAuthorised(fpId));
        }

        private void ExtractCancelFpAuth(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.CancelFpAuth, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result, (fpId) => _setup.SetCancelAuthorised(fpId));
        }

        private void ExtractFpSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.FpSupTrans, subc, data, (subc, data) => subc != 0x00 || data.Length <= 5, out result, (fpId) =>
            {
                ExtractXxxTrans(fpId, data, "FpSupTrans.Status", false);
            });
        }

        private void ExtractUnlockFpSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.UnlockFpUnSupTrans, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ExtractClearFpSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.ClearFpSupTrans, subc, data, (subc, data) => subc != 0x04 || data.Length != 1, out result, (fpId) => _setup.SetCleared(fpId));
        }

        private void ExtractFpUnSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.FpUnSupTrans, subc, data, (subc, data) => subc != 0x00 || data.Length <= 5, out result, (fpId) =>
            {
                ExtractXxxTrans(fpId, data, "FpUnSupTrans.Status", true);
            });
        }

        private void ExtractXxxTrans(byte fpId, byte[] data, string methodName, bool setLocked = false, byte parLength = 1)
        {
            var pump = Pump(fpId);

            // Keep previous value
            var fpGradeId = pump.GradeId;
            byte fpGradeOption = pump.GradeOption;
            uint fpNozzleId = pump.HoseId;

            int transSeqNo = (int)UnBcd(data.Take(2));
            byte noTransParam = data[3];

            byte[] nextBytes = data.Skip(4).ToArray();
            for (int i = 0; i < noTransParam; i++)
            {
                if (nextBytes.Length < 2)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                var temp = (byte)0;
                byte parId = UnBcd1(nextBytes[0]);
                if (parId == 51)
                {
                    temp = UnBcd1(nextBytes[1]);
                    fpGradeId = temp != 0 ? temp : fpGradeId;
                }
                else
                if (parId == 56)
                {
                    temp = nextBytes[1];
                    fpGradeOption = temp != 0 ? temp : fpGradeOption;
                }

                nextBytes = nextBytes.Skip(parLength + 1).ToArray();
            }

            if (_updateSetup)
            {
                if (setLocked)
                {
                    _setup.SetLocked(fpId);
                }

                if (pump.GradeId != fpGradeId || pump.GradeOption != fpGradeOption || pump.HoseId != fpNozzleId)
                {
                    DoDeferredLogging(LogLevel.Warn, "FpId", () => new[] { $"{fpId}; Overriding Grade/Hose info; Grade: {pump.GradeId}/{fpGradeId}; GradeOption: {pump.GradeOption}/{fpGradeOption}; Hose: {pump.HoseId}/{fpNozzleId}" }, methodName: methodName);
                    pump.SetFcGradeInfo(fpGradeId, (byte)fpNozzleId, fpGradeOption);
                }
            }
        }

        private void ExtractUnlockFpUnSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.UnlockFpUnSupTrans, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ExtractClearFpUnSupTrans(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.ClearFpUnSupTrans, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result, (fpId) => _setup.SetCleared(fpId));
        }

        private void ExtractEstopFp(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.EstopFp, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ExtractCancelFpEstop(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.CancelFpEstop, subc, data, (subc, data) => subc != 0x00 || data.Length != 1, out result);
        }

        private void ResetPumpStateOnXxxTransBufStatus(DomsSetupPump pump)
        {
            pump.SetIsReserved(false);
            pump.SetIsAuthorised(false);
            var data = pump.FuellingData;
            data.Amount = 0;
            data.Volume = 0;
            data.Count = 0;
        }

        private void ExtractFpSupTransBufStatus(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpActionWithChecks(DomsMessageCode.FpSupTransBufStatus, subc, data, (subc, data) => !(subc == 0x00 && data.Length > 1 || subc == 0x03 && data.Length > 1), out result, (fpId) =>
            {
                var methodName = "FpSupTransBuf.Status";
                byte noTrans = data[1];
                byte[] nextBytes = data.Skip(2).ToArray();
                var pump = Pump(fpId);

                if (_updateSetup)
                {
                    pump.ClearTransactionList();
                }

                if (noTrans == 0)
                {
                    return true;
                }

                for (byte i = 0; i < noTrans; i++)
                {
                    var transNum = (byte)(i + 1);

                    if (subc == 0x00)
                    {
                        if (nextBytes.Length < 5)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        int transSeqNo = (int)UnBcd(nextBytes.Take(2));
                        byte smId = UnBcd1(nextBytes[2]);
                        byte transLockId = UnBcd1(nextBytes[3]);
                        byte transInfoMask = nextBytes[4];
                        bool storedTrans = (transInfoMask & 0x01) == 0x01;
                        bool errorTrans = (transInfoMask & 0x02) == 0x02;
                        bool transGtMinLimit = (transInfoMask & 0x04) == 0x04;
                        bool prePayModeUsed = (transInfoMask & 0x08) == 0x08;
                        bool volIncluded = (transInfoMask & 0x10) == 0x10;
                        bool finalTransNotAllowed = (transInfoMask & 0x20) == 0x20;
                        bool moneyNegative = (transInfoMask & 0x40) == 0x40;
                        bool moneyIncluded = (transInfoMask & 0x80) == 0x80;
                        if (nextBytes.Length < 5 + (moneyIncluded ? 3 : 0) + (volIncluded ? 3 : 0))
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        uint moneyDue = moneyIncluded ? UnBcd(nextBytes.Skip(5).Take(3)) : 0;
                        uint volume = volIncluded ? UnBcd(nextBytes.Skip(5 + (moneyIncluded ? 3 : 0)).Take(3)) : 0;
                        nextBytes = nextBytes.Skip(5 + (moneyIncluded ? 3 : 0) + (volIncluded ? 3 : 0)).ToArray();

                        DoDeferredLogging(LogLevel.Info, "FpId", () => new[] {
                            $"{fpId}; Trans: {transNum}; " +
                            $"SeqNum: {transSeqNo}; SMId: {smId}; LockId: {transLockId}; InfoMask: 0x{transInfoMask:X2} [" +
                            $"{BooleanToStringOrDefault(storedTrans, "StoredTrans")}{BooleanToStringOrDefault(errorTrans, "ErrorTrans")}" +
                            $"{BooleanToStringOrDefault(transGtMinLimit, "Trans > MinLimit")}{BooleanToStringOrDefault(prePayModeUsed, "Prepay Mode Used")}" +
                            $"{BooleanToStringOrDefault(finalTransNotAllowed, "Finalise Transaction Not Allowed")}{BooleanToStringOrDefault(moneyNegative, "Money Due is Negative")}" +
                            $"{BooleanToStringOrDefault(volIncluded, "Volume Included")}{BooleanToStringOrDefault(moneyIncluded, "Money Due Included")}" +
                            $" ]; {(BooleanToStringOrDefault(volIncluded, $"Volume: {volume}"))}{(BooleanToStringOrDefault(moneyIncluded, $"Cash: {moneyDue}"))}"
                        }, methodName: methodName);

                        if (_updateSetup)
                        {
                            pump.AddTransaction(new DomsSetupTransaction(transNum, transSeqNo, smId, transLockId, transInfoMask, moneyDue, volume, 0));
                        }

                        ResetPumpStateOnXxxTransBufStatus(pump);
                    }
                    else
                    {
                        if (nextBytes.Length < 16)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        int transSeqNo = (int)UnBcd(nextBytes.Take(2));
                        byte smId = UnBcd1(nextBytes[2]);
                        byte transLockId = UnBcd1(nextBytes[3]);
                        byte transInfoFlags = nextBytes[4];
                        bool storedTrans = (transInfoFlags & 0x01) == 0x01;
                        bool errorTrans = (transInfoFlags & 0x02) == 0x02;
                        bool transGtMinLimit = (transInfoFlags & 0x04) == 0x04;
                        bool prePayModeUsed = (transInfoFlags & 0x08) == 0x08;
                        bool finalTransNotAllowed = (transInfoFlags & 0x20) == 0x20;
                        bool moneyNegative = (transInfoFlags & 0x40) == 0x40;
                        uint moneyDue = UnBcd(nextBytes.Skip(5).Take(5));
                        uint volume = UnBcd(nextBytes.Skip(10).Take(5));
                        byte gradeId = UnBcd1(nextBytes[15]);
                        nextBytes = nextBytes.Skip(16).ToArray();

                        DoDeferredLogging(LogLevel.Info, "FpId", () => new[] {
                            $"{fpId}; Trans: {transNum}; " +
                            $"SeqNum: {transSeqNo}; SMId: {smId}; LockId: {transLockId}; InfoMask: 0x{transInfoFlags:X2} [" +
                            $"{BooleanToStringOrDefault(storedTrans, "StoredTrans")}{BooleanToStringOrDefault(errorTrans, "ErrorTrans")}" +
                            $"{BooleanToStringOrDefault(transGtMinLimit, "Trans > MinLimit")}{BooleanToStringOrDefault(prePayModeUsed, "Prepay Mode Used")}" +
                            $"{BooleanToStringOrDefault(finalTransNotAllowed, "Finalise Transaction Not Allowed")}{BooleanToStringOrDefault(moneyNegative, "Money Due is Negative")}" +
                            //$"{BooleanToStringOrDefault(volIncluded, "Volume Included")}{BooleanToStringOrDefault(moneyIncluded, "Money Due Included")}" +
                            $" ]; Volume: {volume}; Cash: {moneyDue}; Grade: {gradeId}"
                        }, methodName: methodName);

                        if (_updateSetup)
                        {
                            pump.AddTransaction(new DomsSetupTransaction(transNum, transSeqNo, smId, transLockId, transInfoFlags, moneyDue, volume, gradeId));

                            if (pump.GradeId != gradeId)
                            {
                                var info = string.Empty;
                                var pumpGradeId = pump.GradeId;
                                if (pump.GradeId == 0)
                                {
                                    info = " (overriding Pump.GradeId)";
                                    pump.SetFcGradeInfo(gradeId, pump.HoseId, pump.GradeOption);
                                }

                                DoDeferredLogging(LogLevel.Warn, "FpId", () => new[] { $"{fpId}; Grade Mismatch detected: Pump/Msg: {pumpGradeId}/{gradeId}{info}" }, methodName: methodName);
                                _setup.ReadClaimTransactionFpSup(fpId, transNum, transSeqNo, transLockId);
                            }
                        }

                        ResetPumpStateOnXxxTransBufStatus(pump);
                    }
                }

                return true;
            }, checkLength: subc == 0x00 ? 6 : 16);
        }

        private void ExtractFpUnSupTransBufStatus(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpActionWithChecks(DomsMessageCode.FpUnSupTransBufStatus, subc, data, (subc, data) => !(subc == 0x00 && data.Length > 1 || subc == 0x03 && data.Length > 1), out result, (fpId) =>
            {
                var methodName = "FpUnSupTransBuf.Status";
                byte noTrans = data[1];
                byte[] nextBytes = data.Skip(2).ToArray();
                var pump = Pump(fpId);

                if (_updateSetup)
                {
                    pump.ClearTransactionList();
                }

                if (noTrans == 0)
                {
                    return true;
                }

                for (byte i = 0; i < noTrans; i++)
                {
                    var transNum = (byte)(i + 1);

                    if (subc == 0x00)
                    {
                        if (nextBytes.Length < 5)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        int transSeqNo = (int)UnBcd(nextBytes.Take(2));
                        byte smId = UnBcd1(nextBytes[2]);
                        byte transLockId = UnBcd1(nextBytes[3]);
                        byte transInfoMask = nextBytes[4];
                        bool storedTrans = (transInfoMask & 0x01) == 0x01;
                        bool errorTrans = (transInfoMask & 0x02) == 0x02;
                        bool transGtMinLimit = (transInfoMask & 0x04) == 0x04;
                        bool prePayModeUsed = (transInfoMask & 0x08) == 0x08;
                        bool volIncluded = (transInfoMask & 0x10) == 0x10;
                        bool finalTransNotAllowed = (transInfoMask & 0x20) == 0x20;
                        bool moneyNegative = (transInfoMask & 0x40) == 0x40;
                        bool moneyIncluded = (transInfoMask & 0x80) == 0x80;
                        if (nextBytes.Length < 5 + (moneyIncluded ? 3 : 0) + (volIncluded ? 3 : 0))
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        uint moneyDue = moneyIncluded ? UnBcd(nextBytes.Skip(5).Take(3)) : 0;
                        uint volume = volIncluded ? UnBcd(nextBytes.Skip(5 + (moneyIncluded ? 3 : 0)).Take(3)) : 0;
                        nextBytes = nextBytes.Skip(5 + (moneyIncluded ? 3 : 0) + (volIncluded ? 3 : 0)).ToArray();

                        DoDeferredLogging(LogLevel.Info, "FpId", () => new[] { $"{fpId}; Trans: {transNum}; " +
                            $"SeqNum: {transSeqNo}; SMId: {smId}; LockId: {transLockId}; InfoMask: 0x{transInfoMask:X2} [" +
                            $"{BooleanToStringOrDefault(storedTrans, "StoredTrans")}{BooleanToStringOrDefault(errorTrans, "ErrorTrans")}" +
                            $"{BooleanToStringOrDefault(transGtMinLimit, "Trans > MinLimit")}{BooleanToStringOrDefault(prePayModeUsed, "Prepay Mode Used")}" +
                            $"{BooleanToStringOrDefault(finalTransNotAllowed, "Finalise Transaction Not Allowed")}{BooleanToStringOrDefault(moneyNegative, "Money Due is Negative")}" +
                            $"{BooleanToStringOrDefault(volIncluded, "Volume Included")}{BooleanToStringOrDefault(moneyIncluded, "Money Due Included")}" +
                            $" ]; {(BooleanToStringOrDefault(volIncluded, $"Volume: {volume}"))}{(BooleanToStringOrDefault(moneyIncluded, $"Cash: {moneyDue}"))}"
                        }, methodName: methodName);

                        if (_updateSetup)
                        {
                            pump.AddCashedOutState(transSeqNo, TransactionCashedOutState.TransactionReceived);
                            var states = pump.GetCashedOutStates(transSeqNo);
                            if (states.Any(x=> x.Key != TransactionCashedOutState.TransactionReceived))
                            {
                                DoDeferredLogging(LogLevel.Warn, "CashedOutState.FpId", () => new[] { $"{fpId}; SeqNum: {transSeqNo}; json: {JsonConvert.SerializeObject(states)}" }, methodName: methodName);
                            }

                            if (states.ContainsKey(TransactionCashedOutState.Started) && states.ContainsKey(TransactionCashedOutState.PaymentAcknowledged))
                            {
                                _setup.AutoClearTransaction(fpId, transNum, transSeqNo, smId, volume, moneyDue, transLockId);
                            }
                            else
                            {
                                pump.AddTransaction(new DomsSetupTransaction(transNum, transSeqNo, smId, transLockId, transInfoMask, moneyDue, volume, 0));
                            }
                        }

                        ResetPumpStateOnXxxTransBufStatus(pump);
                    }
                    else
                    {
                        if (nextBytes.Length < 16)
                        {
                            LogInvalidMessageFormat();
                            return false;
                        }

                        int transSeqNo = (int)UnBcd(nextBytes.Take(2));
                        byte smId = UnBcd1(nextBytes[2]);
                        byte transLockId = UnBcd1(nextBytes[3]);
                        byte transInfoFlags = nextBytes[4];
                        bool storedTrans = (transInfoFlags & 0x01) == 0x01;
                        bool errorTrans = (transInfoFlags & 0x02) == 0x02;
                        bool transGtMinLimit = (transInfoFlags & 0x04) == 0x04;
                        bool prePayModeUsed = (transInfoFlags & 0x08) == 0x08;
                        bool finalTransNotAllowed = (transInfoFlags & 0x20) == 0x20;
                        bool moneyNegative = (transInfoFlags & 0x40) == 0x40;
                        uint moneyDue = UnBcd(nextBytes.Skip(5).Take(5));
                        uint volume = UnBcd(nextBytes.Skip(10).Take(5));
                        byte gradeId = UnBcd1(nextBytes[15]);

                        nextBytes = nextBytes.Skip(16).ToArray();

                        DoDeferredLogging(LogLevel.Info, "FpId", () => new[] { $"{fpId}; Trans: {transNum}; " +
                            $"SeqNum: {transSeqNo}; SMId: {smId}; LockId: {transLockId}; InfoMask: 0x{transInfoFlags:X2} [" +
                            $"{BooleanToStringOrDefault(storedTrans, "StoredTrans")}{BooleanToStringOrDefault(errorTrans, "ErrorTrans")}" +
                            $"{BooleanToStringOrDefault(transGtMinLimit, "Trans > MinLimit")}{BooleanToStringOrDefault(prePayModeUsed, "Prepay Mode Used")}" +
                            $"{BooleanToStringOrDefault(finalTransNotAllowed, "Finalise Transaction Not Allowed")}{BooleanToStringOrDefault(moneyNegative, "Money Due is Negative")}" +
                            //$"{BooleanToStringOrDefault(volIncluded, "Volume Included")}{BooleanToStringOrDefault(moneyIncluded, "Money Due Included")}" +
                            $" ]; Volume: {volume}; Cash: {moneyDue}; Grade: {gradeId}"
                        }, methodName: methodName);

                        if (_updateSetup)
                        {
                            pump.AddCashedOutState(transSeqNo, TransactionCashedOutState.TransactionReceived);
                            var states = pump.GetCashedOutStates(transSeqNo);
                            if (states.Any(x => x.Key != TransactionCashedOutState.TransactionReceived))
                            {
                                DoDeferredLogging(LogLevel.Warn, "CashedOutState.FpId", () => new[] { $"{fpId}; SeqNum: {transSeqNo}; json: {JsonConvert.SerializeObject(states)}" }, methodName: methodName);
                            }

                            if (states.ContainsKey(TransactionCashedOutState.Started) && states.ContainsKey(TransactionCashedOutState.PaymentAcknowledged))
                            {
                                _setup.AutoClearTransaction(fpId, transNum, transSeqNo, smId, volume, moneyDue, transLockId);
                            }
                            else
                            {
                                pump.AddTransaction(new DomsSetupTransaction(transNum, transSeqNo, smId, transLockId, transInfoFlags, moneyDue, volume, gradeId));

                                if (pump.GradeId != gradeId)
                                {
                                    var info = string.Empty;
                                    var pumpGradeId = pump.GradeId;
                                    if (pump.GradeId == 0)
                                    {
                                        info = " (overriding Pump.GradeId)";
                                        pump.SetFcGradeInfo(gradeId, pump.HoseId, pump.GradeOption);
                                    }

                                    DoDeferredLogging(LogLevel.Warn, "FpId", () => new[] { $"{fpId}; Grade Mismatch detected: Pump/Msg: {pumpGradeId}/{gradeId}{info}" }, methodName: methodName);
                                    _setup.ReadClaimTransactionFpUnSup(fpId, transNum, transSeqNo, transLockId);
                                }
                            }
                        }

                        ResetPumpStateOnXxxTransBufStatus(pump);
                    }
                }

                return true;
            }, checkLength: subc == 0x00 ? 6 : 16);
        }

        private void ExtractFpInfo(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpAction(DomsMessageCode.FpInfo, subc, data, (subc, data) => subc != 0x01 || data.Length < 2, out result, (fpId) =>
            {
                var methodName = "FpInfo";
                byte noInfoItems = data[1];
                byte[] nextBytes = data.Skip(2).ToArray();
                var logMessages = Enumerable.Empty<string>().ToList();
                for (int i = 0; i < noInfoItems; i++)
                {
                    if (nextBytes.Length < 3)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    byte infoId = UnBcd1(nextBytes[0]);
                    int infoLength = FromTwoByte(nextBytes.Skip(1).Take(2).ToArray());
                    if (nextBytes.Length < 3 + infoLength)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    var infoBytes = nextBytes.Skip(3).Take(infoLength).ToArray();

                    logMessages.Add($"{fpId}; Item/InfoId: {i}/0x{infoId:x2}; Length: {infoLength}; {(infoId == 0x01 ? "Trans Return Data" : infoId == 0x02 ? "[Grade/Price]" : infoId == 0x03 ? "Trans Return Data 2" : "0x{infoId:X2}")}:");

                    if (infoId == 0x02)
                    {
                        byte noGrades = infoBytes[0];
                        if (infoLength == 1 + 3 * noGrades)
                        {
                            logMessages.Add(" [");
                            for (int j = 0; j < noGrades; j++)
                            {
                                byte gradeId = UnBcd1(infoBytes[1 + 3 * j]);
                                uint price = UnBcd(infoBytes.Skip(1 + 3 * j + 1).Take(2).ToArray());
                                logMessages.Add($" {gradeId}/{price};");
                                if (_updateSetup)
                                {
                                    if (gradeId > 0 && price > 0)
                                    {
                                        _setup.SetPumpGradePrice(fpId, gradeId, price);
                                    }
                                    else
                                    {
                                        DoDeferredLogging(LogLevel.Warn, "FpId", () => new[] { string.Join(string.Empty, logMessages.Concat(new[] { " - Zero GradeId or Price detected! ]" })) }, methodName: methodName);
                                    }
                                }
                            }
                            logMessages.Add("]");
                        }
                    }

                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "FpId", () => new[] { string.Join(string.Empty, logMessages) }, methodName: methodName);

                    nextBytes = nextBytes.Skip(3 + infoLength).ToArray();
                }
            });
        }

        private void ExtractFpPumpGradeTotals(byte subc, byte[] data, out (bool, byte) result)
        {
            ExecuteFpActionWithChecks(DomsMessageCode.FpInfo, subc, data, (subc, data) => subc != 0x01 || data.Length < 2, out result, (fpId) =>
            {
                var methodName = "OnMeterReadings"; // "FpPumpGradeTotals";

                var msgLength = 14;
                var pump = Pump(fpId);
                var grandVolTotal = UnBcd(data.Skip(1).Take(6));
                var grandMoneyTotal = UnBcd(data.Skip(7).Take(6));
                var noGradeOption = data[13];
                var nextBytes = data.Skip(14).ToArray();

                var meter = new MeterReadings() { Number = fpId };

                for (var i = 0; i < noGradeOption; i++)
                {
                    if (nextBytes.Length < msgLength)
                    {
                        LogInvalidMessageFormat();
                        return false;
                    }

                    var gradeOptionNo = UnBcd1(nextBytes[0]);
                    var gradeId = UnBcd1(nextBytes[1]);
                    var gradeVolTotal = UnBcd(nextBytes.Skip(2).Take(6));
                    var gradeAmountTotal = UnBcd(nextBytes.Skip(8).Take(6));

                    var hose = DomsSetup.ToHose(pump, _setup, gradeOptionNo, gradeId);
                    var totals = new MeterHoseTotals()
                    {
                        Number = hose.Number,
                        Read = true,
                        Hose = hose, 
                        Price = hose.Grade.Price,
                        Volume = Convert.ToDecimal(gradeVolTotal),
                        Amount = Convert.ToDecimal(gradeAmountTotal),
                    };
                    totals.Volume = Math.Round(Convert.ToDecimal(totals.Volume / 100), 2);
                    totals.Amount = Math.Round(Convert.ToDecimal(totals.Amount / 100), 2);

                    meter.HoseTotals[hose.Number] = totals;
                    nextBytes = nextBytes.Skip(msgLength).ToArray();
                }

                // Deal with odd number of hoses - must be atleast a 4-hose model
                var mht = (byte)1;
                while (meter.HoseTotals.Count < 4)
                {
                    var number = mht++;
                    if (meter.HoseTotals.ContainsKey(number))
                    {
                        continue;
                    }

                    var hose = DomsSetup.ToHose(pump, _setup, 0, 0);
                    meter.HoseTotals[number] = new MeterHoseTotals()
                    {
                        Number = number,
                        Read = false,
                        Hose = hose
                    };
                }

                DoDeferredLogging(LogLevel.Info, "Pump", () => new string[1] { meter.CreateOnMeterReadingsParamText() }, null, null, methodName);

                if (_updateSetup)
                {
                    Pump(fpId).SetMeterStatus(meter);
                    _setup.SetMeterStatus(meter, LoggingReference);
                }

                return false;
            });
        }

        private void ExtractFcStatus(byte subc, byte[] data)
        {
            if (!(subc == 0x00 && data.Length == 19 || subc == 0x02 && data.Length > 19))
            {
                LogInvalidMessageFormat();
                return;
            }

            var methodName = "FcStatus";
            byte fcStatus1Flags = data[0];
            byte fcStatus2Flags = data[1];
            byte serviceMessageServiceSeqNo = UnBcd1(data[2]);
            int masterResetYear = (int) UnBcd(data.Skip(3).Take(2));
            int masterResetMonth = UnBcd1(data[5]);
            int masterResetDay = UnBcd1(data[6]);
            int masterResetHour = UnBcd1(data[7]);
            int masterResetMinute = UnBcd1(data[8]);
            int masterResetSecond = UnBcd1(data[9]);
            var masterResetTime = new DateTime(masterResetYear, masterResetMonth, masterResetDay, masterResetHour, masterResetMinute,
                masterResetSecond);
            byte masterResetCode = data[10];
            int resetYear = (int) UnBcd(data.Skip(11).Take(2));
            int resetMonth = UnBcd1(data[13]);
            int resetDay = UnBcd1(data[14]);
            int resetHour = UnBcd1(data[15]);
            int resetMinute = UnBcd1(data[16]);
            int resetSecond = UnBcd1(data[17]);
            var resetTime = new DateTime(resetYear, resetMonth, resetDay, resetHour, resetMinute, resetSecond);
            byte resetCode = data[18];
            var info = $"Flags 1/2: {fcStatus1Flags}/{fcStatus2Flags}; ServiceMessage SeqNo: {serviceMessageServiceSeqNo}; " +
                         $"Time/Code - MasterReset: {FormatDateTime(masterResetTime)}/{masterResetCode}; " +
                         $"Reset: {FormatDateTime(resetTime)}/{resetCode}";
            if (subc == 0x02)
            {
                byte noFcStatusPars = data[19];
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Params.Count", () => new[] { $"{noFcStatusPars}; {info}" }, methodName: methodName);
                byte[] nextBytes = data.Skip(20).ToArray();
                for (int i = 0; i < noFcStatusPars; i++)
                {
                    if (nextBytes.Length < 2)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    byte parId = UnBcd1(nextBytes[0]);
                    byte parLen = nextBytes[1];
                    if (nextBytes.Length < 2 + parLen)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    var parData = nextBytes.Skip(2).Take(parLen).ToArray();

                    nextBytes = nextBytes.Skip(2 + parLen).ToArray();
                }
            }
        }

        private void ExtractTgStatus(byte subc, byte[] data)
        {
            if (!(subc == 0x00 && data.Length == 5 || subc == 0x01 && data.Length > 5))
            {
                LogInvalidMessageFormat();
                return;
            }

            byte tgId = UnBcd1(data[0]);
            byte tgMainState = data[1];
            byte tgSubStates = data[2];
            int tgAlarmStatus = FromTwoByte(data.Skip(3).Take(2).ToArray());
            GetLogger().Info("DOMS - TG Status Response," + $" Tank Gauge ID is {tgId}," + $" Main State is {tgMainState}," +
                         $" Sub States is is {tgSubStates}," + $" Alarm status is {tgAlarmStatus}");
            if (subc == 0x01)
            {
                byte noTgAlarmTexts = data[5];
                GetLogger().Info("DOMS - TG Status Response," + $" Number of TG Alarm Texts is {noTgAlarmTexts}");
                byte[] nextBytes = data.Skip(6).ToArray();
                for (int i = 0; i < noTgAlarmTexts; i++)
                {
                    if (nextBytes.Length < 4)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    int tgAlarmNo = FromTwoByte(nextBytes.Take(2).ToArray());
                    byte tgProtocolId = UnBcd1(nextBytes[2]);
                    byte tgAlarmTextLength = nextBytes[3];
                    if (nextBytes.Length < 4 + tgAlarmTextLength)
                    {
                        LogInvalidMessageFormat();
                        return;
                    }

                    string tgAlarmText = AsciiString(nextBytes.Skip(4).Take(tgAlarmTextLength).ToArray());
                    GetLogger().Info("DOMS - TG Status Response," +
                                 $" TG Alarm Texts {i} Number is {tgAlarmNo}, Prorotcol is {tgProtocolId}, Length is {tgAlarmTextLength}, Text is {tgAlarmText}");

                    nextBytes = nextBytes.Skip(4 + tgAlarmTextLength).ToArray();
                }
            }
        }

        private void ExtractPssPeripheralsStatus(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length < 1)
            {
                LogInvalidMessageFormat();
                return;
            }

            byte noPeripherals = data[0];
            GetLogger().Info("DOMS - PSS Peripherals Status Response," + $" Number of Peripherals is {noPeripherals}");
            if (data.Length != 1 + 9 * noPeripherals)
            {
                LogInvalidMessageFormat();
                return;
            }

            for (int i = 0; i < noPeripherals; i++)
            {
                byte peripheralType = data[1 + 9 * i];
                byte connType = data[1 + 9 * i + 1];
                string address;
                if (connType == 1 && data[1 + 9 * i + 2] == 0 && data[1 + 9 * i + 3] == 0 && data[1 + 9 * i + 4] == 0)
                {
                    address = $"Serial Address {data[1 + 9 * i + 5]}";
                }
                else if (connType == 2)
                {
                    address = $"IP Address {data[1 + 9 * i + 5]}.{data[1 + 9 * i + 4]}.{data[1 + 9 * i + 3]}.{data[1 + 9 * i + 2]}";
                }
                else
                {
                    address =
                        $"Unknown Address 0x{data[1 + 9 * i + 2]:X2} 0x{data[1 + 9 * i + 3]:X2} 0x{data[1 + 9 * i + 4]:X2} 0x{data[1 + 9 * i + 5]:X2}";
                }

                int portNumber = FromTwoByte(data.Skip(1 + 9 * i + 6).Take(2).ToArray());
                byte peripheralStatus = data[1 + 9 * i + 8];
                GetLogger().Info("DOMS - PSS Peripherals Status Response," + $" Peripheral {i} Type is {peripheralType}" +
                             $" Connection Type is {connType}," + $" Connection Address is {address}," +
                             $" Server Port Number is {portNumber}," + $" Peripheral Status is {peripheralStatus}");
            }
        }

        private void ExtractPosConnectionStatus(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length < 1)
            {
                LogInvalidMessageFormat();
                return;
            }

            byte noConnections = data[0];
            GetLogger().Info("DOMS - POS Connection Status Response," + $" Number of Connections is {noConnections}");
            if (data.Length != 1 + 9 * noConnections)
            {
                LogInvalidMessageFormat();
                return;
            }

            for (int i = 0; i < noConnections; i++)
            {
                byte deviceType = data[1 + 9 * i];
                byte connType = data[1 + 9 * i + 1];
                string address;
                if (connType == 1 && data[1 + 9 * i + 2] == 0 && data[1 + 9 * i + 3] == 0 && data[1 + 9 * i + 4] == 0)
                {
                    address = $"Serial Address {data[1 + 9 * i + 5]}";
                }
                else if (connType == 2)
                {
                    address = $"IP Address {data[1 + 9 * i + 5]}.{data[1 + 9 * i + 4]}.{data[1 + 9 * i + 3]}.{data[1 + 9 * i + 2]}";
                }
                else
                {
                    address =
                        $"Unknown Address 0x{data[1 + 9 * i + 2]:X2} 0x{data[1 + 9 * i + 3]:X2} 0x{data[1 + 9 * i + 4]:X2} 0x{data[1 + 9 * i + 5]:X2}";
                }

                int portNumber = FromTwoByte(data.Skip(1 + 9 * i + 6).Take(2).ToArray());
                byte connStatus = data[1 + 9 * i + 8];
                GetLogger().Info("DOMS - POS Connection Status Response," + $" Device {i} Type is {deviceType}" +
                             $" Connection Type is {connType}," + $" Connection Address is {address}," +
                             $" Server Port Number is {portNumber}," + $" Connection Status is {connStatus}");
            }
        }

        private void ExtractBnaExchangeRateStatus(byte subc, byte[] data)
        {
            if (subc != 0x00 || data.Length < 8)
            {
                LogInvalidMessageFormat();
                return;
            }

            int settingYear = (int) UnBcd(data.Take(2));
            int settingMonth = UnBcd1(data[2]);
            int settingDay = UnBcd1(data[3]);
            int settingHour = UnBcd1(data[4]);
            int settingMinute = UnBcd1(data[5]);
            int settingSecond = UnBcd1(data[6]);
            var settingTime = new DateTime(settingYear, settingMonth, settingDay, settingHour, settingMinute, settingSecond);
            int noBnaForeignCurrencies = data[7];
            GetLogger().Info("DOMS - BNA Exchange Rate Status Response," + $" Setting Timestamp is {settingTime:F}," +
                         $" Number of Foreign Currencies is {noBnaForeignCurrencies}");
            if (data.Length != 8 + 5 * noBnaForeignCurrencies)
            {
                LogInvalidMessageFormat();
                return;
            }

            for (int i = 0; i < noBnaForeignCurrencies; i++)
            {
                uint currencyCode = UnBcd(data.Skip(8 + 5 * i).Take(2));
                uint exchangeRate = UnBcd(data.Skip(8 + 5 * i + 2).Take(3));
                GetLogger().Info("DOMS - BNA Exchange Rate Status Response," + $" Currency {i} Code is {currencyCode:D4}," +
                             $" Exchange Rate is {exchangeRate:D6}");
            }
        }

        private void ExtractSiteDeliveryStatus(byte subc, byte[] data)
        {
            if (!(subc == 0x00 && data.Length > 2 || subc == 0x01 && data.Length > 2))
            {
                LogInvalidMessageFormat();
                return;
            }

            byte deliveryStatusFlags = data[0];
            byte deliveryReportSequenceNumber = UnBcd1(data[1]);
            byte noTankDeliveries = data[2];
            if (data.Length < 3 + noTankDeliveries)
            {
                LogInvalidMessageFormat();
                return;
            }

            GetLogger().Info("DOMS - Site Delivery Status Response," + $" Delivery Status Flags is {deliveryStatusFlags}," +
                         $" Delivery Report Sequence Number is {deliveryReportSequenceNumber}," +
                         $" Number of Tank Deliveries is {noTankDeliveries}");
            for (int i = 0; i < noTankDeliveries; i++)
            {
                byte tgId = UnBcd1(data[3 + i]);
                GetLogger().Info("DOMS - Site Delivery Status Response," + $" TG ID {i} is {tgId}");
            }

            if (subc == 0x01)
            {
                if (data.Length < 3 + noTankDeliveries + 1)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                byte noTicketedDeliveries = data[3 + noTankDeliveries];
                GetLogger().Info("DOMS - Site Delivery Status Response," + $" Number of Ticketed Deliveries is {noTankDeliveries}");
                for (int i = 0; i < noTicketedDeliveries; i++)
                {
                    byte tgId = UnBcd1(data[3 + noTankDeliveries + 1 + i]);
                    GetLogger().Info("DOMS - Site Delivery Status Response," + $" TG ID {i} is {tgId}");
                }
            }
        }

        private Result<(DomsMessageCode, byte)> ExtractMultiMessageMessageCodes(byte subcMulti, byte[] data)
        {
            return Result.SuccessIf(!(subcMulti != 0x01 || data.Length < 3 || data[0] == 0x00 && data.Length < 5),
                (
                    (DomsMessageCode)(data[0] == 0x00 ? FromTwoByte(data.Skip(1).Take(2).ToArray()) - 0x8000 : data[0] - 0x80),
                    data[0] == 0x00 ? data[3] : data[1]
                ),
                "Invalid DomsMessageCode.MultiMessage");
        }

        private void ExtractMultiMessage(byte apc, byte subcMulti, byte[] data, byte[] bytes, out List<(bool, byte, (DomsMessageCode, byte))> results)
        {
            results = new List<(bool, byte, (DomsMessageCode, byte))>();

            var result = ExtractMultiMessageMessageCodes(subcMulti, data);
            if (!result.IsSuccess)
            {
                LogInvalidMessageFormat();
                return;
            }

            var (msgCode, subc) = result.Value;
            byte numMsgs = data[0] == 0x00 ? data[4] : data[2];
            var logMsg = LoggingEnabled(msgCode, subc);

            DoDeferredLogging(logMsg ? ToLogLevel(DeveloperLoggingLevelState.GetValue()) : LogLevel.None, "APC", () => new[] { $"{apc}; Code/SubCode: {msgCode}/{subc}; Count: {numMsgs}" });
            byte[] nextBytes = data.Skip(data[0] == 0x00 ? 5 : 3).ToArray();

            for (int i = 0; i < numMsgs; i++)
            {
                if (nextBytes.Length < 1)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                byte msgLength = nextBytes[0];
                if (nextBytes.Length < 1 + msgLength)
                {
                    LogInvalidMessageFormat();
                    return;
                }

                var msgData = nextBytes.Skip(1).Take(msgLength).ToArray();

                var result2 = ExtractMessage(apc, msgCode, subc, msgData, bytes);
                if (result2.IsSuccess)
                {
                    results.AddRange(result2.Value);
                }
                nextBytes = nextBytes.Skip(1 + msgLength).ToArray();
            }
        }

        private void ExtractRejectMessage(byte apc, byte subcMulti, byte[] data)
        {
            if (subcMulti != 0x01 || data.Length < 3 || data[0] == 0x00 && data.Length < 5)
            {
                LogInvalidMessageFormat();
                return;
            }

            DomsMessageCode msgCode = (DomsMessageCode)(data[0] == 0x00 ? FromTwoByte(data.Skip(1).Take(2).ToArray()) - 0x8000 :
                data[0] == 0xFF ? FromTwoByte(data.Skip(1).Take(2).ToArray()) :
                data[0] < 0x80 ? data[0] : data[0] - 0x80);
            byte subc = data[0] == 0x00 || data[0] == 0xFF ? data[3] : data[1];
            byte rejectCode = data[0] == 0x00 || data[0] == 0xFF ? data[4] : data[2];
            bool hasRejectInfo = data.Length == (data[0] == 0x00 || data[0] == 0xFF ? 6 : 3);
            byte rejectInfo = (byte)(hasRejectInfo ? data[data[0] == 0x00 || data[0] == 0xFF ? 5 : 3] : 0);

            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "RejectMessage", () => new[] {
                $"APC: {apc}; Code/SubCode | RejectCode/Info: {msgCode}/{subc} | ",
                $"{(rejectCode == 0x01 ? "Unknown Message Code" : rejectCode == 0x02 ? "Syntax Error" : rejectCode == 0x03 ? "Access Error" : $"{rejectCode}")}",
                $"/{rejectInfo}"
            });
        }

        private Result<IEnumerable<(bool, byte, (DomsMessageCode, byte))>> ExtractMessage(byte apc, DomsMessageCode msgCode, byte subc, byte[] data, byte[] bytes)
        {
            //DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "APC", () => new[] { $"{apc}; Code/SubCode: {msgCode}/{subc}" });

            var result = (false, (byte)0);
            List<(bool, byte, (DomsMessageCode, byte))> resultMultiple = null;

            MessageReject = false;
            switch (msgCode)
            {
                case DomsMessageCode.FcPriceSet:
                    ExtractFcPriceSet(subc, data);
                    break;
                case DomsMessageCode.FcParameterSet:
                    ExtractFcParameterSet(subc, data);
                    break;
                case DomsMessageCode.FcInstallStatus:
                    ExtractFcInstallStatus(subc, data);
                    break;
                case DomsMessageCode.FcPriceSetStatus:
                    ExtractFcPriceSetStatus(subc, data);
                    break;
                case DomsMessageCode.OperationModeStatus:
                    ExtractOperationModeStatus(subc, data);
                    break;
                case DomsMessageCode.FpInstallData:
                    ExtractFpInstallData(subc, data, out result);
                    break;
                case DomsMessageCode.ChangeFcPriceSet:
                    ExtractChangeFcPriceSet(subc, data);
                    break;
                case DomsMessageCode.ChangeFcParameters:
                    ExtractChangeFcParameters(subc, data);
                    break;
                case DomsMessageCode.ClearInstallData:
                    ExtractClearInstallData(subc, data);
                    break;
                case DomsMessageCode.InstallFp:
                    ExtractInstallFp(subc, data, out result);
                    break;
                case DomsMessageCode.OpenFp:
                    ExtractOpenFp(subc, data, out result);
                    break;
                case DomsMessageCode.CloseFp:
                    ExtractCloseFp(subc, data, out result);
                    break;
                case DomsMessageCode.FpStatus:
                    ExtractFpStatus(subc, data, out result);
                    break;
                case DomsMessageCode.FcLogon:
                    ExtractFcLogon(apc, subc, data);
                    break;
                case DomsMessageCode.FpFuellingData:
                    ExtractFpFuellingData(subc, data, out result);
                    break;
                case DomsMessageCode.ReserveFp:
                    ExtractReserveFp(subc, data, out result);
                    break;
                case DomsMessageCode.AuthoriseFp:
                    ExtractAuthoriseFp(subc, data, out result);
                    break;
                case DomsMessageCode.CancelFpAuth:
                    ExtractCancelFpAuth(subc, data, out result);
                    break;
                case DomsMessageCode.FpSupTrans:
                    ExtractFpSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.UnlockFpSupTrans:
                    ExtractUnlockFpSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.ClearFpSupTrans:
                    ExtractClearFpSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.FpUnSupTrans:
                    ExtractFpUnSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.UnlockFpUnSupTrans:
                    ExtractUnlockFpUnSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.ClearFpUnSupTrans:
                    ExtractClearFpUnSupTrans(subc, data, out result);
                    break;
                case DomsMessageCode.EstopFp:
                    ExtractEstopFp(subc, data, out result);
                    break;
                case DomsMessageCode.CancelFpEstop:
                    ExtractCancelFpEstop(subc, data, out result);
                    break;
                case DomsMessageCode.FpSupTransBufStatus:
                    ExtractFpSupTransBufStatus(subc, data, out result);
                    break;
                case DomsMessageCode.FpUnSupTransBufStatus:
                    ExtractFpUnSupTransBufStatus(subc, data, out result);
                    break;
                case DomsMessageCode.FpInfo:
                    ExtractFpInfo(subc, data, out result);
                    break;
                case DomsMessageCode.FpPumpGradeTotals:
                    ExtractFpPumpGradeTotals(subc, data, out result);
                    break;
                case DomsMessageCode.FcStatus:
                    ExtractFcStatus(subc, data);
                    break;
                case DomsMessageCode.TgStatus:
                    ExtractTgStatus(subc, data);
                    break;
                case DomsMessageCode.SiteDeliveryStatus:
                    ExtractSiteDeliveryStatus(subc, data);
                    break;
                case DomsMessageCode.PssPeripheralsStatus:
                    ExtractPssPeripheralsStatus(subc, data);
                    break;
                case DomsMessageCode.PosConnectionStatus:
                    ExtractPosConnectionStatus(subc, data);
                    break;
                case DomsMessageCode.BnaExchangeRateStatus:
                    ExtractBnaExchangeRateStatus(subc, data);
                    break;
                case DomsMessageCode.MultiMessage:
                    ExtractMultiMessage(apc, subc, data, bytes, out resultMultiple);
                    break;
                case DomsMessageCode.RejectMessage:
                    ExtractRejectMessage(apc, subc, data);
                    RejectedApc(apc);
                    MessageReject = true;
                    break;
                case DomsMessageCode.FpError:
                    ExtractFpError(subc, data, out result);
                    break;
                case DomsMessageCode.ClearFpError:
                    ExtractClearFpError(subc, data, out result);
                    break;
                default:
                    DoDeferredLogging(LogLevel.Warn, $"Unknown.RX (length={bytes.Length})", () => new[] {
                        $"APC: {apc}; Code/SubCode: {msgCode}/{subc}",
                        $"Data: {ToHexString(bytes)}" }, methodName: "ConvertBytesToMessages");
                    break;
            }

            return resultMultiple ?? new List<(bool, byte, (DomsMessageCode, byte))>() { (result.Item1, result.Item2, (msgCode, subc)) };
        }

        public Result<IEnumerable<(bool, byte, (DomsMessageCode, byte))>> ExtractResponse(byte[] bytes)
        {
            if (bytes.Length < 3 || bytes[1] == 0x00 && bytes.Length < 5)
            {
                return LogInvalidMessageFormat();
            }

            var methodName = "ConvertBytesToMessages";
            var apc = bytes[0];
            var msgCode = (DomsMessageCode) (bytes[1] == 0x00 ? FromTwoByte(bytes.Skip(2).Take(2).ToArray()) - 0x8000 : bytes[1] - 0x80);
            var subc = bytes[1] == 0x00 ? bytes[4] : bytes[2];
            var data = bytes.Skip(bytes[1] == 0x00 ? 5 : 3).ToArray();
            if (msgCode < 0)
            {
                return LogInvalidMessageFormat();
            }

            var logMsg = LoggingEnabled(msgCode, subc);
            if (msgCode == DomsMessageCode.MultiMessage)
            {
                var resultCodes = ExtractMultiMessageMessageCodes(subc, data);
                if (resultCodes.IsSuccess)
                {
                    var (msgCode1, subc1) = resultCodes.Value;
                    logMsg = LoggingEnabled(msgCode1, subc1);
                }
            }

            DoDeferredLogging(logMsg ? LogLevel.Info : LogLevel.None, $"RX (length={bytes.Length})", () => new[] { $"APC: {apc}; Code/SubCode: {msgCode}/{subc}; Message: {ToHexString(bytes)}" }, methodName: methodName);
            
            ReceivedApc(apc);

            var result = ExtractMessage(apc, msgCode, subc, data, bytes);
            return result;
        }

        private void ExecuteFpAction(DomsMessageCode msgCode, byte subc, byte[] data, Func<byte, byte[], bool> check, out (bool, byte) result, Action<byte> action = null, Func<string> logAction = null, [CallerMemberName] string methodName = null)
        {
            var resultFp = DoExecuteFpAction(msgCode, subc, data, check, out result, logAction, methodName: methodName);

            if (resultFp.IsSuccess)
            {
                var fpId = resultFp.Value;

                action?.Invoke(fpId);

                result = (false, fpId);
            }
        }

        private Result<byte> DoExecuteFpAction(DomsMessageCode msgCode, byte subc, byte[] data, Func<byte, byte[], bool> check, out (bool, byte) result, Func<string> logAction = null, int checkLength = -1, [CallerMemberName] string methodName = null)
        {
            result = (false, 0);
            var invalid = check?.Invoke(subc, data) ?? false;
            if (invalid)
            {
                LogInvalidMessageFormat();
                return Result.Failure<byte>("InvalidMessage (check)");
            }

            var fpId = UnBcd1(data[0]);

            if (!_lastPumpMessage.ContainsKey(fpId))
            {
                _lastPumpMessage[fpId] = new ConcurrentDictionary<DomsMessageCode, byte[]>();
            }

            if (checkLength > -1 && _lastPumpMessage.TryGetValue(fpId, out var lastMessage) && lastMessage != null)
            {
                if (lastMessage.TryGetValue(msgCode, out var lastData) && (lastData?.SequenceEqual(data.Take(checkLength - 1)) ?? false))
                {
                    return Result.Failure<byte>("DuplicateMessage");
                }
            }

            var extraLog = logAction?.Invoke() ?? null;
            if (extraLog != null)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "FpId", () => new[] { string.Join("; ", fpId, extraLog) }, methodName: methodName);
            }

            if (_lastPumpMessage.TryGetValue(fpId, out var lastMessage2))
            {
                lastMessage2[msgCode] = data.Take(checkLength - 1).ToArray();
            }

            return Result.Success(fpId);
        }

        private void ExecuteFpActionWithChecks(DomsMessageCode msgCode, byte subc, byte[] data, Func<byte, byte[], bool> check, out (bool, byte) result, Func<byte, bool> action = null, Func<string> logAction = null, int checkLength = -1, [CallerMemberName] string methodName = null)
        {
            var resultFp = DoExecuteFpAction(msgCode, subc, data, check, out result, logAction, checkLength, methodName);

            if (resultFp.IsSuccess)
            {
                var fpId = resultFp.Value;

                var fetchPumpState = action?.Invoke(fpId) ?? false;

                result = (fetchPumpState, fpId);
            }
        }
    }
}