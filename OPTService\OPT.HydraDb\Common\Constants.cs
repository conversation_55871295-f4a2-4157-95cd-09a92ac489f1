﻿namespace OPT.HydraDb.Common
{
    /// <summary>
    /// Project/namespace wide constants
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// Migration type for, any Create database type changes
        /// </summary>
        public const string MigrationTypeBootStrap = "BootStrap";

        /// <summary>
        /// Migration type for, any DDL/schema type changes
        /// </summary>
        public const string MigrationTypeSchema = "Schema";

        /// <summary>
        /// Migration type for, any DML/data type changes
        /// </summary>
        public const string MigrationTypeData = "Data";

        /// <summary>
        /// Migration type for, any programmatic/stored procedure/function/trigger type changes
        /// </summary>
        public const string MigrationTypeProgrammatic = "Code";

        /// <summary>
        /// EmbeddedResources Sql type for, any stored procedures
        /// </summary>
        public const string EmbededResourceSqlTypeStoredProcedures = "Stored_Procedures";

        /// <summary>
        /// EmbeddedResources Sql type for, any functions
        /// </summary>
        public const string EmbededResourceSqlTypeFunctions = "Functions";

        /// <summary>
        /// The constant to prefix to all Sql objects defined as EmbeddedResources
        /// </summary>
        public const string EmbeddedResourcePrefix = "OPT.HydraDb.Sql";

        /// <summary>
        /// The constant that denotes the root HydraDb path
        /// </summary>
        public const string RootHydraDbFolder = @"C:\HydraOPTService\DB";

        /// <summary>
        /// The constant that denotes the root HydraDb path
        /// </summary>
        public const string BackupHydraDbFolder = "Backup";

        /// <summary>
        /// Constant for the Sql file extension
        /// </summary>
        public const string FileExtensionSql = ".sql";

        /// <summary>
        /// Constant for the default Sql Server Schema name
        /// </summary>
        public const string SchemaNameDefault = "dbo.";

        /// <summary>
        /// Config key category prefix constant, for all HydraDb related config
        /// </summary>
        public const string ConfigKeyCategoryHydraDb = "HydraDb:";

        /// <summary>
        /// Config key for the HydraDb client settings
        /// </summary>
        public const string ConfigKeyHydraDbClientSettings = ConfigKeyCategoryHydraDb + "Client:Settings";

        /// <summary>
        /// Config key for the HydraDb migration type, i.e. Up or Down
        /// </summary>
        public const string ConfigKeyHydraDbMigrationType = ConfigKeyCategoryHydraDb + "Migration:Type";

        /// <summary>
        /// Default value for the Upgrade Db Migration Type
        /// </summary>
        public const string DefaultValueHydraDbMigrationTypeUpgrade = "Up";

        /// <summary>
        /// Default value for the Rollback Db Migration Type
        /// </summary>
        public const string DefaultValueHydraDbMigrationTypeRollback = "Down";

        /// <summary>
        /// Default value for Rollback version to Migrate Down to, will be the dbo.VersionInfo.Id from the final migration BEFORE the current release!
        /// </summary>
        public const int DefaultValueHydraDbMigrateDownToVersion = 28100001;
    }
}
