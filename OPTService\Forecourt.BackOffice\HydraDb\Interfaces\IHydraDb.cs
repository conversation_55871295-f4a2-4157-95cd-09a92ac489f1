﻿using CSharpFunctionalExtensions;
using Forecourt.Bos.HydraDb.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Bos.HydraDb.Interfaces
{

    /// <summary>
    /// Any and all defintions of IHydraDb needed by <PERSON><PERSON>, split down by area
    /// </summary>
    public interface IHydraDb :
        Core.HydraDb.Interfaces.IHydraDb,
        IHydraDbBos,
        IHydraDbBosTransactionBookings
    {
    }

    /// <summary>
    /// Any and all defintions of IHydraDb needed by Bos
    /// </summary>
    public interface IHydraDbBos
    {
        void SetRetalixTransactionFileDirectory(string directory);
        void SetTransactionFileDirectory(string directory);
    }

    /// <summary>
    /// Any and all capabilities of IHydraDb need by <PERSON><PERSON>, to book transactions 
    /// </summary>
    public interface IHydraDbBosTransactionBookings
    {
        /// <summary>
        /// Locate the transaction booking state, by any of the transaction ids.  At least one must be given.
        /// </summary>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="txnNumber">OPT transaction number</param>
        /// <param name="externalTransId">External transaction id</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result<TransactionBookingState> GetTransactionBooking(long? transId = null, string txnNumber = null, string externalTransId = null, IMessageTracking message = null);        

        /// <inheritdoc cref="GetTransactionBooking(long?, string, string, IMessageTracking)"/>
        Result<StatusCodeResult> GetTransactionBookingHttp(long? transId = null, string txnNumber = null, string externalTransId = null, IMessageTracking message = null);

        /// <summary>
        /// Get a list of outstanding transaction bookings, to book to the external system
        /// </summary>
        /// <param name="message">Current message</param>
        /// <returns>Result wrapped list of <see cref="TransactionBookingState"/></returns>
        Result<IEnumerable<TransactionBookingState>> GetPendingTransactionBookings(IMessageTracking message = null);

        /// <inheritdoc cref="GetPendingTransactionBookings(IMessageTracking)" />
        Result<StatusCodeResult> GetPendingTransactionBookingsHttp(IMessageTracking message = null);

        /// <summary>
        /// Update the booking state, with information relevant to the transaction
        /// </summary>
        /// <param name="id">Transaction booking id</param>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="item"><see cref="SendTransactionItem"/> instance, to serialise</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result UpdateTransactionBooking(long id, long transId, SendTransactionItem item, IMessageTracking message = null);

        /// <summary>
        /// Update the booking state, with information relevant to the transaction
        /// </summary>
        /// <param name="id">Transaction booking id</param>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="externalId">External transaction id</param>
        /// <param name="shiftId">Linked, Shift id</param>
        /// <param name="periodId">Linked, Period id</param>
        /// <param name="businessDate">Linked, Business date</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result UpdateTransactionBooking(long id, long transId, string externalId, int shiftId, int periodId, DateTime businessDate, IMessageTracking message = null);

        /// <inheritdoc cref="UpdateTransactionBooking(long, long, string, int, int, DateTime, IMessageTracking)" />
        Result<StatusCodeResult> UpdateTransactionBookingHttp(long id, long transId, string externalId, int shiftId, int periodId, DateTime businessDate, IMessageTracking message = null);

        /// <summary>
        /// pdate the booking state, when a booking attempt fails
        /// </summary>
        /// <param name="id">Transaction booking id</param>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="responseCode">HttpStatusCode</param>
        /// <param name="response">Response text</param>
        /// <param name="retryCount">Current retry count</param>
        /// <param name="nextRetryDate">Next retry time</param>
        /// <param name="message">Current message</param>
        /// <returns></returns>
        Result UpdateTransactionBooking(long id, long transId, HttpStatusCode responseCode, string response, int retryCount, DateTime nextRetryDate, IMessageTracking message = null);

        /// <inheritdoc cref="UpdateTransactionBooking(long, long, HttpStatusCode, string, int, DateTime, IMessageTracking)" />
        Result<StatusCodeResult> UpdateTransactionBookingHttp(long id, long transId, HttpStatusCode responseCode, string response, int retryCount, DateTime nextRetryDate, IMessageTracking message = null);

        /// <summary>
        /// Complete the transaction booking
        /// </summary>
        /// <param name="id">Transaction booking id</param>
        /// <param name="transId">Internal transaction id</param>
        /// <param name="bookedDate">Booked date</param>
        /// <param name="message">Current message</param>
        /// <returns>Result</returns>
        Result CompleteTransactionBooking(long id, long transId, DateTime bookedDate, IMessageTracking message = null);

        /// <inheritdoc cref="CompleteTransactionBooking(long, long, DateTime, IMessageTracking)" />
        Result<StatusCodeResult> CompleteTransactionBookingHttp(long id, long transId, DateTime bookedDate, IMessageTracking message = null);
    }
}
