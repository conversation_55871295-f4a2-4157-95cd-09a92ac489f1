import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Version } from '@angular/compiler';
import { TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { VersionInfo } from 'src/app/core/models/versionInfo.model';
import { LOADING_SERVICE_PROVIDER, LOADING_SERVICE_SPY } from 'src/app/services/loading.service.spy';
import { SignalRService } from 'src/app/services/signal-r.service';
import { WebService } from 'src/app/services/web.service';
import { FileVersionInfo } from '../../core/models/fileVersioInfo.model';
import { AboutComponent } from './about.component';
import { OptService } from 'src/app/services/opt.service';

describe('AboutComponent', () => {
  let component: AboutComponent;  
  let webServiceSpy: jasmine.SpyObj<WebService>;
  let signalRServiceSpy: jasmine.SpyObj<SignalRService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;
  let optServiceSpy: jasmine.SpyObj<OptService>;

  beforeEach(() => {
    const webSpy = jasmine.createSpyObj('WebService', [
      'getVersionInfo', 
      'commonFileAction',
      'upgradeService',
      'rollbackService',
      'restartService',
      'backupDatabase'
    ]);
    const signalRSpy = jasmine.createSpyObj('SignalRService', ['getAboutSignalRMessage']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger',['info','debug','error']);
    const optServiceSpyObj = jasmine.createSpyObj('OptService', ['requestOptLog']);
    
    TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        HttpClientTestingModule,
      ],
      providers: [
        AboutComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: WebService, useValue: webSpy },
        { provide: OptService, usevalue: optServiceSpyObj },
        { provide: SignalRService, useValue: signalRSpy },
        { provide: ActivatedRoute, useValue: { queryParams: of({})} },
        LOADING_SERVICE_PROVIDER(),
      ]
    });

    signalRServiceSpy = TestBed.inject(SignalRService) as jasmine.SpyObj<SignalRService>;
    webServiceSpy = TestBed.inject(WebService) as jasmine.SpyObj<WebService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    optServiceSpy = TestBed.inject(OptService) as jasmine.SpyObj<OptService>;

    signalRServiceSpy.getAboutSignalRMessage.and.returnValue(of());

    component = TestBed.inject(AboutComponent);
  });

  it('should create', () => {
    //Arrange
    //Act
    //Assert
    expect(component).toBeTruthy();
  });

  it('.refreshData() should handle success response from service', ()=>{
    //Arrange
    let fakeVersionInfo: VersionInfo = <VersionInfo>{
      Current: [new FileVersionInfo("Common", "1.0.1.0"), new FileVersionInfo("Current", "1.0.1.0")],
      DatabaseBackupFiles: ["Hydra_20210419175247_Backup.bak", "Hydra_20210419175306_Backup.bak"],
      PumpIntegrator: [new FileVersionInfo("FDC", "1.1.4.4", "$80A75F7F"), new FileVersionInfo("HSC.DLL", "1.2.0.0", "198B3759")],
      LayoutFiles: [],
      MediaFiles: [],
      OfflineFileService: [new FileVersionInfo("Unknown", "")],
      OptLogFiles: [],
      PlaylistFiles: [],
      Rollback: [new FileVersionInfo("Common", "1.0.1.0"), new FileVersionInfo("Current", "1.0.1.0")],
      SoftwareFiles: [],
      Upgrade: [],
      UpgradeFiles: [],
      UploadedFileNames: [],
      WhitelistFiles: [],
      DisplayOptLogFiles: []
    } as VersionInfo;
    webServiceSpy.getVersionInfo.and.returnValue(of(fakeVersionInfo));

    //Act
    component.refreshData();

    //Assert
    expect(component.versionInfoData).toEqual(fakeVersionInfo);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().hideLoadingScreen).toHaveBeenCalledTimes(1);
  });

  it('.refreshData() should handle unsuccess response from service', ()=>{
    //Arrange
    webServiceSpy.getVersionInfo.and.returnValue(throwError({status:500}));

    //Act
    component.refreshData();

    //Assert
    expect(component.versionInfoData).toEqual(new VersionInfo());
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
    expect(LOADING_SERVICE_SPY().showLoadingScreen).toHaveBeenCalledTimes(1);
    expect(LOADING_SERVICE_SPY().errorDuringLoading).toHaveBeenCalledTimes(1);
  });

  it('.getVersionText() should return correct value', ()=>{
    //Arrange
    let text = "unit test";

    //Act
    let result = component.getVersionText(text);

    //Assert
    expect(result).toEqual(text);
  });

  it('.getVersionText() should handle empty text', ()=>{
    //Arrange

    //Act
    let result = component.getVersionText(null);

    //Assert
    expect(result).toEqual("Not available");
  });

  it('.upgradeService() should handle success response from service', () => {
    //Arrange
    webServiceSpy.upgradeService.and.returnValue(of({}));
    
    //Act
    component.upgradeService();

    //Assert
    expect(webServiceSpy.upgradeService).toHaveBeenCalledTimes(1);
    expect(component.errors.OPTService).toBeNull();
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.upgradeService() should handle unsuccess response from service', () => {
    //Arrange
    webServiceSpy.upgradeService.and.returnValue(throwError({status: 500}))
    
    //Act
    component.upgradeService();

    //Assert
    expect(webServiceSpy.upgradeService).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.rollbackService() should handle success response from service', () => {
    //Arrange
    webServiceSpy.rollbackService.and.returnValue(of({}));
    
    //Act
    component.rollbackService();

    //Assert
    expect(webServiceSpy.rollbackService).toHaveBeenCalledTimes(1);
    expect(component.errors.OPTService).toBeNull();
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.rollbackService() should handle unsuccess response from service', () => {
    //Arrange
    webServiceSpy.rollbackService.and.returnValue(throwError({status: 500}))
    
    //Act
    component.rollbackService();

    //Assert
    expect(webServiceSpy.rollbackService).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.restartService() should handle success response from service', () => {
    //Arrange
    webServiceSpy.restartService.and.returnValue(of({}));
    
    //Act
    component.restartService();

    //Assert
    expect(webServiceSpy.restartService).toHaveBeenCalledTimes(1);
    expect(component.errors.OPTService).toBeNull();
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.restartService() should handle unsuccess response from service', () => {
    //Arrange
    webServiceSpy.restartService.and.returnValue(throwError({status: 500}))
    
    //Act
    component.restartService();

    //Assert
    expect(webServiceSpy.restartService).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  function CreateTestInfo(name: string, version: string): FileVersionInfo {
    let info = new FileVersionInfo(name, version, "CHECKSUM");
    info.DateModified = new Date();
    return info;
  }

  function testIsReinstall(upgradeVersion: string, currentVersion: string, commonVersion: string, upgradeCommonVersion: string, result: boolean) {
    //Arrange
    component.versionInfoData = new VersionInfo();
    component.versionInfoData.Upgrade = [];
    component.versionInfoData.Upgrade.push(CreateTestInfo("current", upgradeVersion), CreateTestInfo("common", upgradeCommonVersion));
    component.versionInfoData.Current = [];
    component.versionInfoData.Current.push(CreateTestInfo("current", currentVersion), CreateTestInfo("common", commonVersion))

    //Act
    let value = component.isReinstall();

    //Assert
    expect(value).toBe(result);
  }

  it('.isReinstall() should handle checks correctly 1', () => {
    testIsReinstall(null, "1", "2", null, false);
  });

  it('.isReinstall() should handle checks correctly 2', () => {
    testIsReinstall("", "1", "2", "3", false);
  });

  it('.isReinstall() should handle checks correctly 3', () => {
    testIsReinstall(null, "1", "2", "3", false);
  });

  it('.isReinstall() should handle checks correctly 4', () => {
    testIsReinstall("", "1", "2", null, false);
  });

  it('.isReinstall() should handle checks correctly 5', () => {
    testIsReinstall("", "1", "1", "1", true);
  });

  it('.commonFileAction() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    let errors = new Map<string, string>();
    errors.set("0", "error");
    let inputComponent = { nativeElement: { value: "test" } };
    
    //Act
    component.commonFileAction("service", "filename", "base64", errors, "0", inputComponent);

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(errors.size).toBe(0);
    expect(inputComponent.nativeElement.value).toBe("");
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.commonFileAction() should handle unsuccess response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    let errors = new Map<string, string>();
    
    //Act
    component.commonFileAction("service", "filename", "base64", errors, "0", null);

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(errors.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.filesAction() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    let errors = new Map<string, string>();
    errors.set("filename", "error");
    component.fileType.set("filename", "value");
    
    //Act
    component.filesAction("service", "filename", errors);

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.fileType.size).toBe(0);
    expect(errors.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.filesAction() should handle unsuccess response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    let errors = new Map<string, string>();
    component.fileType.set("filename", "value");
    
    //Act
    component.filesAction("service", "filename", errors);

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.fileType.size).toBe(1);
    expect(errors.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeWhitelistFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveWhitelistFile = new Map<string, string>();
    component.errors.RemoveWhitelistFile.set("filename", "error");
    
    //Act
    component.removeWhitelistFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveWhitelistFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeWhitelistFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveWhitelistFile = new Map<string, string>();
    component.errors.RemoveWhitelistFile.set("filename", "error");
    
    //Act
    component.removeWhitelistFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveWhitelistFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeLayoutFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveWhitelistFile = new Map<string, string>();
    component.errors.RemoveWhitelistFile.set("filename", "error");
    
    //Act
    component.removeWhitelistFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveWhitelistFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeLayoutFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveLayoutFile = new Map<string, string>();
    component.errors.RemoveLayoutFile.set("filename", "error");
    
    //Act
    component.removeLayoutFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveLayoutFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeUpgradeFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveUpgradeFile = new Map<string, string>();
    component.errors.RemoveUpgradeFile.set("filename", "error");
    
    //Act
    component.removeUpgradeFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveWhitelistFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeUpgradeFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveUpgradeFile = new Map<string, string>();
    component.errors.RemoveUpgradeFile.set("filename", "error");
    
    //Act
    component.removeUpgradeFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveUpgradeFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeSoftwareFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveSoftwareFile = new Map<string, string>();
    component.errors.RemoveSoftwareFile.set("filename", "error");
    
    //Act
    component.removeSoftwareFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveSoftwareFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeSoftwareFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveSoftwareFile = new Map<string, string>();
    component.errors.RemoveSoftwareFile.set("filename", "error");
    
    //Act
    component.removeSoftwareFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveSoftwareFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeMediaFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveMediaFile = new Map<string, string>();
    component.errors.RemoveMediaFile.set("filename", "error");
    
    //Act
    component.removeMediaFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveMediaFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeMediaFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveMediaFile = new Map<string, string>();
    component.errors.RemoveMediaFile.set("filename", "error");
    
    //Act
    component.removeMediaFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveMediaFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removePlaylistFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemovePlaylistFile = new Map<string, string>();
    component.errors.RemovePlaylistFile.set("filename", "error");
    
    //Act
    component.removePlaylistFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemovePlaylistFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removePlaylistFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemovePlaylistFile = new Map<string, string>();
    component.errors.RemovePlaylistFile.set("filename", "error");
    
    //Act
    component.removePlaylistFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemovePlaylistFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeOptLogFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.RemoveOptLogFile = new Map<string, string>();
    component.errors.RemoveOptLogFile.set("filename", "error");
    
    //Act
    component.removeOptLogFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveOptLogFile.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeOptLogFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.RemoveOptLogFile = new Map<string, string>();
    component.errors.RemoveOptLogFile.set("filename", "error");
    
    //Act
    component.removeOptLogFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.RemoveOptLogFile.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.restoreDatabase() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.Database = new Map<string, string>();
    component.errors.Database.set("filename", "error");
    
    //Act
    component.restoreDatabase("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.Database.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.restoreDatabase() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.Database = new Map<string, string>();
    component.errors.Database.set("filename", "error");
    
    //Act
    component.restoreDatabase("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.Database.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.removeDatabaseBackupFile() should handle success response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(of({}));
    component.errors.Database = new Map<string, string>();
    component.errors.Database.set("filename", "error");
    
    //Act
    component.removeDatabaseBackupFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.Database.size).toBe(0);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.removeDatabaseBackupFile() should handle error response from service', () => {
    //Arrange
    webServiceSpy.commonFileAction.and.returnValue(throwError({status: 500}));
    component.errors.Database = new Map<string, string>();
    component.errors.Database.set("filename", "error");
    
    //Act
    component.removeDatabaseBackupFile("filename");

    //Assert
    expect(webServiceSpy.commonFileAction).toHaveBeenCalledTimes(1);
    expect(component.errors.Database.size).toBe(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(2);
  });

  it('.backupDatabase() should handle success response from service', () => {
    //Arrange
    webServiceSpy.backupDatabase.and.returnValue(of({}));
    
    //Act
    component.backupDatabase();

    //Assert
    expect(webServiceSpy.backupDatabase).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.backupDatabase() should handle error response from service', () => {
    //Arrange
    webServiceSpy.backupDatabase.and.returnValue(throwError({status: 500}));
    
    //Act
    component.backupDatabase();

    //Assert
    expect(webServiceSpy.backupDatabase).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  
  it('.requestOptLog() should handle success response from service', () => {
    //Arrange
    optServiceSpy.requestOptLog.and.returnValue(of({}));
    
    //Act
    component.requestAllOptLogs();

    //Assert
    expect(optServiceSpy.requestOptLog).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.info).toHaveBeenCalledTimes(1);
  });

  it('.requestOptLog() should handle error response from service', () => {
    //Arrange
    optServiceSpy.requestOptLog.and.returnValue(throwError({status: 500}));
    
    //Act
    component.requestAllOptLogs();

    //Assert
    expect(optServiceSpy.requestOptLog).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });
});
