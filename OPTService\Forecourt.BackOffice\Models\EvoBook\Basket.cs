﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Forecourt.BackOffice.Models.EvoBook
{
    public class Basket
    {
        [JsonProperty("bunkeringSale")]
        public bool BunkeringSale { get; set; }
        [JsonProperty("totalSaleAmount")]
        public double TotalSaleAmount { get; set; }
        [JsonProperty("saleLines")]
        public List<SaleLine> SaleLines { get; set; }
        [JsonProperty("taxLines")]
        public List<TaxLine> TaxLines { get; set; }
        [JsonProperty("tenderLines")]
        public List<TenderLine> TenderLines { get; set; }
        [JsonProperty("discountVouchers")]
        public List<string> DiscountVouchers { get; set; }
        [<PERSON><PERSON><PERSON>roper<PERSON>("employee")]
        public string Employee { get; set; }
        [JsonProperty("loyaltyId")]
        public string LoyaltyId { get; set; }
        [JsonProperty("loyaltyTextLines")]
        public List<string> LoyaltyTextLines { get; set; }
    }
}
