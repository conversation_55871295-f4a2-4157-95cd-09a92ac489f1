import { HttpClientModule } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { LoggerModule, NGXLogger, NgxLoggerLevel } from 'ngx-logger';
import { of, throwError } from 'rxjs';
import { AppRoutingModule } from 'src/app/app-routing.module';
import { FuelPriceService } from 'src/app/services/fuel-price.service';


import { FuelPriceComponent } from './fuel-price.component';

describe('FuelPriceComponent', () => {
  let component: FuelPriceComponent;
  let fixture: ComponentFixture<FuelPriceComponent>;
  let serviceSpy: jasmine.SpyObj<FuelPriceService>;
  let ngxLoggerServiceSpy: jasmine.SpyObj<NGXLogger>;

  beforeEach(async () => {
    const serviceObjSpy = jasmine.createSpyObj('serviceSpy', ['setPrice', 'setGradeName', 'setGradeVatRate']);
    const ngxLoggerSpy = jasmine.createSpyObj('NGXLogger', ['info', 'debug', 'error']);

    await TestBed.configureTestingModule({
      imports: [
        AppRoutingModule,
        ReactiveFormsModule,
        LoggerModule.forRoot({ level: NgxLoggerLevel.DEBUG }),
        HttpClientModule],
      providers: [
        FuelPriceComponent,
        FormBuilder,
        { provide: NGXLogger, useValue: ngxLoggerSpy },
        { provide: FuelPriceService, useValue: serviceObjSpy }
      ]
    });

    serviceSpy = TestBed.inject(FuelPriceService) as jasmine.SpyObj<FuelPriceService>;
    ngxLoggerServiceSpy = TestBed.inject(NGXLogger) as jasmine.SpyObj<NGXLogger>;
    component = TestBed.inject(FuelPriceComponent);

  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('.ngOnInit() success', () => {
    //Arrange
    component.fuelPrice =
    {
      Grade: 1,
      GradeName: "Unleaded",
      VatRate: 20,
      PriceToSet: 136.9,
      Prices: [
        {
          Pump: 1,
          Price: 136.9
        },
        {
          Pump: 2,
          Price: 136.9
        }
      ]
    };
    spyOn(component.fuelPriceForm, 'patchValue').and.stub();

    //Act
    component.ngOnInit();

    //Assert
    expect(component.fuelPriceForm.patchValue).toHaveBeenCalledTimes(0);
  });

  it('.setFuelPrice() should handle success response from service', () => {
    //Arrange
    serviceSpy.setPrice.and.returnValue(of({}));

    //Act
    component.setFuelPrice();

    //Assert
    expect(serviceSpy.setPrice).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
  });

  it('.setFuelPrice() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setPrice.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setFuelPrice();

    //Assert
    expect(serviceSpy.setPrice).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setGradeName() should handle success response from service', () => {
    //Arrange
    serviceSpy.setGradeName.and.returnValue(of({}));

    //Act
    component.setGradeName();

    //Assert
    expect(serviceSpy.setGradeName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
  });

  it('.setGradeName() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setGradeName.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setGradeName();

    //Assert
    expect(serviceSpy.setGradeName).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });

  it('.setGradeVatRate() should handle success response from service', () => {
    //Arrange
    serviceSpy.setGradeVatRate.and.returnValue(of({}));

    //Act
    component.setGradeVatRate();

    //Assert
    expect(serviceSpy.setGradeVatRate).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(2);
  });

  it('.setGradeVatRate() should handle unsuccess response from service', () => {
    //Arrange
    serviceSpy.setGradeVatRate.and.returnValue(throwError({ status: 500 }))

    //Act
    component.setGradeVatRate();

    //Assert
    expect(serviceSpy.setGradeVatRate).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.debug).toHaveBeenCalledTimes(1);
    expect(ngxLoggerServiceSpy.error).toHaveBeenCalledTimes(1);
  });
});
