﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Pump.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Extensions;
using Htec.Foundation.Workers.Interfaces;
using Htec.Hydra.Core.Bos.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using System;
using System.Collections.Generic;
using System.Net;

namespace Forecourt.Bos.Workers
{
    /// <inheritdoc/>
    public abstract class BosOutWorker: Core.Workers.HydraDbable<HydraDb.Interfaces.IHydraDb>, IWorkerable, IBosIntegratorOut<IMessageTracking>
    {    
        /// <inheritdoc />
        protected BosOutWorker(IHtecLogManager logManager, string loggerName, IConfigurationManager configurationManager, HydraDb.Interfaces.IHydraDb hydraDb, IPumpCollection allPumps = null) :
            base(hydraDb, logManager, loggerName.ConvertToPrefixedDottedName(LoggerNamePrefix), allPumps, configurationManager)
        {
        }

        /// <inheritdoc cref="DayEndResponse"/>
        protected virtual Result DoDayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result DayEndResponse(ShiftEndItem dayEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales,
            IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => DoDayEndResponse(dayEndDetails, itemSales, categorySales, cardSales, cardAmountSales, cardVolumeSales, message), message);
        }

        /// <inheritdoc cref="GetTransactionFileFolder"/>
        protected virtual Result<string> DoGetTransactionFileFolder()
        {
            return Result.Success<string>(null);
        }

        /// <inheritdoc />
        public Result<string> GetTransactionFileFolder()
        {
           return DoGetTransactionFileFolder();
        }

        /// <inheritdoc cref="MoveOfflineTransactionFiles(IPAddress, string)" />
        protected virtual Result DoMoveOfflineTransactionFiles(IPAddress ipAddress, string logginReference = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result MoveOfflineTransactionFiles(IPAddress ipAddress, string logginReference = null)
        {
            return DoAction(() => DoMoveOfflineTransactionFiles(ipAddress, logginReference), logginReference);
        }

        /// <inheritdoc cref="SendTransaction(SendTransactionItem, IMessageTracking)"/>
        protected virtual Result DoSendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result SendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            return DoAction<Result>(() => DoSendTransaction(sendTransaction, message), message);
        }

        /// <inheritdoc cref="ShiftEndResponse(ShiftEndItem, IEnumerable{ItemSalesItem}, IEnumerable{CardSalesItem}, IEnumerable{CardVolumeSalesItem}, IMessageTracking)" />
        protected virtual Result DoShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result ShiftEndResponse(ShiftEndItem shiftEndDetails, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            return DoAction(() => DoShiftEndResponse(shiftEndDetails, itemSales, cardSales, cardVolumeSales, message), message);
        }

        /// <inheritdoc cref="WriteLocalAccountInvoiceFile(LocalAccountTransactionItem, IMessageTracking)"/>
        protected virtual Result DoWriteLocalAccountInvoiceFile(LocalAccountTransactionItem localAccountItem, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result WriteLocalAccountInvoiceFile(LocalAccountTransactionItem localAccountItem, IMessageTracking message = null)
        {
            return DoAction(() => DoWriteLocalAccountInvoiceFile(localAccountItem, message), message);
        }

        /// <inheritdoc cref="WriteShiftEndFiles(bool, DateTime, DateTime, ShiftEndItem, IEnumerable{ItemSalesItem}, IEnumerable{CategorySalesItem}, IEnumerable{CardSalesItem}, IEnumerable{CardAmountSalesItem}, IEnumerable{CardVolumeSalesItem}, IMessageTracking)"/>
        protected virtual Result DoWriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result WriteShiftEndFiles(bool isDayEnd, DateTime startTime, DateTime endTime, ShiftEndItem endItem, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardSalesItem> cardSales, IEnumerable<CardAmountSalesItem> cardAmountSales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            return DoAction(() => DoWriteShiftEndFiles(isDayEnd, startTime, endTime, endItem, itemSales, categorySales, cardSales, cardAmountSales, cardVolumeSales, message), message);
        }

        /// <inheritdoc cref="WriteTransactionFile(IEnumerable{TransactionItem}, DateTime, IEnumerable{IPAddress}, IMessageTracking)"/>
        protected virtual Result DoWriteTransactionFile(IEnumerable<TransactionItem> items, DateTime dateTime, IEnumerable<IPAddress> devices = null, IMessageTracking message = null)
        {
            return Result.Success();
        }

        /// <inheritdoc />
        public Result WriteTransactionFile(IEnumerable<TransactionItem> items, DateTime dateTime, IEnumerable<IPAddress> devices = null, IMessageTracking message = null)
        {
            return DoAction(() => DoWriteTransactionFile(items, dateTime, devices, message), message);
        }
    }
}
