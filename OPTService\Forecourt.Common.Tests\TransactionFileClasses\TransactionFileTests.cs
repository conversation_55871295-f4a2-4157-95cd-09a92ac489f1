﻿using FluentAssertions;
using Forecourt.Bos.TransactionFiles;
using Forecourt.Common.HydraDbClasses;
using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Logger.Interfaces.Tracing;
using NSubstitute;
using System;
using System.Collections.Specialized;
using System.IO.Abstractions.TestingHelpers;
using System.Linq;
using Xunit;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TaskbarClock;
using CardVolumeSalesItem = Htec.Hydra.Core.Bos.Messages.Hydra.CardVolumeSalesItem;
using LocalAccountTransactionItem = Htec.Hydra.Core.Bos.Messages.Hydra.LocalAccountTransactionItem;

namespace OPT.Common.Tests.TransactionFileClasses
{
    public class TransactionFileTests
    {
        private readonly string DefaultTillNo = "99";
        private readonly IHydraDb _hydraDb;
        private readonly IHtecLogger _logger;
        private readonly MockFileSystem _fileSystem;
        private readonly IConfigurationManager _configurationManager;
        private readonly NameValueCollection _appSettings;

        public TransactionFileTests()
        {
            _hydraDb = Substitute.For<IHydraDb>();
            _logger = Substitute.For<IHtecLogger>();
            _fileSystem = new MockFileSystem();
            _appSettings = Substitute.For<NameValueCollection>();
            _configurationManager = Substitute.For<IConfigurationManager>();
            _configurationManager.AppSettings.Returns(_appSettings);
            _appSettings[HydraTransactionFile.ConfigKeyCleanTrFiles].Returns("false");
        }

        #region Create Transaction File Names

        [Fact]
        public void create_transaction_file_name_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateTransactionFileName("1", new DateTime(2021, 4, 1, 11, 25, 0));

            // Assert
            result.Should().Be(@"C:\Transactions\Test\TR1210401112500.csv");
        }

        [Fact]
        public void create_shift_summary_file_name_not_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateShiftSummaryFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), false);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\SM1210401112500S.csv");
        }

        [Fact]
        public void create_shift_summary_file_name_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateShiftSummaryFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), true);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\DE1210401112500D.csv");
        }

        [Fact]
        public void create_sales_item_file_name_not_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateSalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), false);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\SI1210401112500S.csv");
        }

        [Fact]
        public void create_sales_item_file_name_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateSalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), true);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\SI1210401112500D.csv");
        }

        [Fact]
        public void create_card_sales_item_file_name_not_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateCardSalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), false);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\CA1210401112500S.csv");
        }

        [Fact]
        public void create_card_sales_item_file_name_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateCardSalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), true);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\CA1210401112500D.csv");
        }


        [Fact]
        public void create_category_sales_item_file_name_not_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateCategorySalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), false);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\SC1210401112500S.csv");
        }

        [Fact]
        public void create_category_sales_item_file_name_day_end_creates_valid_name()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.CreateCategorySalesItemFileName("1", new DateTime(2021, 4, 1, 11, 25, 0), true);

            // Assert
            result.Should().Be(@"C:\Transactions\Test\SC1210401112500D.csv");
        }

        private static AllFileLocations CreateAllFileLocations(string transactionLocation)
        {
            return new AllFileLocations(string.Empty,
                transactionLocation, string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                false,
                string.Empty,
                string.Empty,
                string.Empty,
                false,
                false,
                false,
                false);
        }

        #endregion

        #region Remove Duplicate Product Lines

        [Fact]
        public void remove_duplicate_product_lines_enabled_removes_duplicates()
        {
            // Arrange
            var dateTime = new DateTime(2021, 4, 1, 13, 5, 0);
            var items = new[]
            {
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
            };

            _appSettings[HydraTransactionFile.ConfigKeyCleanTrFiles].Returns("true");

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.RemoveDuplicateProductLines(items);

            // Assert
            var expected = new[] {TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1")};
            result.Should().Equal(expected);
        }

        [Fact]
        public void remove_duplicate_product_lines_enabled_array_order_retained()
        {
            // Arrange
            var dateTime = new DateTime(2021, 4, 1, 13, 5, 0);
            var items = new[]
            {
                new EftChipCardMethodOfPaymentItem(75, "1234", "1", 1240, "12340000", dateTime, "16"),
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
                TransactionFileItem.ConstructEndOfSalesTransaction(75, "1234", dateTime)
            };

            _appSettings[HydraTransactionFile.ConfigKeyCleanTrFiles].Returns("true");

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.RemoveDuplicateProductLines(items);

            // Assert
            var expected = new[]
            {
                new EftChipCardMethodOfPaymentItem(75, "1234", "1", 1240, "12340000", dateTime, "16"),
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
                TransactionFileItem.ConstructEndOfSalesTransaction(75, "1234", dateTime)
            };

            result.Should().Equal(expected);
        }

        [Fact]
        public void remove_duplicate_product_lines_disabled_does_not_remove_duplicates()
        {
            // Arrange
            var dateTime = new DateTime(2021, 4, 1, 13, 5, 0);
            var items = new[]
            {
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
                TransactionFileItem.ConstructSalesItem(75, "1234", "Unleaded", "FUEL1", 12400, 1240, "PUMP 2:3", dateTime, "7", "1"),
            };

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            var result = transactionFile.RemoveDuplicateProductLines(items);

            // Assert
            result.Should().Equal(items);
        }

        #endregion

        #region Write Transactions
        
        [Fact]
        public void write_transactions_valid_transaction_outputs_file()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var now = new DateTime(2021, 08, 10, 14, 55, 37);
            var items = new[]
            {
                new LocalAccountTransactionItem
                {
                    CardNumber = "****************",
                    Date =now.ToString(TransactionLineFormats.DateFormat),
                    Time = now.ToString(TransactionLineFormats.TimeFormat),
                    Product = "FUEL2",
                    Volume = 8.37m,
                    Amount = 11.02m,
                    Grade = "Diesel"
                }
            };

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            transactionFile.WriteLocalAccountInvoiceFile(items, 9, now);

            // Assert
            var expectedContents = "****************,********,145537,,,FUEL2,8.37,11.02,Diesel\r\n";
            _fileSystem.GetFile("C:\\Transactions\\Test\\***************.csv").TextContents.Should().Be(expectedContents);
        }

        [Fact]
        public void write_transactions_registration_transaction_outputs_registration_line()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var now = new DateTime(2021, 08, 10, 14, 55, 37);
            short tillNumber = 75;
            string receipt = "567";
            string operatorId = "1";
            string registration = "TH68 SRG";
            uint mileage = 0;
            string cardNumber = "633803******0376";
            string name = "Diesel";
            string code = "FUEL2";
            string eftCode = "27";
            var pumpDetails = "PUMP 2:1";

            uint quantity = 837;
            uint amount = 1102;
            string cat = "7";
            string subCat = "2";

            
            var items = new[]
            {
                TransactionFileItem.ConstructSalesItem(tillNumber, receipt, name, code, quantity, amount, pumpDetails, now, cat, subCat),
                new EftChipCardMethodOfPaymentItem(tillNumber, receipt, operatorId, amount, cardNumber, now, eftCode),
                new RegistrationAndMileage(tillNumber, receipt, operatorId, registration, mileage, cardNumber, now),
                TransactionFileItem.ConstructEndOfSalesTransaction(tillNumber, receipt, now)
            };

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            transactionFile.WriteTransactionFile(items, now, null);

            // Assert
            var expectedContents = "75,10,567,1,Diesel,FUEL2,0.84,11.02,PUMP 2:1,\" \",********,145537,7,2\r\n" +
                                   "75,16,567,1,,27,0.00,11.02,633803******0376,I,********,145537,,\r\n" +
                                   "75,18,567,1,,TH68 SRG,0,0.00,633803******0376,\" \",********,145537,,\r\n" +
                                   "75,19,567,1,,,0.00,0.00,,\" \",********,145537,,\r\n";
            _fileSystem.GetFile("C:\\Transactions\\Test\\TR75210810145537.csv").TextContents.Should().Be(expectedContents);
        }

        [Fact]
        public void write_transactions_registration_and_mileage_transaction_outputs_registration_line()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var now = new DateTime(2025, 04, 29, 13, 14, 23);
            short tillNumber = 75;
            string receipt = "69785";
            string operatorId = "1";
            string registration = "TEST REG";
            uint mileage = 4561;
            string cardNumber = "633803******0371";
            string name = "Unleaded";
            string code = "FUEL1";
            string eftCode = "13";
            var pumpDetails = "PUMP 4:2";

            uint quantity = 837;
            uint amount = 1102;
            string cat = "7";
            string subCat = "1";


            var items = new[]
            {
                TransactionFileItem.ConstructSalesItem(tillNumber, receipt, name, code, quantity, amount, pumpDetails, now, cat, subCat),
                new EftChipCardMethodOfPaymentItem(tillNumber, receipt, operatorId, amount, cardNumber, now, eftCode),
                new RegistrationAndMileage(tillNumber, receipt, operatorId, registration, mileage, cardNumber, now),
                TransactionFileItem.ConstructEndOfSalesTransaction(tillNumber, receipt, now)
            };

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            transactionFile.WriteTransactionFile(items, now, null);

            // Assert
            var expectedContents = "75,10,69785,1,Unleaded,FUEL1,0.84,11.02,PUMP 4:2,\" \",********,131423,7,1\r\n" +
                                   "75,16,69785,1,,13,0.00,11.02,633803******0371,I,********,131423,,\r\n" +
                                   "75,18,69785,1,,TEST REG,4561,0.00,633803******0371,\" \",********,131423,,\r\n" +
                                   "75,19,69785,1,,,0.00,0.00,,\" \",********,131423,,\r\n";

            _fileSystem.GetFile("C:\\Transactions\\Test\\****************.csv").TextContents.Should().Be(expectedContents);
        }

        #endregion

        #region Write Local Account Invoice

        [Fact]
        public void write_local_account_invoice_valid_transaction_outputs_file()
        {
            // Arrange
            _hydraDb.GetFileLocations().Returns(CreateAllFileLocations(@"C:\Transactions\Test"));

            var now = new DateTime(2021, 08, 10, 14, 55, 37);
            var items = new[]
            {
                new LocalAccountTransactionItem
                {
                    CardNumber = "****************",
                    Date =now.ToString(TransactionLineFormats.DateFormat),
                    Time = now.ToString(TransactionLineFormats.TimeFormat),
                    Product = "FUEL2",
                    Volume = 8.37m,
                    Amount = 11.02m,
                    Grade = "Diesel"
                }
            };

            var transactionFile = CreateDefaultHydraTransactionFile();

            // Act
            transactionFile.WriteLocalAccountInvoiceFile(items, 9, now);

            // Assert
            var expectedContents = "****************,********,145537,,,FUEL2,8.37,11.02,Diesel\r\n";
            _fileSystem.GetFile("C:\\Transactions\\Test\\***************.csv").TextContents.Should().Be(expectedContents);
        }

        #endregion

        #region Write Card Volume File

        [Theory, InlineData(true), InlineData(false)]
        public void write_card_volume_file_no_data_writes_no_file(bool isDayEnd)
        {
            // Arrange
            var transactionFile = CreateDefaultHydraTransactionFile();

            var cardValueItems = Enumerable.Empty<CardVolumeSalesItem>();

            // Act
            transactionFile.WriteCardVolumeItems(DefaultTillNo, cardValueItems.ToArray(), isDayEnd);

            // Assert
            _fileSystem.AllFiles.Should().HaveCount(0);
        }
     
        [Theory, InlineData(true), InlineData(false)]
        public void write_card_volume_file_valid_file_contents_is_correct(bool isDayEnd)
        {
            // Arrange
            var transactionFile = CreateDefaultHydraTransactionFile();

            var cardValueItems = CreateDefaultCardVolumes();

            // Act
            transactionFile.GetNow = () => new DateTime(2020, 1, 1, 13, 04, 00);

            transactionFile.SetFileDirectory(@"C:\temp");
            transactionFile.WriteCardVolumeItems(DefaultTillNo, cardValueItems, isDayEnd);

            // Assert
            const string expected = "1,1,0.50,VISA\r\n";
            _fileSystem.GetFile($@"C:\temp\CV99200101130400{(isDayEnd ? "D" : "S")}.csv").TextContents.Should().Be(expected);
        }

        private static CardVolumeSalesItem[] CreateDefaultCardVolumes()
        {
            return new[] { CardVolumeSalesItem.ConstructCardVolumeSalesItem(1, 1, 500, "VISA") };
        }

        #endregion

        private HydraTransactionFile CreateDefaultHydraTransactionFile()
        {
            return new HydraTransactionFile(_hydraDb, _logger, _fileSystem, _configurationManager);
        }
    }
}
