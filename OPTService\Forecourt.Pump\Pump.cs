﻿using CSharpFunctionalExtensions;
using Forecourt.Core.Opt.Enums;
using Forecourt.Core.Opt.Interfaces;
using Forecourt.Core.Pos.Enums;
using Forecourt.Core.Pump.Interfaces;
using Forecourt.Core.Pump.Models;
using Forecourt.Pump.HydraDb.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Extensions;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Core;
using Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc;
using Htec.Hydra.Core.SecAuth.Enums;
using Htec.Hydra.Messages.Opt.Models;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using OPT.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using CommonHydraPosCommand = Forecourt.Core.Pos.Enums.HydraPosCommand;
using CommonPumpState = Htec.Hydra.Core.Pump.Messages.HydraFdc.Hsc.PumpState;
using PaymentResult = Forecourt.Core.Payment.Enums.PaymentResult;
using PumpMode = Forecourt.Core.Pump.Enums.PumpModeType;

namespace Forecourt.Pump
{
    /// <inheritdoc/>
    [HasConfiguration]
    public class Pump : Core.Pump.Models.Pump, IPump
    {
        PumpMode IPump.PumpMode { get => _pumpMode; }

        public IOpt Opt { get; private set; }
        public byte Number { get; private set; }
        public byte DeliveredHose { get; private set; }
        public ushort DeliveredPpu { get; private set; }
        public string Tid { get; private set; }

        private SecAuthState _secAuthState = SecAuthState.Idle;
        private PumpMode _pumpMode = PumpMode.OutsideOnly;
        private PumpMode _defaultMode = PumpMode.OutsideOnly;
        private bool _unmanned = false;
        public bool IsDelivering { get; private set; } = false;
        public bool HasKioskPayment { get; private set; } = false;
        public bool MaxFillOverrideForFuelCards { get; private set; } = false;
        public bool MaxFillOverrideForPaymentCards { get; private set; } = false;
        public IList<byte> AllGrades { get; } = new List<byte>();
        public bool IsDelivered => _paymentToClear && !IsDelivering;

        private bool _pumpRequesting = false;
        private bool _pumpOptPayment = false;
        private bool _authoriseSent = false;
        private bool _paymentToClear = false;
        private IList<byte> _allowedGrades = null;
        private IList<byte> _disallowedGrades = null;
        private IList<byte> _requestedGrades = new List<byte>();
        public bool ThirdPartyWait { get; private set; } = false;
        private bool _hasCard = false;
        public bool ClosePending { get; private set; } = false;

        public bool ModePending => _pumpMode != _pendingMode;

        private PumpMode _pendingMode = PumpMode.OutsideOnly;

        private IHydraDb _hydraDb;
        private PumpTransaction _pumpTransaction;

        public PumpState LastPumpState { get; set; } = CommonPumpState.Idle;

        private PumpStateType _previousPumpState = PumpStateType.Closed;

        // TODO: This needs some serious work
        public HydraPosPumpState State
        {
            get
            {
                if (PumpIsClosed)
                {
                    return HydraPosPumpState.Closed;
                }

                if (!IsPaid && HasKioskPayment && !KioskUse)
                {
                    return Opt?.PrinterError == true
                        ? GetPrinterErrorState()
                        : HydraPosPumpState.OfferKiosk;
                }

                if (KioskUse)
                {
                    return HydraPosPumpState.KioskUse;
                }

                if (IsDelivering)
                {
                    return HydraPosPumpState.InDelivery;
                }

                if (HasPayment)
                {
                    return HydraPosPumpState.TakeFuel;
                }

                if (_paymentToClear)
                {
                    return HydraPosPumpState.Complete;
                }

                if (_hasCard)
                {
                    return HydraPosPumpState.CardCheck;
                }

                if (Opt?.PrinterError == true)
                {
                    return GetPrinterErrorState();
                }

                return Opt?.PaperLow == true
                    ? GetPrinterErrorState()
                    : GetIdleState();
            }
        }

        private HydraPosPumpState GetIdleState()
        {
            return Opt?.Mode == OptModeType.OptModeMixed
                ? HydraPosPumpState.OfferKiosk
                : HydraPosPumpState.Idle;
        }

        private HydraPosPumpState GetPrinterErrorState()
        {
            return Opt?.Mode == OptModeType.OptModeMixed
                ? HydraPosPumpState.MixModePrintErr
                : HydraPosPumpState.PrintErr;
        }

        public PumpStateType PumpState
        {
            get
            {
                if (PumpIsClosed)
                {
                    return PumpStateType.Closed;
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; _pumpMode: {_pumpMode}; _unmanned: {_unmanned}; HasKioskPayment: {HasKioskPayment}; " +
                    $"KioskUse: {KioskUse}; OutsideOnly: {OutsideOnly}" });

                return KioskUse ? PumpStateType.KioskOnly :
                    OutsideOnly ? PumpStateType.Open :
                    PumpStateType.Mixed;
            }
        }

        private bool _hasDeliveryResponsePending = false;
        private bool _hasPayment = false;
        public uint AuthorisedAmount { get; private set; } = 0;
        private DateTime _paymentExpiryTime;
        private DateTime _secAuthExpiryTime;
        private DateTime _reserveExpiryTime = DateTime.MaxValue;
        public bool HasPayment => _hasPayment && _paymentExpiryTime > DateTime.Now;
        public bool IsSecAuthRequested => _secAuthState == SecAuthState.Requested && !HasSecAuthRequestTimedOut;
        public bool HasSecAuthRequestTimedOut => _secAuthExpiryTime < DateTime.UtcNow && _secAuthExpiryTime != DateTime.MinValue;
        public bool HasReserveTimedOut => _reserveExpiryTime < DateTime.UtcNow;

        public SecAuthState SecAuthState => _secAuthState;
        public DateTime SecAuthExpiryTime => _secAuthExpiryTime;
        public DateTime ReserveExpiryTime => _reserveExpiryTime;

        public bool HasOptPayment => OptUse && _pumpOptPayment;
        public bool IsNozzleUp => _pumpRequesting || IsDelivering;
        public bool PumpIsClosed { get; private set; } = true;
        public string CardNumber { get; private set; }
        public string CardProductName { get; private set; }
        public uint ClearedAmount { get; private set; }
        public bool IsPaid { get; private set; } = true;
        public bool IsInError { get; set; } = false;

        public bool IsSecAuthApproved => _secAuthState == SecAuthState.Success || (_secAuthState == SecAuthState.Requested && HasSecAuthRequestTimedOut);

        private bool IsInPodMode => Opt?.IsInPodMode ?? false;
        private bool _isMobile;
        private bool _pendingIsMobile;

        public bool InUse => !PumpIsClosed && (HasPayment || IsDelivering || _paymentToClear || _hasCard);
        public bool InUseByOpt => !PumpIsClosed && (HasPayment || IsDelivering && _pumpOptPayment || _paymentToClear || _hasCard);
        public bool IsMobile => _isMobile;
        public bool PendingIsMobile => _pendingIsMobile;
        public bool KioskUse => _pumpMode == PumpMode.KioskOnly && !_unmanned || _pumpMode == PumpMode.KioskUse || HasKioskPayment;
        public bool DefaultKioskUse => _defaultMode == PumpMode.KioskOnly;
        public bool OutsideOnly => _pumpMode == PumpMode.OutsideOnly || _unmanned;
        public bool DefaultOutsideOnly => _defaultMode == PumpMode.OutsideOnly;
        public bool Mixed => _pumpMode == PumpMode.Mixed && !_unmanned || _pumpMode == PumpMode.KioskUse;
        public bool OptUse => !KioskUse;

        public bool IsKioskMode => _pumpMode == PumpMode.KioskOnly && !_unmanned || _pumpMode == PumpMode.KioskUse;
        public bool CanAddPayment => !KioskUse && !PumpIsClosed;
        public bool CanCancelPayment => (LastPumpState == CommonPumpState.Request || LastPumpState == CommonPumpState.Idle || LastPumpState == CommonPumpState.Closed || LastPumpState == CommonPumpState.Initialise) && !_authoriseSent;
        public bool CanClearPayment => true;
        public bool CanInsertCard => !InUse && !KioskUse && !PumpIsClosed;
        public bool CanSetKioskUse => _pumpMode == PumpMode.Mixed || _pumpMode == PumpMode.KioskUse;
        public bool IsReserved { get; private set; } = false;

        public PumpTransaction TransactionSummary => _pumpTransaction;

        private DateTime PayAtKioskExpiryTime { get; set; }

        /// <inheritdoc/>
        public Pump(byte number, IHydraDb hydraDb, IHtecLogManager logMan, string loggerName, IOpt opt = null, string tid = null, IConfigurationManager configurationManager = null) : base(logMan,
            loggerName, configurationManager)
        {
            DoCtor(number, hydraDb, opt, tid);
        }

        private void DoCtor(byte number, IHydraDb hydraDb, IOpt opt = null, string tid = null)
        {
            _hydraDb = hydraDb;
            Opt = opt;
            Number = number;
            Tid = tid;
            PayAtKioskExpiryTime = DateTime.MaxValue;
        }

        public void SetHasKioskPayment(bool value = true)
        {
            HasKioskPayment = value;
        }

        public void AddPayment
            (uint amount, bool thirdPartyWait = false, IList<byte> allowedGrades = null, IList<byte> disallowedGrades = null, string reference = null)
        {
            DoAction(() =>
            {
                if (CanAddPayment)
                {
                    if (!_paymentToClear)
                    {
                        _hasPayment = true;
                        AuthorisedAmount = amount;
                        _allowedGrades = allowedGrades;
                        _disallowedGrades = disallowedGrades;
                        ThirdPartyWait = thirdPartyWait;
                    }

                    _hasCard = false;
                    CardNumber = null;
                    CardProductName = null;
                    ClearedAmount = 0;
                    _paymentExpiryTime = DateTime.Now.AddSeconds(Opt?.PaymentTimeoutInSeconds ?? ConfigConstants.DefaultPaymentTimeoutInSeconds);
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        private void ApplyPendingMode()
        {
            if (ClosePending)
            {
                PumpIsClosed = true;
                ClosePending = false;
            }

            _pumpMode = _pendingMode;
            _isMobile = _pendingIsMobile;
        }

        public bool CancelPayment(string reference = null)
        {
            var result = false;
            DoAction(() =>
            {
                if (CanCancelPayment)
                {
                    _hasPayment = false;
                    _hasCard = false;
                    CardNumber = null;
                    CardProductName = null;
                    ClearedAmount = 0;
                    _allowedGrades = null;
                    _disallowedGrades = null;
                    _secAuthState = SecAuthState.Idle;
                    ThirdPartyWait = false;
                    _hasDeliveryResponsePending = false;
                    _reserveExpiryTime = DateTime.MaxValue;
                    _pendingIsMobile = false;
                    IsReserved = false;

                    ApplyPendingMode();

                    result = true;
                }
            }, _pumpTransaction?.GetFullId(reference));

            return result;
        }

        public void ClearPayment(string cardNumber, string cardProductName, uint amount, string reference = null)
        {
            DoAction(() =>
            {
                if (CanClearPayment)
                {
                    _hasPayment = false;
                    _paymentToClear = false;
                    _hasCard = false;
                    CardNumber = cardNumber;
                    CardProductName = cardProductName;
                    ClearedAmount = amount;
                    _allowedGrades = null;
                    _disallowedGrades = null;
                    _secAuthState = SecAuthState.Idle;
                    ThirdPartyWait = false;
                    _hasDeliveryResponsePending = false;
                    _reserveExpiryTime = DateTime.MaxValue;
                    _pendingIsMobile = false;
                    IsReserved = false;

                    ApplyPendingMode();
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        public void ResetPaymentTimeout(int timeout, string reference = null)
        {
            DoAction(() =>
            {
                if (HasPayment)
                {
                    _paymentExpiryTime = DateTime.Now.AddSeconds(timeout);
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        public bool IsAuthorised(out uint amount, string reference = null)
        {
            amount = AuthorisedAmount;
            return DoAction(() =>
            {
                var isAuthorised = HasPayment
                                   && !IsInError
                                   && IsSecAuthApproved
                                   && _pumpRequesting
                                   && !ThirdPartyWait
                                   && !_authoriseSent
                                   && _requestedGrades != null
                                   && _requestedGrades.Count > 0
                                   && !_hasDeliveryResponsePending
                                   && (_allowedGrades == null || _requestedGrades.Any(x => _allowedGrades.Contains(x)))
                                   && (_disallowedGrades == null || !_requestedGrades.All(y => _disallowedGrades.Contains(y)));

                if (isAuthorised)
                {
                    _authoriseSent = true;
                    _pumpOptPayment = true;
                }
                else
                {
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; isAuthorised: {isAuthorised}{GetNotAuthorisedReasons()}" });
                }

                return Result.Success(isAuthorised);
            }, _pumpTransaction?.GetFullId(reference)).Value;
        }

        /// <inheritdoc />
        public string GetNotAuthorisedReasons()
        {
            return JsonConvert.SerializeObject(new
            {
                Number,
                State,
                HasPayment,
                IsInError,
                IsSecAuthApproved,
                PumpRequesting = _pumpRequesting,
                ThirdPartyWait,
                AuthoriseSent = _authoriseSent,
                RequestedGrades = _requestedGrades?.Count,
                HasDeliveryResponsePending = _hasDeliveryResponsePending,
                Condition1 = _allowedGrades == null || _requestedGrades.Any(x => _allowedGrades.Contains(x)),
                Condition2 = _disallowedGrades == null || !_requestedGrades.All(y => _disallowedGrades.Contains(y))
            });
        }

        public void Requesting(IList<byte> grades, string reference = null)
        {
            DoAction(() =>
            {
                _pumpRequesting = true;
                _requestedGrades = new List<byte>(grades);
            }, _pumpTransaction?.GetFullId(reference));
        }

        public void Idle(string reference = null)
        {
            DoAction(() =>
            {
                _authoriseSent = false;
                _pumpRequesting = false;
                _pumpOptPayment = false;

                if (_pendingMode == PumpMode.KioskUse)
                {
                    _pendingMode = PumpMode.Mixed;
                    _pendingIsMobile = false;
                }

                if (_pumpMode == PumpMode.KioskUse && !InUse && !HasKioskPayment)
                {
                    _pumpMode = _pendingMode;
                    _isMobile = _pendingIsMobile;
                }

                IsDelivering = false;
                if (PumpIsClosed || ClosePending)
                {
                    OpenPump(reference: reference);
                }

                if (!IsPaid)
                {
                    return;
                }

                ApplyPendingMode();

                ResetPumpTransaction(reference);
            }, _pumpTransaction?.GetFullId(reference));
        }

        private void ResetPumpTransaction(string reference)
        {
            if (_pumpTransaction == null)
            {
                _pumpTransaction = new PumpTransaction(Number);
            }
            else if (_pumpTransaction.IsContinuationState())
            {
                DoDeferredLogging(LogLevel.Info, HeaderPump, () => new[] { $"{Number}; Continuing transaction" }, reference: _pumpTransaction.GetFullId(reference));
                _pumpTransaction.ResetRetainingPaymentApproved();
            }
        }

        public void Delivering(string reference = null)
        {
            DoAction(() =>
            {
                IsDelivering = true;
                _pumpRequesting = false;
                if (_hasPayment)
                {
                    _pumpOptPayment = true;
                    _paymentToClear = true;
                    _hasPayment = false;
                }
                else if (!IsPaid)
                {
                    _pumpOptPayment = false;
                    if (_pumpMode == PumpMode.KioskOnly || _pumpMode == PumpMode.KioskUse)
                    {
                        HasKioskPayment = true;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        public void Delivered(bool deliveredRequestSent = false, string reference = null)
        {
            DoAction(() =>
            {
                _authoriseSent = false;
                _pumpRequesting = false;

                IsDelivering = false;

                _hasDeliveryResponsePending = deliveredRequestSent;
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; HasDeliveryResponsePending: {deliveredRequestSent}" });
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SecAuthRequestSent(string reference = null)
        {
            DoAction(() =>
            {
                var timeout = _hydraDb.FetchPaymentTimeout(Core.HydraDb.Enums.PaymentTimeoutType.SecAuth);

                _secAuthState = SecAuthState.Requested;
                _secAuthExpiryTime = DateTime.UtcNow.AddSeconds(timeout);
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetSecAuthResponse(bool response, string reference = null)
        {
            DoAction(() =>
            {
                var timedOut = HasSecAuthRequestTimedOut;
                _secAuthState = response ? (timedOut ? _secAuthState : SecAuthState.Success) : SecAuthState.Failure;

                if (timedOut)
                {
                    DoDeferredLogging(LogLevel.Info, "TimeoutOverride", () => new[] { $"{response}; Expired: {_secAuthExpiryTime:dd/MM/yyyy HH:mm:ss.fff}" });
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void PumpReserved(string reference = null)
        {
            DoAction(() =>
            {
                var timeout = _hydraDb.AdvancedConfig.PumpReserveTimeout;
                var extraSecs = _hydraDb.FetchPaymentTimeout(Core.HydraDb.Enums.PaymentTimeoutType.Mixed) + (IsInPodMode ? _hydraDb.FetchPaymentTimeout(Core.HydraDb.Enums.PaymentTimeoutType.Pod) : 0);
                _reserveExpiryTime = DateTime.UtcNow.Add(timeout).AddSeconds(extraSecs);
                IsReserved = true;
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; Expires: {_reserveExpiryTime:dd/MM/yyyy HH:mm:ss.fff}" });
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetOpt(IOpt opt)
        {
            DoAction(() =>
            {
                Opt = opt;
            }, null);
        }

        /// <inheritdoc/>
        public void SetTid(string tid)
        {
            DoAction(() =>
            {
                Tid = tid;
            }, null);
        }

        /// <inheritdoc/>
        public bool OptIsClosed { get; set; } = false;

        /// <inheritdoc/>
        public bool HydraPosCommand(CommonHydraPosCommand command, string reference = null)
        {
            // TODO:
            // var modeWorker = GetWorker< IPosIntegratorInMode<IMessageTracking>();

            bool DoHydraPosCommand()
            {
                var siteInfo = _hydraDb.AdvancedConfig;

                // ReSharper disable once SwitchStatementMissingSomeCases
                switch (command)
                {
                    case CommonHydraPosCommand.Kiosk:
                        {
                            SetKioskOnly(reference: reference);
                            return true;
                        }

                    case CommonHydraPosCommand.OptUse:
                        {
                            SetMixed(reference: reference);
                            return true;
                        }

                    case CommonHydraPosCommand.Close:
                        var result = false;
                        OptIsClosed = (siteInfo.PosType == PosType.HydraPos);
                        if (OptIsClosed && (_pumpMode == PumpMode.Mixed || _pumpMode == PumpMode.KioskUse))
                        {
                            SetKioskOnly(store: true);
                            result = true;
                        }

                        if (siteInfo.PosType != PosType.Retalix // Don't want to break Retalix behaviour
                            || !PumpIsClosed)
                        {
                            ClosePump(reference: reference);
                            result = true;
                        }
                        return result;

                    case CommonHydraPosCommand.Open:
                        if (OptIsClosed)
                        {
                            SetModeFromDefault();
                        }

                        OptIsClosed = false;
                        if (siteInfo.PosType != PosType.Retalix // Don't want to break Retalix behaviour
                            || PumpIsClosed || ClosePending)
                        {
                            OpenPump(reference: reference);
                            return true;
                        }
                        break;

                    case CommonHydraPosCommand.OutSide:
                        {
                            SetOutsideOnly(reference: reference);
                            return true;
                        }

                    case CommonHydraPosCommand.MixMode:
                        {
                            SetMixed(reference: reference);
                            return true;
                        }

                    case CommonHydraPosCommand.NightMode:
                    case CommonHydraPosCommand.EveningMode:
                        // ReSharper disable once ConvertIfStatementToSwitchStatement
                        if (_pumpMode == PumpMode.Mixed || _pumpMode == PumpMode.KioskUse)
                        {
                            SetOutsideOnly(reference: reference);
                            return true;
                        }
                        else if (_pumpMode == PumpMode.KioskOnly && !PumpIsClosed && !ClosePending)
                        {
                            ClosePump(reference: reference);
                            return true;
                        }

                        break;

                    case CommonHydraPosCommand.DayMode:
                        return DoSetModeFromDefault(reference);

                }

                return false;
            }

            return DoAction(() => Result.Success(DoHydraPosCommand()), _pumpTransaction?.GetFullId(reference)).Value;
        }

        /// <inheritdoc/>
        public void SetKioskUse(string reference = null, bool isMobile = false)
        {
            DoAction(() =>
            {
                if (CanSetKioskUse)
                {
                    _pendingMode = PumpMode.KioskUse;
                    _pendingIsMobile = isMobile;
                    if (!InUse)
                    {
                        _pumpMode = PumpMode.KioskUse;
                        _isMobile = isMobile;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetKioskOnly(bool setDefault = false, bool store = true, string reference = null)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetKioskOnly(Number, setDefault, reference?.ToMessageTracking() ?? new MessageTracking());
                }

                if (setDefault)
                {
                    _defaultMode = PumpMode.KioskOnly;
                }
                else
                {
                    _pendingMode = PumpMode.KioskOnly;
                    _pendingIsMobile = false;
                    if (!InUse)
                    {
                        _pumpMode = PumpMode.KioskOnly;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetMixed(bool setDefault = false, bool store = true, string reference = null)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetMixed(Number, setDefault, reference?.ToMessageTracking() ?? new MessageTracking());
                }

                if (setDefault)
                {
                    _defaultMode = PumpMode.Mixed;
                }
                else
                {
                    _pendingMode = PumpMode.Mixed;
                    _pendingIsMobile = false;
                    if (!InUse && !HasKioskPayment)
                    {
                        _pumpMode = PumpMode.Mixed;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetOutsideOnly(bool setDefault = false, bool store = true, string reference = null)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetOutsideOnly(Number, setDefault, reference?.ToMessageTracking() ?? new MessageTracking());
                }

                if (setDefault)
                {
                    _defaultMode = PumpMode.OutsideOnly;
                }
                else
                {
                    _pendingMode = PumpMode.OutsideOnly;
                    _pendingIsMobile = false;
                    if (!InUse)
                    {
                        _pumpMode = PumpMode.OutsideOnly;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void ThirdPartyAuth(string reference = null)
        {
            DoAction(() =>
            {
                ThirdPartyWait = false;
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void OpenPump(bool store = true, string reference = null)
        {
            DoAction(() =>
            {
                if (OptIsClosed)
                {
                    DoDeferredLogging(LogLevel.Info, HeaderPump, () => new[] { $"{Number}; Pump not Opened, as in HydraPOS Close scenario!" });
                    return;
                }

                if (store)
                {
                    _hydraDb.SetPumpClosed(Number, false);
                }

                PumpIsClosed = false;
                ClosePending = false;
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void ClosePump(bool store = true, string reference = null)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetPumpClosed(Number, true);
                }

                if (InUse)
                {
                    ClosePending = true;
                }
                else
                {
                    PumpIsClosed = true;
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void ForceClosePump(bool store = true)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetPumpClosed(Number, true);
                }

                ClosePending = false;
                PumpIsClosed = true;
            }, _pumpTransaction?.GetFullId(null));
        }

        /// <inheritdoc/>
        public void CardInserted(string reference = null)
        {
            DoAction(() =>
            {
                if (CanInsertCard)
                {
                    CardNumber = null;
                    CardProductName = null;
                    ClearedAmount = 0;
                    _hasCard = true;
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        public void SetDeliveredHose(byte hose, ushort ppu, string reference = null)
        {
            DoAction(() =>
            {
                DeliveredHose = hose;
                DeliveredPpu = ppu;
            }, _pumpTransaction?.GetFullId(reference));
        }

        public bool ResetPump(string reference = null)
        {
            bool Reset()
            {
                if (InUse)
                {
                    _hasCard = false;
                    _hasPayment = false;
                    ThirdPartyWait = false;
                    _paymentToClear = false;
                    IsDelivering = false;
                    _secAuthState = SecAuthState.Idle;
                    _hasDeliveryResponsePending = false;
                    _pendingIsMobile = false;

                    ApplyPendingMode();
                    return true;
                }

                return false;
            }

            return DoAction(() => Result.Success(Reset()), _pumpTransaction?.GetFullId(reference)).Value;
        }

        /// <inheritdoc/>
        public void SetOptPayment(string reference = null)
        {
            DoAction(() =>
            {
                _hasPayment = true;
                _paymentToClear = true;
                _pumpOptPayment = true;
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetPaid(bool paid, string reference = null)
        {
            DoAction(() =>
            {
                IsPaid = paid;
                if (paid)
                {
                    HasKioskPayment = false;
                }
                else if (!_pumpOptPayment)
                {
                    if (_pumpMode == PumpMode.KioskOnly || _pumpMode == PumpMode.KioskUse)
                    {
                        HasKioskPayment = true;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void Authorised(string reference = null)
        {
            DoAction(() =>
            {
                if (!_hasPayment)
                {
                    if (!IsPaid)
                    {
                        _pumpOptPayment = false;
                    }

                    if (_pumpMode == PumpMode.KioskOnly || _pumpMode == PumpMode.KioskUse)
                    {
                        HasKioskPayment = true;
                    }
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetMaxFillOverrideForFuelCards(bool flag, bool store = true)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetPumpMaxFillOverrideForFuelCards(Number, flag);
                }

                MaxFillOverrideForFuelCards = flag;
            }, _pumpTransaction?.GetFullId(null));
        }

        /// <inheritdoc/>
        public void SetMaxFillOverrideForPaymentCards(bool flag, bool store = true)
        {
            DoAction(() =>
            {
                if (store)
                {
                    _hydraDb.SetPumpMaxFillOverrideForPaymentCards(Number, flag);
                }

                MaxFillOverrideForPaymentCards = flag;
            }, _pumpTransaction?.GetFullId(null));
        }

        /// <inheritdoc/>
        public void SetAllGrades(IList<byte> allGrades, string reference = null)
        {
            DoAction(() =>
            {
                AllGrades.Clear();
                foreach (byte grade in allGrades)
                {
                    AllGrades.Add(grade);
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void SetUnmannedPseudoPos(bool isOn)
        {
            DoAction(() =>
            {
                _unmanned = isOn;
            }, _pumpTransaction?.GetFullId(null));
        }

        /// <inheritdoc/>
        public void LogTransactionState(PumpState incomingState, bool useOpt, bool tellOpt, bool isKioskOnly, uint volume, uint amount, string reference)
        {
            DoAction(() =>
            {
                if (_pumpTransaction == null)
                {
                    _pumpTransaction = new PumpTransaction(Number);
                }

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), $"PumpState.{incomingState}.Received.{HeaderPump}", () => new[] { $"{Number}" });

                var state = incomingState;
                if (!_pumpTransaction.TransactionStates.ContainsKey(state))
                {
                    _pumpTransaction.TransactionStates[state] = new PumpTransactionState(state, _pumpTransaction.CurrentUtcTime);
                }

                foreach (var ts in _pumpTransaction.TransactionStates.Where(x => x.Value == null))
                {
                    _pumpTransaction.TransactionStates[ts.Key] = new PumpTransactionState(ts.Key, _pumpTransaction.CurrentUtcTime);
                }

                _pumpTransaction.LastPumpState = state;
                var transState = _pumpTransaction.TransactionStates[state];
                transState.LastEntered = _pumpTransaction.CurrentUtcTime;
                transState.Count++;
                transState.UseOpt = useOpt;
                transState.TellOpt = tellOpt;
                transState.IsKioskOnly = isKioskOnly;
                transState.Volume = volume;
                transState.Amount = amount;
                transState.Reference = reference;

                // Catch-all for starting a new transaction
                if ((incomingState == CommonPumpState.Request || incomingState == CommonPumpState.Authorise || incomingState == CommonPumpState.Delivering) &&
                    _pumpTransaction.TransactionStates.ContainsKey(CommonPumpState.Idle))
                {
                    _pumpTransaction.TransactionStates.Remove(CommonPumpState.Idle);
                    _pumpTransaction.SetActionsComplete(false);
                }
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void LogTransactionState(PaymentResult paymentResult, string reference, bool success = true)
        {
            DoAction(() =>
            {
                var payment = (Forecourt.Core.Payment.Enums.PaymentResult)Enum.Parse(typeof(Forecourt.Core.Payment.Enums.PaymentResult), $"{paymentResult}");
                if (_pumpTransaction == null)
                {
                    _pumpTransaction = new PumpTransaction(Number);
                }

                if (_pumpTransaction.PaymentStates.ContainsKey(payment))
                {
                    DoDeferredLogging(LogLevel.Info, $"PaymentState.AlreadyRecorded.{paymentResult}.{HeaderPump}", () => new[] { $"{Number}" });
                    return;
                }

                DoDeferredLogging(LogLevel.Info, $"PaymentState.Received.{paymentResult}.{HeaderPump}", () => new[] { $"{Number}" });

                _pumpTransaction.PaymentStates[payment] = new PumpTransactionPaymentState
                {
                    PaymentResult = payment,
                    Success = success,
                    WhenProcessed = _pumpTransaction.CurrentUtcTime,
                    Reference = reference
                };
            }, _pumpTransaction?.GetFullId(reference));
        }

        /// <inheritdoc/>
        public void ResetTransactionState(string reference)
        {
            DoDeferredLogging(LogLevel.Info, $"{HeaderPump}", () => new[] { $"{Number}" }, reference: _pumpTransaction.GetFullId(reference));
            _pumpTransaction = new PumpTransaction(Number);
        }

        private bool DoSetModeFromDefault(string reference, bool ignoreMixedModeCheck = true)
        {
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; ignoreMixedModeCheck: {ignoreMixedModeCheck}; _pumpMode: {_pumpMode}; _defaultMode: {_defaultMode} " });

            if (_pumpMode != _defaultMode)
            {
                switch (_defaultMode)
                {
                    case PumpMode.KioskOnly:
                        SetKioskOnly(reference: reference);
                        break;
                    case PumpMode.Mixed:
                        if (ignoreMixedModeCheck || _pumpMode != PumpMode.KioskOnly)
                        {
                            SetMixed(reference: reference);
                        }
                        break;
                    case PumpMode.OutsideOnly:
                        SetOutsideOnly(reference: reference);
                        break;
                    default:
                        return false;
                }

                return true;
            }

            return false;
        }

        /// <inheritdoc cref="IPump"/>
        public bool SetModeFromDefault(string reference = null, bool ignoreMixedModeCheck = true)
        {
            return DoAction(() =>
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), HeaderPump, () => new[] { $"{Number}; InUse: {InUse}; !PumpIsClosed && (HasPayment || IsDelivering || _paymentToClear || _hasCard): {!PumpIsClosed} && ({HasPayment} || {IsDelivering} || {_paymentToClear} || {_hasCard});" });

                if (InUse)
                {
                    return Result.Success((false));
                }

                var siteInfo = _hydraDb.AdvancedConfig;

                return Result.Success(DoSetModeFromDefault(reference, siteInfo.PosType != PosType.HydraPos || ignoreMixedModeCheck));

            }, reference).Value;
        }

        /// <inheritdoc/>
        public bool HasPayAtKioskTimeoutExpired()
        {
            return PayAtKioskExpiryTime < DateTime.Now;
        }

        /// <inheritdoc/>
        public void StartPayAtKioskPressedTimer(int timeout)
        {
            PayAtKioskExpiryTime = DateTime.Now.AddSeconds(timeout);
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "PayAtKioskTimer.Expires", () => new[] { $"{PayAtKioskExpiryTime:dd/MM/yyyy HH:mm:ss.fff}" });
        }

        /// <inheritdoc/>
        public void ClearPayAtKioskPressedTimer()
        {
            PayAtKioskExpiryTime = DateTime.MaxValue;
        }

        public void PaymentApprovedFailed(string reference)
        {
            _authoriseSent = false;
        }

        /// <inheritdoc/>
        public PumpStateType PreviousPumpState => _previousPumpState;

        /// <inheritdoc/>
        public PumpStateType SetPreviousPumpState()
        {
            _previousPumpState = PumpState;
            return _previousPumpState;
        }

        /// <inheritdoc/>
        public bool IsLastPumpOnOpt => Opt?.PumpList().OrderBy(x => x).Last() == Number;
    }
}
