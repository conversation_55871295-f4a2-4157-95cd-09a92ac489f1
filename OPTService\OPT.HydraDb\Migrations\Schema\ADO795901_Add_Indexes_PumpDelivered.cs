﻿using FluentMigrator;
using Htec.DapperWrapper.Interfaces;
using Microsoft.Extensions.Logging;
using OPT.HydraDb.Common;
using System.IO.Abstractions;

namespace OPT.HydraDb.Migrations.Programmatic
{
    /// <inheritdoc/>
    [Migration(28200002, "ADO#795901 - Add indexes to dbo.PumpDelivered"), Tags(Constants.MigrationTypeSchema)]
    public class ADO795901_Add_Indexes_PumpDelivered : Migrationable
    {
        private const string TableName = "PumpDelivered";
        private const string IndexNamePrefix = "uix_" + TableName + "_";

        /// <inheritdoc/>
        public ADO795901_Add_Indexes_PumpDelivered(ILogger<Migrationable> logger, IDbExecutorFactory dbExecutorFactory = null, IFileSystem fileSystem = null) : base(logger, dbExecutorFactory, fileSystem)
        {
        }

        /// <inheritdoc/>
        public override void Up()
        {
            var indexName = IndexNamePrefix + "number";
            if (!Schema.Table(TableName).Index(indexName).Exists())
            {
                Create.Index(indexName).OnTable(TableName)
                    .OnColumn("Number").Ascending()
                    .WithOptions().Unique()
                    .WithOptions().NonClustered();
            }
        }

        /// <inheritdoc/>
        public override void Down()
        {
            var indexName = IndexNamePrefix + "number";
            if (Schema.Table(TableName).Index(indexName).Exists())
            {
                Delete.Index(indexName).OnTable(TableName);
            }      
        }
    }
}

