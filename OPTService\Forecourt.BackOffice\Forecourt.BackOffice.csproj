﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net48;net6.0;net8.0</TargetFrameworks>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<LangVersion>latest</LangVersion>
		<Platforms>AnyCPU;x64</Platforms>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Htec.Hydra.Core.Bos" Version="1.2.0-alpha0008" />
		<PackageReference Include="CsvHelper" Version="[30.0.1,)" />
		<PackageReference Include="Htec.Foundation.Connections" Version="[5.0.0,)" />
		<PackageReference Include="Htec.Hydra.Opt.Common" Version="[2.4.0,)" />
	</ItemGroup>
	
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNet.SignalR.Core" Version="[2.4.3,)" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Forecourt.Core\Forecourt.Core.csproj" />
	</ItemGroup>
	
	<ItemGroup>
		<InternalsVisibleTo Include="Forecourt.Common.Tests" />
		<InternalsVisibleTo Include="Forecourt.Common.Integration.Tests" />
	</ItemGroup>

</Project>
