﻿using CSharpFunctionalExtensions;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.TransactionFiles.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Attributes;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Hydra.Core.Bos.Messages.Hydra;
using Htec.Logger.Interfaces.Tracing;
using System;
using System.Collections.Generic;
using System.IO.Abstractions;
using System.Linq;
using System.Net;

namespace Forecourt.Bos.TransactionFiles
{
    /// <inheritdoc />
    [HasConfiguration()]
    public class HydraTransactionFile : GenericTransactionFile, IHydraTransactionFile
    {
        private const string TransactionName = "TR";
        private const string DayEndName = "DE";
        private const string ShiftSummaryName = "SM";
        private const string SalesItemName = "SI";
        private const string CardSalesItemName = "CA";
        private const string CategorySalesItemName = "SC";
        private const string CardVolumeSalesName = "CV";
        private const string DayFlag = "D";
        private const string ShiftFlag = "S";
        private const string InvoiceName = "IV";

        private readonly ConfigurableBool _isTrFileCleaned;

        /// <summary>
        /// Config key to handle removal of duplicate lines from TR file output.
        /// </summary>
        public const string ConfigKeyCleanTrFiles = "CleanTrFiles";

        /// <summary>
        /// Config key to handle removal of duplicate lines from TR file output.
        /// </summary>
        public const bool DefaultValueCleanTrFiles = true;

        /// <inheritdoc />
        public HydraTransactionFile(IHydraDb hydraDb, IHtecLogger logger, IFileSystem fileSystem, IConfigurationManager configurationManager)
            : base(hydraDb, logger, fileSystem, configurationManager)
        {            
            SetFileDirectory(AllFileLocations?.TransactionFileDirectory);

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }

            _isTrFileCleaned = new ConfigurableBool(this, ConfigKeyCleanTrFiles, DefaultValueCleanTrFiles);
        }

        /// <inheritdoc />
        protected override void DoSetFileDirectoryAction(string directory)
        {
            HydraDb.SetTransactionFileDirectory(directory);
        }

        /// <inheritdoc />
        public void WriteTransactionFile(IEnumerable<TransactionFileItem> items, DateTime dateTime, IEnumerable<IPAddress> devices, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                if (!items.Any())
                {
                    return;
                }

                var localItems = RemoveDuplicateProductLines(items.ToArray());

                var transactionFileName = CreateTransactionFileName(localItems[0].Till, dateTime);

                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Number", () => new[] {$"{localItems[0].Receipt}; File: {transactionFileName}"});

                WriteItemsToFile(localItems, transactionFileName, "transaction");
            }, message.FullId);
        }

        /// <inheritdoc />
        public void WriteLocalAccountInvoiceFile(IEnumerable<LocalAccountTransactionItem> localAccountItems, short till, DateTime dateTime, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                if (!localAccountItems.Any())
                {
                    return;
                }

                var fileName = CreateFileName(InvoiceName, till.ToString(), dateTime);

                DoDeferredLogging(LogLevel.Info, "Card", () => new[] { $"{localAccountItems.First().CardNumber}; File: {fileName}" });

                WriteItemsToFile(localAccountItems, fileName, "invoice");
            }, message.FullId);
        }

        internal TransactionFileItem[] RemoveDuplicateProductLines(TransactionFileItem[] items)
        {
            return DoAction(() =>
            {
                if (!_isTrFileCleaned.GetValue())
                {
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Number", () => new[] {$"{items[0].Receipt}; will not be checked for duplicate lines"});
                    return Result.Success(items);
                }

                var distinctItems = items.Distinct().ToList();
                var duplicatedLines = items.ToList().Count - distinctItems.Count;
                if (duplicatedLines == 0)
                {
                    return Result.Success(items);
                }

                DoDeferredLogging(LogLevel.Warn, "Number", () => new[] {$"{items[0].Receipt}; Removing {duplicatedLines}"});
                
                return Result.Success(distinctItems.ToArray());
            }, LoggingReference).Value;
        }

        internal string CreateTransactionFileName(string tillNumber, DateTime timeStamp)
        {
            return CreateFileName(TransactionName, tillNumber, timeStamp);
        }

        /// <inheritdoc />
        public void WriteShiftEnd(DayEndItem item, bool isDayEnd, string loggingReference = null)
        {
            DoAction(() =>
            {
                WriteItemsToFile(new[] {item}, CreateShiftSummaryFileName(item.Till, GetNow(), isDayEnd), "shift summary");
            }, loggingReference);
        }

        internal string CreateShiftSummaryFileName(string tillNumber, DateTime timeStamp, bool isDayEnd)
        {
            return CreateFileName(isDayEnd ? DayEndName : ShiftSummaryName, tillNumber, timeStamp, GetFlag(isDayEnd));
        }

        /// <inheritdoc />
        public void WriteSalesItems(ItemSalesItem[] items, bool isDayEnd, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (!items.Any())
                {
                    DoDeferredLogging(LogLevel.Info, "NoItemsFound");
                    return;
                }

                WriteItemsToFile(items, CreateSalesItemFileName(items[0].TillNumber(), GetNow(), isDayEnd), "itemSales item");
            }, loggingReference);
        }

        internal string CreateSalesItemFileName(string tillNumber, DateTime timeStamp, bool isDayEnd)
        {
            return CreateFileName(SalesItemName, tillNumber, timeStamp, GetFlag(isDayEnd));
        }

        /// <inheritdoc />
        public void WriteCardSalesItems(CardSalesItem[] items, bool isDayEnd, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (!items.Any())
                {
                    DoDeferredLogging(LogLevel.Info, "NoItemsFound");
                    return;
                }

                WriteItemsToFile(items, CreateCardSalesItemFileName(items[0].TillNumber(), GetNow(), isDayEnd), "card itemSales item");

            }, loggingReference);
        }

        internal string CreateCardSalesItemFileName(string tillNumber, DateTime timeStamp, bool isDayEnd)
        {
            return CreateFileName(CardSalesItemName, tillNumber, timeStamp, GetFlag(isDayEnd));
        }

        /// <inheritdoc />
        public void WriteCategorySalesItems(CategorySalesItem[] items, bool isDayEnd, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (!items.Any())
                {
                    return;
                }

                WriteItemsToFile(items, CreateCategorySalesItemFileName(items[0].TillNumber(), GetNow(), isDayEnd), "category itemSales item");

            }, loggingReference);
        }

        internal string CreateCategorySalesItemFileName(string tillNumber, DateTime timeStamp, bool isDayEnd)
        {
            return CreateFileName(CategorySalesItemName, tillNumber, timeStamp, GetFlag(isDayEnd));
        }

        private static string GetFlag(bool isDayEnd)
        {
            return isDayEnd ? DayFlag : ShiftFlag;
        }
        
        /// <inheritdoc />
        public void WriteShiftEndFiles(DayEndItem endItem, bool isDayEnd, IEnumerable<ItemSalesItem> itemSales, IEnumerable<CardAmountItem> cardAmounts, IEnumerable<CardSalesItem> cardSales, IEnumerable<CategorySalesItem> categorySales, IEnumerable<CardVolumeSalesItem> cardVolumeSales, IMessageTracking message = null)
        {
            message ??= new MessageTracking();
            DoAction(() =>
            {
                var logRef = message.FullId;
                WriteShiftEnd(endItem, isDayEnd, logRef);
                WriteSalesItems(itemSales.ToArray(), isDayEnd, logRef);
                WriteCardSalesItems(cardSales.ToArray(), isDayEnd, logRef);
                WriteCardVolumeItems(endItem.Till, cardVolumeSales.ToArray(), isDayEnd, logRef);
                WriteCategorySalesItems(categorySales.ToArray(), isDayEnd, logRef);
                WriteCardAmountItems(cardAmounts.ToArray(), isDayEnd, logRef);

                return Result.Success();
            }, message.FullId);
        }

        /// <inheritdoc />
        public void WriteCardVolumeItems(string tillNumber, CardVolumeSalesItem[] items, bool isDayEnd, string loggingReference = null)
        {
            DoAction(() =>
            {
                if (!items.Any())
                {
                    return;
                }

                WriteItemsToFile(items, CreateCardVolumeSalesItemFileName(tillNumber, GetNow(), isDayEnd), "card volume itemSales");

            }, loggingReference);
        }

        /// <inheritdoc />
        public void WriteCardAmountItems(CardAmountItem[] items, bool options, string reference = null)
        {
            // Do nothing
        }

        internal string CreateCardVolumeSalesItemFileName(string tillNumber, DateTime timeStamp, bool isDayEnd)
        {
            return CreateFileName(CardVolumeSalesName, tillNumber, timeStamp, GetFlag(isDayEnd));
        }

        /// <inheritdoc />
        public void SendTransaction(TransactionFileItem transaction, out IEnumerable<IPAddress> devices, IMessageTracking message = null)
        {
            devices = null;
            // Do nothing
        }

        /// <inheritdoc />
        public Result MoveOfflineTransactionFiles(IPAddress ipAddress, string logginReference = null)
        {
            // Do nothing
            return Result.Success();
        }
    }
}
