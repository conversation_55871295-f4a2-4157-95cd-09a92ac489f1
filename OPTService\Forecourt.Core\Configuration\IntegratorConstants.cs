﻿using Forecourt.Core.HydraDb.Enums;
using Forecourt.Core.Pos.Enums;

namespace Forecourt.Core.Configuration
{
    /// <summary>
    /// All constants related to Configuration
    /// </summary>
    public static partial class Constants
    {
        /// <summary>
        /// All constants related to Integrator configuration
        /// </summary>
        public static class Integrator
        {
            /// <summary>
            /// Config key for, Integrators prefix
            /// </summary>
            public const string ConfigKeyIntegratorPrefix = "INTEGRATORS:";

            /// <summary>
            /// Config key for, Integrators POS prefix
            /// </summary>
            public const string ConfigKeyIntegratorPosPrefix = ConfigKeyIntegratorPrefix + "POS:";

            /// <summary>
            /// Config key for, Pos (mobile) integrator
            /// </summary>
            public const string ConfigKeyIntegratorPosMobile = ConfigKeyIntegratorPosPrefix + "MOBILE";

            /// <summary>
            /// HydraMobile Pos (mobile) type for Orbis
            /// </summary>
            public const string PosMobileTypeHydraMobile = "HYDRAMOBILE";

            /// <summary>
            /// HydraMobile Pos (mobile) type for Status Only
            /// </summary>
            public const string PosMobileTypeHydraMobileStatus = "HYDRAMOBILE:STATUS";

            /// <summary>
            /// Config key for, Site integrator
            /// </summary>
            public const string ConfigKeyIntegratorSite = nameof(SiteType);

            /// <summary>
            /// Config key for, Pos integrator
            /// </summary>
            public const string ConfigKeyIntegratorPos = nameof(PosType);

            /// <summary>
            /// Config key for, Pump integrator
            /// </summary>
            public const string ConfigKeyIntegratorPump = ConfigKeyIntegratorPrefix + "PUMP";

            /// <summary>
            /// HSC PumpType
            /// </summary>
            public const string PumpTypeHsc = "HSC";

            /// <summary>
            /// DOMS PumpType
            /// </summary>
            public const string PumpTypeDoms = "DOMS";            

            /// <summary>
            /// Config key for, Integrators Payments prefix
            /// </summary>
            public const string ConfigKeyIntegratorPaymentPrefix = ConfigKeyIntegratorPrefix + "PAYMENT:";

            /// <summary>
            /// Config key for, Mobile Payments integrator
            /// </summary>
            public const string ConfigKeyIntegratorPaymentMobile = ConfigKeyIntegratorPaymentPrefix + "MOBILE";

            /// <summary>
            /// HydraMobile Payment (mobile) type
            /// </summary>
            public const string PaymentMobileTypeHydraMobile = "HYDRAMOBILE";

            /// <summary>
            /// Config key for, BosType integrator
            /// </summary>
            public const string ConfigKeyIntegratorBos = ConfigKeyIntegratorPrefix + "BOS";

            /// <summary>
            /// Config key for, Integrators Secondary Authorisation prefix
            /// </summary>
            public const string ConfigKeyIntegratorSecAuth = ConfigKeyIntegratorPrefix + "SEC-AUTH:";

            /// <summary>
            /// Hydra ANPR SecAuth Type
            /// </summary>
            public const string SecAuthTypeHydra = "ANPR";

            /// <summary>
            /// Config key for, Secondary Auth, Hydra/ANPR
            /// </summary>
            public const string ConfigKeyIntegratorSecAuthHydra = ConfigKeyIntegratorSecAuth + SecAuthTypeHydra;

            /// <summary>
            /// Arbitrary SignalR SecAuth Type
            /// </summary>
            public const string SecAuthTypeSignalRApi = "SIGNALR-API";

            /// <summary>
            /// Arbitrary SignalR SecAuth Type, PreAuth
            /// </summary>
            public const string SecAuthTypeSignalRApiPre = SecAuthTypeSignalRApi + ":PRE";

            /// <summary>
            /// Arbitrary SignalR SecAuth Type, PostAuth
            /// </summary>
            public const string SecAuthTypeSignalRApiPost = SecAuthTypeSignalRApi + ":POST";

            /// <summary>
            /// Config key for, Secondary Auth, SignalR, In=API, for PreAuth requests
            /// </summary>
            public const string ConfigKeyIntegratorSecAuthSignalRApiPre = ConfigKeyIntegratorSecAuth + SecAuthTypeSignalRApiPre;

            /// <summary>
            /// Config key for, Secondary Auth, SignalR, In=API, for PostAuth requests
            /// </summary>
            public const string ConfigKeyIntegratorSecAuthSignalRApiPost = ConfigKeyIntegratorSecAuth + SecAuthTypeSignalRApiPost;

            /// <summary>
            /// Config key for, Integrators Payment Configuration prefix
            /// </summary>
            public const string ConfigKeyIntegratorPaymentConfig = ConfigKeyIntegratorPrefix + "PAYMENT-CONFIG";

            /// <summary>
            /// eSocketPOS PaymentConfig Type
            /// </summary>
            public const string PaymentConfigTypeeSocket = "ESOCKET";

            /// <summary>
            /// Config key for, Payment Config, eSocket Pos (SqlServer)
            /// </summary>
            public const string PaymenConfigeSocketSqlServer = PaymentConfigTypeeSocket + ":SQLSERVER";

            /// <summary>
            /// Config key for, Payment Config, eSocket Pos (HSqlDb)
            /// </summary>
            public const string PaymenConfigeSocketHSqlDb = PaymentConfigTypeeSocket + ":HSQLDB";
        }
    }
}
