<form [formGroup]="optForm">
  <div *ngIf="opt.MediaChannel">
    <app-label-text-button labelText="Playlist" controlName="PlaylistFileName" id="playlistFileNameInput"
                           [errorInAction]="setPlaylistFileNameError" buttonText="Set playlist" (action)="setPlaylistFileName($event)"
                           [formGroup]="optForm" labelColClass="col-sm-12 col-md-3 col-lg-3 col-xl-3"
                           textColClass="col-sm-12 col-md-6 col-xl-5" buttonColClass="col-sm-12 col-md-3 col-xl-4" maxlength="2048">
    </app-label-text-button>
  </div>

  <table class="table">
    <tbody>
      <tr>
        <td>
          <table class="table table-bordered table-opt-versions">
            <tbody>
              <tr>
                <th scope="row">Software version</th>
                <td id="softwareVersion">{{opt.SoftwareVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Secure assets version</th>
                <td id="secureAssetsVersion">{{opt.SecureAssetsVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Multimedia assets version</th>
                <td id="multimediaAssetsVersion">{{opt.MultimediaAssetsVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">CPAT assets version</th>
                <td id="cpatAssetsVersion">{{opt.CpatAssetsVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">OPT firmware version</th>
                <td id="optFirmwareVersion">{{opt.OptFirmwareVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">EMV Kernel version</th>
                <td id="emvKernelVersion">{{opt.EmvKernelVersion || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Plugin type</th>
                <td id="pluginType">{{opt.PluginType || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Plugin version</th>
                <td id="pluginVersion">{{opt.PluginVersion || '-'}}</td>
              </tr>
            </tbody>
          </table>
        </td>
        <td>
          <table class="table table-bordered table-opt-versions">
            <tbody>
              <tr>
                <th scope="row">IP address</th>
                <td id="ipAddress">{{opt.IpAddress || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Subnet</th>
                <td id="subnet">{{opt.Subnet || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Gateway</th>
                <td id="gateway">{{opt.Gateway || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Dns 1</th>
                <td id="dns1">{{opt.Dns1 || '-'}}</td>
              </tr>
              <tr>
                <th scope="row">Dns 2</th>
                <td id="dns2">{{opt.Dns2 || '-'}}</td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
  <ng-container *ngIf="opt.Pumps.length > 0">
    <div class="row">
      <h5 class="col-12">Pump List</h5>
    </div>
    <div class="row">
      <div class="col-12">
        <app-pumps [parentOpt]="opt.StringId" [pumpsData]="opt.Pumps" [opts]="opts" [tids]="tids" [maxFillOverride]="maxFillOverride" [pumpCardStates]="pumpCardStates" (pumpCardStatesChange)="updateCardStates($event)"></app-pumps>
      </div>
    </div>
  </ng-container>
  <div class="row">
    <h5 class="col-12">Receipts</h5>
  </div>
  <div class="row">
    <div class="col-12">
      <div class="card card-receipt mt-2">
        <div class="card-header">
          <div class="row">
            <div class="col-6 text-left">
              <span>Header and Footer</span>
            </div>
            <div class="col-6 text-right">
              <a (click)="receiptCollapse.toggle()" class="header-button">
                <i class="bi" [ngClass]="{'bi-chevron-down':receiptIsCollapsed,'bi-chevron-up':!receiptIsCollapsed }"></i>
              </a>
            </div>
          </div>
        </div>
        <div class="card-body" #receiptCollapse="ngbCollapse" [(ngbCollapse)]="receiptIsCollapsed">
          <app-opt-receipt [opt]="opt" [formGroup]="optForm" headerArrayName="ReceiptHeaders" footerArrayName="ReceiptFooters"></app-opt-receipt>
        </div>
      </div>
    </div>
  </div>
</form>
