﻿using Forecourt.Core.HydraDb.Models;
using Htec.Common.Abstractions.Net.Http.Interfaces;
using Htec.Hydra.Core.Bos.Configuration.EvoBook;

namespace Forecourt.Bos.Configuration.EvoBook
{
    /// <summary>
    /// MADIC version of the <see cref="HydraToExternalMapping{TExternalGrade, TExternalProduct, TExternalCurrency, TExternalVat, TExternalCardReference}"/> model
    /// </summary>
    public class HydraToEvoBookMapping : HydraToExternalMapping<Grade, Product, Currency, TaxLevel, Tender>
    {
        public string StepName { get; set; }

        /// <inheritdoc />
        public EndPointsConfig EndPointsConfig { get; set; }

        /// <inheritdoc />
        public BosConfig BosConfig { get; set; }      

        /// <summary>
        /// Http client instance for access Configuration API
        /// </summary>
        public IHttpClientWrapper ConfigHttpClient { get; set; }
    }
}
