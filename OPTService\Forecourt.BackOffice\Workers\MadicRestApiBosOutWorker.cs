﻿using CSharpFunctionalExtensions;
using Forecourt.BackOffice.Orchestrations.Interfaces;
using Forecourt.BackOffice.Workers.Interfaces;
using Forecourt.Bos.Configuration.EvoBook;
using Forecourt.Bos.HydraDb.Interfaces;
using Forecourt.Bos.Orchestrations.Interfaces;
using Forecourt.Core.Configuration;
using Forecourt.Core.Helpers.Interfaces;
using Htec.Common.Abstractions.Configuration.Interfaces;
using Htec.Foundation.Connections.Models;
using Htec.Foundation.Connections.Models.Interfaces;
using Htec.Foundation.Core;
using Htec.Foundation.Models;
using Htec.Foundation.Models.Interfaces;
using Htec.Hydra.Core.Bos.Messages;
using Htec.Logger.Interfaces;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Forecourt.BackOffice.Workers
{
    /// <summary>
    /// Any and all capabilities of the Madic RestAPI back-office systems (Bos)
    /// </summary>
    public class MadicRestApiBosOutWorker : RestApiBosOutWorker, IMadicRestApiBosOutWorker
    {
        private readonly object _mapConfigLock = new object();
        private readonly ICacheHelper _cacheHelper;
        private readonly IBookTransactionOrchestration _bookTransactionOrchestration;
        private readonly IMapConfigurationOrchestration<ILogTracking> _mapConfigOrchestration;

        /// <inheritdoc/>
        public MadicRestApiBosOutWorker(IHtecLogManager logManager, IHydraDb hydraDb, IBookTransactionOrchestration bookTransactionOrchestration, ICacheHelper cacheHelper,
            IMapConfigurationOrchestration<ILogTracking> mapConfigOrchestration, IConfigurationManager configurationManager) :
            base(logManager, nameof(MadicRestApiBosOutWorker), hydraDb, configurationManager)
        {
            _bookTransactionOrchestration = bookTransactionOrchestration ?? throw new ArgumentNullException(nameof(bookTransactionOrchestration));
            _cacheHelper = cacheHelper ?? throw new ArgumentNullException(nameof(cacheHelper));
            _mapConfigOrchestration = mapConfigOrchestration ?? throw new ArgumentNullException(nameof(mapConfigOrchestration));

            if (configurationManager == null)
            {
                throw new ArgumentNullException(nameof(configurationManager));
            }
        }

        /// <inheritdoc/>
        protected override Result DoSendTransaction(SendTransactionItem sendTransaction, IMessageTracking message = null)
        {
            message ??= new MessageTracking();

            Task.Run(() => { DoAction<StatusCodeResult>(() => _bookTransactionOrchestration.RunOrchestration(sendTransaction, ConfigDataMap, message).Result, message.FullId); });

            return Result.Success();
        }

        /// <summary>
        /// Get the cached <see cref="HydraToEvoBookMapping"/> instance
        /// </summary>
        protected HydraToEvoBookMapping ConfigDataMap
        {
            get
            {
                lock (_mapConfigLock)
                {
                    HydraToEvoBookMapping newModel = null;
                    var isValid = _cacheHelper.IsCachedItemValid($"{Constants.ConfigKeySuffixHydraDb}{Constants.CategoryNameBOS}:", Constants.CachedItemHydraToExternalConfigDataMap);
                    DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "ConfigDataMap.CacheStatus", () => new[] { $"{(isValid ? string.Empty : "In")}Valid" });

                    if (!isValid)
                    {
                        var result = _mapConfigOrchestration.RunOrchestration();

                        newModel = result.IsSuccess ? ((StatusCodeResult<HydraToEvoBookMapping>)result).Results : new HydraToEvoBookMapping();                        
                        DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "ConfigDataMap.Result", () => new[] { $"{result.IsSuccess}" });
                    }

                    return _cacheHelper.GetCachedItem($"{Constants.ConfigKeySuffixHydraDb}{Constants.CategoryNameBOS}:", Constants.CachedItemHydraToExternalConfigDataMap, () => newModel);
                }
            }
        }

        /// <inheritdoc/>
        protected override Result DoStart(params object[] startParams)
        {
            var dataMap = ConfigDataMap;

            var result = base.DoStart(startParams);

            DoBackgroundTask(() => ProcessPendingTransactions(), null, null, TimeSpan.FromSeconds(30).Milliseconds, nameof(ProcessPendingTransactions));

            return result;
        }

        private Result ProcessPendingTransactions()
        {
            var message = new MessageTracking();

            var resultPending = HydraDb.GetPendingTransactionBookings(message);
            if (!resultPending.IsSuccess)
            {
                DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Count", () => new[] { "0" }, reference: message.IdAsString);
                return Result.Failure(resultPending.Error);
            }

            var bookings = resultPending.Value;
            var bosConfig = ConfigDataMap?.BosConfig ?? HydraDb.BosConfig;
            DoDeferredLogging(ToLogLevel(DeveloperLoggingLevelState.GetValue()), "Count", () => new[] 
            { 
                $"{bookings.Count()}", 
                bookings.Count() <= bosConfig.BookTransactionMaxPending ? string.Empty : $"; Processing first: {bosConfig.BookTransactionMaxPending}"
            }, reference: message.IdAsString);

            foreach (var booking in bookings.Take(bosConfig.BookTransactionMaxPending))
            {
                if (booking.SendTransactionItem == null)
                {
                    var expiry = booking.CreatedDate.ToUniversalTime().AddTicks(bosConfig.BookTransactionCreationInterval.Ticks);
                    var info = $"TransId: {booking.TransactionId}; TxnNumber: {booking.TxnNumber}; Timestamp: {booking.TransactionDate.ToUniversalTime():dd/MM/yyyy HH:mm:ss.fff};" + 
                        $" CreatedDate: {booking.CreatedDate.ToUniversalTime():dd/MM/yyyy HH:mm:ss.fff}; CreateIntervalExpires: {expiry:dd/MM/yyyy HH:mm:ss.fff}";

                    if (expiry >= DateTime.UtcNow)
                    {
                        DoDeferredLogging(LogLevel.Info, "Pseudo.Invalid.Transaction.Booked", () => new[]{ info });
                    }
                    else
                    {
                        var result = HydraDb.CompleteTransactionBooking(booking.Id, booking.TransactionId, DateTime.UtcNow);
                        if (result.IsSuccess)
                        {
                            DoDeferredLogging(LogLevel.Info, "Invalid.Transaction.Pseudo.Booked", () => new[] { info });
                        }
                        else
                        {
                            DoDeferredLogging(LogLevel.Warn, "Invalid.Transaction.Pseudo.Booked.Failure", () => new[] { info});
                        }
                    }

                    continue;
                }

                var item = JsonConvert.DeserializeObject<SendTransactionItem>(booking.SendTransactionItem);

                Task.Run(() => DoActionAsync<StatusCodeResult>(async () => 
                    await _bookTransactionOrchestration.RunOrchestration(item, ConfigDataMap, null), 
                    message.FullId).ConfigureAwait(false));
            }

            return Result.Success();
        }
    }
}
